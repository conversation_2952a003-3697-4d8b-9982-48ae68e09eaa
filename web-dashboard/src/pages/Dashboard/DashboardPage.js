import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Divider,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Security as SecurityIcon,
  Devices as DevicesIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { Helmet } from 'react-helmet-async';

// Components
import StatCard from '../../components/Dashboard/StatCard';
import ChartCard from '../../components/Dashboard/ChartCard';
import RecentActivity from '../../components/Dashboard/RecentActivity';
import SecurityOverview from '../../components/Dashboard/SecurityOverview';
import DeviceStatus from '../../components/Dashboard/DeviceStatus';
import AIInsights from '../../components/Dashboard/AIInsights';

// Services
import { dashboardService } from '../../services/dashboardService';
import { useAuthStore } from '../../store/authStore';

const DashboardPage = () => {
  const { user } = useAuthStore();
  const [refreshing, setRefreshing] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  // Fetch dashboard data
  const { data: dashboardData, isLoading, refetch } = useQuery(
    'dashboard',
    dashboardService.getDashboardData,
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const stats = dashboardData?.stats || {
    totalUsers: 0,
    activeScans: 0,
    devicesMonitored: 0,
    threatsBlocked: 0,
    networkHealth: 0,
    systemUptime: 0,
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <>
      <Helmet>
        <title>Dashboard - SecureNet Pro</title>
        <meta name="description" content="SecureNet Pro Dashboard - Network Security Analytics and Management" />
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Welcome back, {user?.firstName || user?.username}! 👋
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Here's what's happening with your network security today.
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={refreshing ? <RefreshIcon sx={{ animation: 'spin 1s linear infinite' }} /> : <RefreshIcon />}
                onClick={handleRefresh}
                disabled={refreshing}
              >
                Refresh
              </Button>
              
              <IconButton onClick={handleMenuOpen}>
                <MoreVertIcon />
              </IconButton>
              
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={handleMenuClose}>
                  <AnalyticsIcon sx={{ mr: 1 }} />
                  View Analytics
                </MenuItem>
                <MenuItem onClick={handleMenuClose}>
                  <SecurityIcon sx={{ mr: 1 }} />
                  Security Report
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleMenuClose}>
                  Export Data
                </MenuItem>
              </Menu>
            </Box>
          </Box>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <StatCard
                  title="Total Users"
                  value={stats.totalUsers}
                  change="+12%"
                  changeType="positive"
                  icon={<DashboardIcon />}
                  color="primary"
                  loading={isLoading}
                />
              </motion.div>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <StatCard
                  title="Active Scans"
                  value={stats.activeScans}
                  change="+5%"
                  changeType="positive"
                  icon={<SecurityIcon />}
                  color="secondary"
                  loading={isLoading}
                />
              </motion.div>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <StatCard
                  title="Devices Monitored"
                  value={stats.devicesMonitored}
                  change="+8%"
                  changeType="positive"
                  icon={<DevicesIcon />}
                  color="success"
                  loading={isLoading}
                />
              </motion.div>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <StatCard
                  title="Threats Blocked"
                  value={stats.threatsBlocked}
                  change="-2%"
                  changeType="negative"
                  icon={<WarningIcon />}
                  color="error"
                  loading={isLoading}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Main Content Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3}>
            {/* Network Health Overview */}
            <Grid item xs={12} lg={8}>
              <motion.div variants={itemVariants}>
                <Card sx={{ height: '400px' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" component="h2">
                        Network Health Overview
                      </Typography>
                      <Chip
                        label={`${stats.networkHealth}% Healthy`}
                        color={stats.networkHealth > 80 ? 'success' : stats.networkHealth > 60 ? 'warning' : 'error'}
                        icon={stats.networkHealth > 80 ? <CheckCircleIcon /> : <WarningIcon />}
                      />
                    </Box>
                    
                    <ChartCard
                      data={dashboardData?.networkHealthChart || []}
                      loading={isLoading}
                      type="area"
                    />
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            {/* Security Overview */}
            <Grid item xs={12} lg={4}>
              <motion.div variants={itemVariants}>
                <SecurityOverview
                  data={dashboardData?.securityOverview}
                  loading={isLoading}
                />
              </motion.div>
            </Grid>

            {/* AI Insights */}
            <Grid item xs={12} lg={6}>
              <motion.div variants={itemVariants}>
                <AIInsights
                  insights={dashboardData?.aiInsights || []}
                  loading={isLoading}
                />
              </motion.div>
            </Grid>

            {/* Device Status */}
            <Grid item xs={12} lg={6}>
              <motion.div variants={itemVariants}>
                <DeviceStatus
                  devices={dashboardData?.recentDevices || []}
                  loading={isLoading}
                />
              </motion.div>
            </Grid>

            {/* Recent Activity */}
            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <RecentActivity
                  activities={dashboardData?.recentActivity || []}
                  loading={isLoading}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      </Box>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </>
  );
};

export default DashboardPage;
