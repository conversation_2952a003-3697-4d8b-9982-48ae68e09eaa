{"name": "securenet-pro-dashboard", "version": "1.0.0", "description": "SecureNet Pro Web Dashboard - Analytics and Management Portal", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "recharts": "^2.8.0", "framer-motion": "^10.16.5", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "yup": "^1.3.3", "@hookform/resolvers": "^3.3.2", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-helmet-async": "^1.3.0", "react-intersection-observer": "^9.5.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.202", "webpack-bundle-analyzer": "^4.10.1"}, "proxy": "http://localhost:5000"}