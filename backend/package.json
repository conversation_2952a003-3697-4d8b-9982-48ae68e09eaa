{"name": "securenet-pro-backend", "version": "1.0.0", "description": "SecureNet Pro Backend API with Authentication and Analytics", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run", "build": "npm run migrate && npm run seed"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "knex": "^3.0.1", "pg": "^8.11.3", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "redis": "^4.6.10", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "express-winston": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}, "keywords": ["network-security", "authentication", "analytics", "api", "backend"], "author": "SecureNet Pro Team", "license": "MIT"}