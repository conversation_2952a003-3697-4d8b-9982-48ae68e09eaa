const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const db = require('../database/connection');
const logger = require('../utils/logger');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// Stricter rate limiting for auth endpoints
const strictAuthLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true,
});

// Validation rules
const registerValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('username').isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/).withMessage('Username must be 3-30 characters, alphanumeric and underscore only'),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('Password must be at least 8 characters with uppercase, lowercase, and number'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).withMessage('Last name must be 1-50 characters')
];

const loginValidation = [
  body('emailOrUsername').notEmpty().withMessage('Email or username is required'),
  body('password').notEmpty().withMessage('Password is required')
];

// Register endpoint
router.post('/register', registerValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, username, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await db('users')
      .where('email', email)
      .orWhere('username', username)
      .first();

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: existingUser.email === email ? 'Email already registered' : 'Username already taken'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userId = require('uuid').v4();
    const user = {
      id: userId,
      email,
      username,
      password_hash: passwordHash,
      first_name: firstName || null,
      last_name: lastName || null,
      role: 'user',
      is_email_verified: false,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date(),
      login_count: 0,
      analytics_enabled: true,
      ai_insights_enabled: true,
      subscription_type: 'free'
    };

    await db('users').insert(user);

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        username: user.username,
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Create session record
    const sessionId = require('uuid').v4();
    await db('user_sessions').insert({
      session_id: sessionId,
      user_id: userId,
      device_id: req.headers['x-device-id'] || 'web',
      device_name: req.headers['user-agent'] || 'Unknown',
      device_type: req.headers['x-device-type'] || 'web',
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      is_active: true,
      created_at: new Date(),
      last_activity_at: new Date(),
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    });

    // Log analytics event
    await db('user_analytics').insert({
      id: require('uuid').v4(),
      user_id: userId,
      session_id: sessionId,
      event_type: 'user_registered',
      event_name: 'account_created',
      event_data: JSON.stringify({ registration_method: 'email' }),
      timestamp: new Date(),
      network_type: req.headers['x-network-type'] || null
    });

    // Send welcome email (optional)
    try {
      await sendEmail({
        to: email,
        subject: 'Welcome to SecureNet Pro!',
        template: 'welcome',
        data: {
          firstName: firstName || username,
          username
        }
      });
    } catch (emailError) {
      logger.warn('Failed to send welcome email:', emailError.message);
    }

    logger.info(`New user registered: ${email} (${userId})`);

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        subscriptionType: user.subscription_type,
        createdAt: user.created_at
      },
      token,
      sessionId
    });

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to register user'
    });
  }
});

// Login endpoint
router.post('/login', strictAuthLimiter, loginValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { emailOrUsername, password } = req.body;

    // Find user by email or username
    const user = await db('users')
      .where('email', emailOrUsername)
      .orWhere('username', emailOrUsername)
      .first();

    if (!user) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid credentials'
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been deactivated'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid credentials'
      });
    }

    // Update login info
    await db('users')
      .where('id', user.id)
      .update({
        last_login_at: new Date(),
        login_count: user.login_count + 1,
        updated_at: new Date()
      });

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        username: user.username,
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Create session record
    const sessionId = require('uuid').v4();
    await db('user_sessions').insert({
      session_id: sessionId,
      user_id: user.id,
      device_id: req.headers['x-device-id'] || 'web',
      device_name: req.headers['user-agent'] || 'Unknown',
      device_type: req.headers['x-device-type'] || 'web',
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      is_active: true,
      created_at: new Date(),
      last_activity_at: new Date(),
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    });

    // Log analytics event
    await db('user_analytics').insert({
      id: require('uuid').v4(),
      user_id: user.id,
      session_id: sessionId,
      event_type: 'user_login',
      event_name: 'login_success',
      event_data: JSON.stringify({ 
        login_method: 'password',
        login_count: user.login_count + 1 
      }),
      timestamp: new Date(),
      network_type: req.headers['x-network-type'] || null
    });

    logger.info(`User logged in: ${user.email} (${user.id})`);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        subscriptionType: user.subscription_type,
        lastLoginAt: new Date(),
        loginCount: user.login_count + 1
      },
      token,
      sessionId
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to authenticate user'
    });
  }
});

// Logout endpoint
router.post('/logout', async (req, res) => {
  try {
    const sessionId = req.headers['x-session-id'];
    
    if (sessionId) {
      // Deactivate session
      await db('user_sessions')
        .where('session_id', sessionId)
        .update({
          is_active: false,
          updated_at: new Date()
        });
    }

    res.json({
      message: 'Logout successful'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to logout'
    });
  }
});

// Verify token endpoint
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'No token provided'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get fresh user data
    const user = await db('users')
      .where('id', decoded.userId)
      .first();

    if (!user || !user.is_active) {
      return res.status(401).json({
        error: 'Invalid token'
      });
    }

    res.json({
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        subscriptionType: user.subscription_type
      }
    });

  } catch (error) {
    res.status(401).json({
      error: 'Invalid token',
      valid: false
    });
  }
});

module.exports = router;
