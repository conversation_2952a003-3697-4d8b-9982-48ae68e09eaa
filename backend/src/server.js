const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const analyticsRoutes = require('./routes/analytics');
const scanRoutes = require('./routes/scans');
const deviceRoutes = require('./routes/devices');
const insightsRoutes = require('./routes/insights');
const userRoutes = require('./routes/users');

// Import middleware
const authMiddleware = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Auth rate limiting (stricter)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 auth requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true,
});

// Basic middleware
app.use(compression());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API routes
app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/analytics', authMiddleware, analyticsRoutes);
app.use('/api/scans', authMiddleware, scanRoutes);
app.use('/api/devices', authMiddleware, deviceRoutes);
app.use('/api/insights', authMiddleware, insightsRoutes);
app.use('/api/users', authMiddleware, userRoutes);

// Real-time WebSocket connections
io.use((socket, next) => {
  // Authenticate WebSocket connections
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication error'));
  }
  
  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.userId;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

io.on('connection', (socket) => {
  logger.info(`User ${socket.userId} connected via WebSocket`);
  
  // Join user-specific room
  socket.join(`user_${socket.userId}`);
  
  // Handle real-time scan updates
  socket.on('start_scan', async (data) => {
    try {
      // Broadcast scan start to user's devices
      io.to(`user_${socket.userId}`).emit('scan_started', {
        scanId: data.scanId,
        scanType: data.scanType,
        timestamp: new Date().toISOString()
      });
      
      // Here you would trigger actual scan logic
      logger.info(`Scan started for user ${socket.userId}: ${data.scanType}`);
    } catch (error) {
      socket.emit('scan_error', { error: error.message });
    }
  });
  
  // Handle scan progress updates
  socket.on('scan_progress', (data) => {
    io.to(`user_${socket.userId}`).emit('scan_progress_update', data);
  });
  
  // Handle scan completion
  socket.on('scan_complete', (data) => {
    io.to(`user_${socket.userId}`).emit('scan_completed', data);
  });
  
  // Handle AI insights generation
  socket.on('generate_insights', async () => {
    try {
      // Generate AI insights for user
      const insights = await generateUserInsights(socket.userId);
      socket.emit('insights_generated', insights);
    } catch (error) {
      socket.emit('insights_error', { error: error.message });
    }
  });
  
  socket.on('disconnect', () => {
    logger.info(`User ${socket.userId} disconnected`);
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found on this server.',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// Start server
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  logger.info(`🚀 SecureNet Pro Backend API running on port ${PORT}`);
  logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🔗 WebSocket server ready for real-time connections`);
});

// Helper function for AI insights generation
async function generateUserInsights(userId) {
  // This would integrate with your AI/ML service
  // For now, return sample insights
  return {
    insights: [
      {
        type: 'security_recommendation',
        title: 'Network Security Optimization',
        description: 'Your network security score has improved by 15% this week',
        confidence: 0.92,
        priority: 'medium'
      },
      {
        type: 'usage_pattern',
        title: 'Peak Activity Detected',
        description: 'Most network activity occurs between 9 AM - 5 PM',
        confidence: 0.87,
        priority: 'low'
      }
    ],
    generated_at: new Date().toISOString()
  };
}

module.exports = { app, server, io };
