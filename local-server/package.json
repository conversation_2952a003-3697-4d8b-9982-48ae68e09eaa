{"name": "securenet-pro-local-server", "version": "1.0.0", "description": "SecureNet Pro Local Development Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "setup": "node setup.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "sqlite3": "^5.1.6", "better-sqlite3": "^9.2.2", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "express-winston": "^4.2.0", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["network-security", "authentication", "analytics", "api", "local-server"], "author": "SecureNet Pro Team", "license": "MIT"}