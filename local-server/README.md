# 🚀 SecureNet Pro Local Development Server

## 📋 Overview

This is a complete local development server for testing the SecureNet Pro Android app. It provides:

- ✅ **Authentication API** - Register, login, logout
- ✅ **Analytics Tracking** - User behavior and app usage
- ✅ **Network Scanning** - Scan results and device discovery
- ✅ **AI Insights** - Machine learning recommendations
- ✅ **Real-time Updates** - WebSocket connections
- ✅ **SQLite Database** - Local data storage

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd local-server
npm install
```

### 2. Setup Database & Sample Data
```bash
npm run setup
```

### 3. Start Server
```bash
npm start
```

### 4. Test Server
```bash
curl http://localhost:3001/health
```

## 📱 Android App Integration

### For Android Emulator
The app will automatically connect to: `http://********:3001`

### For Physical Device
1. Find your computer's IP address:
   ```bash
   # On macOS/Linux
   ifconfig | grep "inet "
   
   # On Windows
   ipconfig
   ```

2. Update the IP in `LocalApiClient.kt`:
   ```kotlin
   private const val PHYSICAL_DEVICE_URL = "http://YOUR_IP_HERE:3001/"
   ```

3. Make sure your phone and computer are on the same WiFi network

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /health` - Server health check

### Analytics
- `POST /api/analytics/events` - Track analytics event
- `GET /api/analytics/events` - Get user analytics

### Network Scanning
- `POST /api/scans` - Create new scan
- `PUT /api/scans/:scanId` - Update scan results
- `GET /api/scans` - Get user scans

### Devices
- `POST /api/devices` - Save discovered device
- `GET /api/devices` - Get user devices

### AI Insights
- `GET /api/insights` - Get AI insights

### Dashboard
- `GET /api/dashboard` - Get dashboard data

## 🧪 Test Credentials

```
Email: <EMAIL>
Username: testuser
Password: testpass123
```

## 📊 Sample Data

The setup script creates:
- ✅ 1 test user (premium subscription)
- ✅ 3 analytics events
- ✅ 2 network scans
- ✅ 3 network devices
- ✅ 3 AI insights

## 🔍 Testing the Integration

### 1. Start the Server
```bash
npm start
```

You should see:
```
🚀 SecureNet Pro Local Server running!
📍 Server: http://localhost:3001
📍 Health: http://localhost:3001/health
📊 Database: SQLite (./securenet.db)
🔒 JWT Secret: your-super...
📡 WebSocket: Ready for real-time connections
✅ Ready for Android app testing!
```

### 2. Test Server Health
```bash
curl http://localhost:3001/health
```

Expected response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 1.234,
  "environment": "local-development",
  "version": "1.0.0",
  "database": "SQLite",
  "features": ["authentication", "analytics", "network-scanning", "ai-insights"]
}
```

### 3. Test Authentication
```bash
# Register new user
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "newuser",
    "password": "password123",
    "firstName": "New",
    "lastName": "User"
  }'

# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "emailOrUsername": "<EMAIL>",
    "password": "testpass123"
  }'
```

### 4. Test with Android App
1. Build and install the Android app
2. Open the app
3. Try registering a new user or login with test credentials
4. Perform network scans
5. Check analytics in the dashboard

## 🔧 Configuration

### Environment Variables
Create a `.env` file:
```env
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-for-development
NODE_ENV=development
```

### Database
- **Type**: SQLite
- **File**: `./securenet.db`
- **Auto-created**: Yes

### CORS
- **Enabled**: Yes (all origins for development)
- **Credentials**: Yes

### Rate Limiting
- **Window**: 15 minutes
- **Max Requests**: 1000 (permissive for development)

## 📡 Real-time Features

### WebSocket Connection
```javascript
const socket = io('http://localhost:3001');

// Authenticate
socket.emit('authenticate', 'your-jwt-token');

// Listen for events
socket.on('scan_started', (data) => {
  console.log('Scan started:', data);
});

socket.on('scan_completed', (data) => {
  console.log('Scan completed:', data);
});
```

## 🐛 Troubleshooting

### Server Won't Start
1. Check if port 3001 is available:
   ```bash
   lsof -i :3001
   ```
2. Kill any process using the port:
   ```bash
   kill -9 <PID>
   ```

### Android App Can't Connect
1. **Emulator**: Use `http://********:3001`
2. **Physical Device**: 
   - Use your computer's IP address
   - Ensure same WiFi network
   - Check firewall settings

### Database Issues
1. Delete the database file:
   ```bash
   rm securenet.db
   ```
2. Run setup again:
   ```bash
   npm run setup
   ```

### CORS Errors
- The server allows all origins for development
- If you still get CORS errors, check the request headers

## 📈 Monitoring

### Logs
- All requests are logged to console
- Database operations are logged
- WebSocket connections are logged

### Database Inspection
```bash
# Install SQLite CLI
npm install -g sqlite3

# Open database
sqlite3 securenet.db

# View tables
.tables

# View users
SELECT * FROM users;

# View analytics
SELECT * FROM analytics_events ORDER BY timestamp DESC LIMIT 10;
```

## 🚀 Production Deployment

This server is for **development only**. For production:

1. Use PostgreSQL instead of SQLite
2. Add proper security headers
3. Implement rate limiting
4. Add input validation
5. Use environment-specific configurations
6. Add logging and monitoring
7. Use HTTPS

## 📞 Support

If you encounter issues:
1. Check the console logs
2. Verify network connectivity
3. Test API endpoints with curl
4. Check Android app logs

---

**🎉 Happy Testing! Your SecureNet Pro local development environment is ready!**
