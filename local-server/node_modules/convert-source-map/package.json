{"name": "convert-source-map", "version": "2.0.0", "description": "Converts a source-map from/to  different formats and allows adding/changing properties.", "main": "index.js", "scripts": {"test": "tap test/*.js --color"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/convert-source-map.git"}, "homepage": "https://github.com/thlorenz/convert-source-map", "devDependencies": {"inline-source-map": "~0.6.2", "tap": "~9.0.0"}, "keywords": ["convert", "sourcemap", "source", "map", "browser", "debug"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": "MIT", "engine": {"node": ">=4"}, "files": ["index.js"]}