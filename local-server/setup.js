const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

console.log('🚀 Setting up SecureNet Pro Local Server...');

// Initialize database
const db = new Database('./securenet.db');

// Initialize database tables first
function initDatabase() {
  console.log('🗄️ Initializing database tables...');

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      first_name TEXT,
      last_name TEXT,
      avatar_url TEXT,
      role TEXT DEFAULT 'user',
      subscription_plan TEXT DEFAULT 'premium',
      subscription_status TEXT DEFAULT 'active',
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME,
      login_count INTEGER DEFAULT 0
    )
  `);

  // Analytics events table
  db.exec(`
    CREATE TABLE IF NOT EXISTS analytics_events (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      session_id TEXT,
      event_type TEXT NOT NULL,
      event_name TEXT NOT NULL,
      event_data TEXT,
      screen_name TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      duration_ms INTEGER,
      network_type TEXT,
      battery_level INTEGER,
      memory_usage_mb INTEGER,
      cpu_usage_percent REAL,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Network scans table
  db.exec(`
    CREATE TABLE IF NOT EXISTS network_scans (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      scan_id TEXT UNIQUE NOT NULL,
      scan_type TEXT NOT NULL,
      target_range TEXT NOT NULL,
      scan_status TEXT DEFAULT 'pending',
      devices_found INTEGER DEFAULT 0,
      vulnerabilities_found INTEGER DEFAULT 0,
      scan_duration_ms INTEGER DEFAULT 0,
      success BOOLEAN DEFAULT 0,
      error_message TEXT,
      scan_results TEXT,
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      completed_at DATETIME,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Network devices table
  db.exec(`
    CREATE TABLE IF NOT EXISTS network_devices (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      device_id TEXT NOT NULL,
      device_name TEXT,
      ip_address TEXT NOT NULL,
      mac_address TEXT,
      device_type TEXT DEFAULT 'unknown',
      manufacturer TEXT,
      security_risk TEXT DEFAULT 'low',
      is_known_device BOOLEAN DEFAULT 0,
      is_trusted BOOLEAN DEFAULT 0,
      first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      total_connections INTEGER DEFAULT 1,
      notes TEXT,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, device_id)
    )
  `);

  // AI insights table
  db.exec(`
    CREATE TABLE IF NOT EXISTS ai_insights (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      insight_type TEXT NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      recommendation TEXT,
      severity TEXT DEFAULT 'low',
      confidence_score REAL,
      data_points TEXT,
      is_read BOOLEAN DEFAULT 0,
      is_actioned BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  console.log('✅ Database tables created');
}

// Create sample data for testing
async function setupSampleData() {
  console.log('📊 Creating sample data...');

  try {
    // Create a test user
    const testUserId = uuidv4();
    const passwordHash = await bcrypt.hash('testpass123', 12);
    
    const insertUser = db.prepare(`
      INSERT OR REPLACE INTO users (
        id, email, username, password_hash, first_name, last_name, 
        role, subscription_plan, subscription_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    insertUser.run(
      testUserId, 
      '<EMAIL>', 
      'testuser', 
      passwordHash, 
      'Test', 
      'User',
      'user',
      'premium',
      'active'
    );
    
    console.log('✅ Test user created:');
    console.log('   Email: <EMAIL>');
    console.log('   Username: testuser');
    console.log('   Password: testpass123');
    
    // Create sample analytics events
    const analyticsEvents = [
      {
        id: uuidv4(),
        user_id: testUserId,
        event_type: 'app_launch',
        event_name: 'app_opened',
        screen_name: 'dashboard'
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        event_type: 'network_scan',
        event_name: 'scan_started',
        event_data: JSON.stringify({ scan_type: 'device_discovery' })
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        event_type: 'feature_used',
        event_name: 'analytics_viewed',
        screen_name: 'analytics'
      }
    ];
    
    const insertAnalytics = db.prepare(`
      INSERT INTO analytics_events (id, user_id, event_type, event_name, event_data, screen_name)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    analyticsEvents.forEach(event => {
      insertAnalytics.run(
        event.id, event.user_id, event.event_type, 
        event.event_name, event.event_data || null, event.screen_name || null
      );
    });
    
    console.log('✅ Sample analytics events created');
    
    // Create sample network scans
    const networkScans = [
      {
        id: uuidv4(),
        user_id: testUserId,
        scan_id: 'scan_' + Date.now(),
        scan_type: 'device_discovery',
        target_range: '192.168.1.0/24',
        scan_status: 'completed',
        devices_found: 12,
        vulnerabilities_found: 2,
        scan_duration_ms: 15000,
        success: 1
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        scan_id: 'scan_' + (Date.now() + 1000),
        scan_type: 'vulnerability',
        target_range: '192.168.1.0/24',
        scan_status: 'completed',
        devices_found: 8,
        vulnerabilities_found: 0,
        scan_duration_ms: 25000,
        success: 1
      }
    ];
    
    const insertScan = db.prepare(`
      INSERT INTO network_scans (
        id, user_id, scan_id, scan_type, target_range, scan_status,
        devices_found, vulnerabilities_found, scan_duration_ms, success
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    networkScans.forEach(scan => {
      insertScan.run(
        scan.id, scan.user_id, scan.scan_id, scan.scan_type, scan.target_range,
        scan.scan_status, scan.devices_found, scan.vulnerabilities_found,
        scan.scan_duration_ms, scan.success
      );
    });
    
    console.log('✅ Sample network scans created');
    
    // Create sample network devices
    const networkDevices = [
      {
        id: uuidv4(),
        user_id: testUserId,
        device_id: 'device_router',
        device_name: 'Home Router',
        ip_address: '***********',
        mac_address: '00:11:22:33:44:55',
        device_type: 'router',
        manufacturer: 'Netgear',
        security_risk: 'low',
        is_known_device: 1
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        device_id: 'device_laptop',
        device_name: 'MacBook Pro',
        ip_address: '***********00',
        mac_address: '00:11:22:33:44:66',
        device_type: 'computer',
        manufacturer: 'Apple',
        security_risk: 'low',
        is_known_device: 1
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        device_id: 'device_unknown',
        device_name: null,
        ip_address: '*************',
        mac_address: '00:11:22:33:44:77',
        device_type: 'unknown',
        manufacturer: null,
        security_risk: 'medium',
        is_known_device: 0
      }
    ];
    
    const insertDevice = db.prepare(`
      INSERT INTO network_devices (
        id, user_id, device_id, device_name, ip_address, mac_address,
        device_type, manufacturer, security_risk, is_known_device
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    networkDevices.forEach(device => {
      insertDevice.run(
        device.id, device.user_id, device.device_id, device.device_name,
        device.ip_address, device.mac_address, device.device_type,
        device.manufacturer, device.security_risk, device.is_known_device
      );
    });
    
    console.log('✅ Sample network devices created');
    
    // Create sample AI insights
    const aiInsights = [
      {
        id: uuidv4(),
        user_id: testUserId,
        insight_type: 'security',
        title: 'Unknown Device Detected',
        description: 'A new device has been detected on your network that is not recognized.',
        recommendation: 'Review the device and mark it as trusted if it belongs to you.',
        severity: 'medium',
        confidence_score: 0.85
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        insight_type: 'performance',
        title: 'Network Scan Optimization',
        description: 'Your recent scans are taking longer than average.',
        recommendation: 'Consider reducing the scan range or adjusting scan parameters.',
        severity: 'low',
        confidence_score: 0.72
      },
      {
        id: uuidv4(),
        user_id: testUserId,
        insight_type: 'behavior',
        title: 'Regular Scanning Pattern',
        description: 'You consistently perform network scans every 2-3 days.',
        recommendation: 'Consider setting up automated scanning for better coverage.',
        severity: 'low',
        confidence_score: 0.91
      }
    ];
    
    const insertInsight = db.prepare(`
      INSERT INTO ai_insights (
        id, user_id, insight_type, title, description, recommendation, severity, confidence_score
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    aiInsights.forEach(insight => {
      insertInsight.run(
        insight.id, insight.user_id, insight.insight_type, insight.title,
        insight.description, insight.recommendation, insight.severity, insight.confidence_score
      );
    });
    
    console.log('✅ Sample AI insights created');
    
    // Display summary
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get().count;
    const eventCount = db.prepare('SELECT COUNT(*) as count FROM analytics_events').get().count;
    const scanCount = db.prepare('SELECT COUNT(*) as count FROM network_scans').get().count;
    const deviceCount = db.prepare('SELECT COUNT(*) as count FROM network_devices').get().count;
    const insightCount = db.prepare('SELECT COUNT(*) as count FROM ai_insights').get().count;
    
    console.log('\n📊 Database Summary:');
    console.log(`   Users: ${userCount}`);
    console.log(`   Analytics Events: ${eventCount}`);
    console.log(`   Network Scans: ${scanCount}`);
    console.log(`   Network Devices: ${deviceCount}`);
    console.log(`   AI Insights: ${insightCount}`);
    
    console.log('\n🎯 Test Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Username: testuser');
    console.log('   Password: testpass123');
    
    console.log('\n🚀 Setup complete! You can now:');
    console.log('   1. Start the server: npm start');
    console.log('   2. Test the API: http://localhost:3001/health');
    console.log('   3. Login with the test credentials in your Android app');
    
  } catch (error) {
    console.error('❌ Error setting up sample data:', error);
  } finally {
    db.close();
  }
}

// Initialize database and create sample data
initDatabase();
setupSampleData();
