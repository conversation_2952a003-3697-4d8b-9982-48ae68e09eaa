const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Initialize SQLite database
const db = new Database('./securenet.db');

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-for-development';

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false // Disable for development
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // More permissive for local development
  message: 'Too many requests from this IP, please try again later.',
});

app.use(limiter);

// Basic middleware
app.use(compression());
app.use(cors({
  origin: "*", // Allow all origins for local development
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined'));

// Initialize database tables
function initDatabase() {
  console.log('🗄️ Initializing database...');
  
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      first_name TEXT,
      last_name TEXT,
      avatar_url TEXT,
      role TEXT DEFAULT 'user',
      subscription_plan TEXT DEFAULT 'premium',
      subscription_status TEXT DEFAULT 'active',
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME,
      login_count INTEGER DEFAULT 0
    )
  `);

  // Analytics events table
  db.exec(`
    CREATE TABLE IF NOT EXISTS analytics_events (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      session_id TEXT,
      event_type TEXT NOT NULL,
      event_name TEXT NOT NULL,
      event_data TEXT,
      screen_name TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      duration_ms INTEGER,
      network_type TEXT,
      battery_level INTEGER,
      memory_usage_mb INTEGER,
      cpu_usage_percent REAL,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Network scans table
  db.exec(`
    CREATE TABLE IF NOT EXISTS network_scans (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      scan_id TEXT UNIQUE NOT NULL,
      scan_type TEXT NOT NULL,
      target_range TEXT NOT NULL,
      scan_status TEXT DEFAULT 'pending',
      devices_found INTEGER DEFAULT 0,
      vulnerabilities_found INTEGER DEFAULT 0,
      scan_duration_ms INTEGER DEFAULT 0,
      success BOOLEAN DEFAULT 0,
      error_message TEXT,
      scan_results TEXT,
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      completed_at DATETIME,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Network devices table
  db.exec(`
    CREATE TABLE IF NOT EXISTS network_devices (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      device_id TEXT NOT NULL,
      device_name TEXT,
      ip_address TEXT NOT NULL,
      mac_address TEXT,
      device_type TEXT DEFAULT 'unknown',
      manufacturer TEXT,
      security_risk TEXT DEFAULT 'low',
      is_known_device BOOLEAN DEFAULT 0,
      is_trusted BOOLEAN DEFAULT 0,
      first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      total_connections INTEGER DEFAULT 1,
      notes TEXT,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, device_id)
    )
  `);

  // AI insights table
  db.exec(`
    CREATE TABLE IF NOT EXISTS ai_insights (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      insight_type TEXT NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      recommendation TEXT,
      severity TEXT DEFAULT 'low',
      confidence_score REAL,
      data_points TEXT,
      is_read BOOLEAN DEFAULT 0,
      is_actioned BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  console.log('✅ Database initialized successfully');
}

// Authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'local-development',
    version: '1.0.0',
    database: 'SQLite',
    features: ['authentication', 'analytics', 'network-scanning', 'ai-insights']
  });
});

// Authentication routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, username, password, firstName, lastName } = req.body;

    // Validate input
    if (!email || !username || !password) {
      return res.status(400).json({ error: 'Email, username, and password are required' });
    }

    // Check if user exists
    const existingUser = db.prepare('SELECT id FROM users WHERE email = ? OR username = ?').get(email, username);
    if (existingUser) {
      return res.status(409).json({ error: 'User already exists' });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const userId = uuidv4();
    const stmt = db.prepare(`
      INSERT INTO users (id, email, username, password_hash, first_name, last_name)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(userId, email, username, passwordHash, firstName || null, lastName || null);

    // Generate JWT token
    const token = jwt.sign(
      { userId, email, username, role: 'user' },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Get created user
    const user = db.prepare('SELECT id, email, username, first_name, last_name, role, subscription_plan, created_at FROM users WHERE id = ?').get(userId);

    console.log(`✅ New user registered: ${email} (${userId})`);

    res.status(201).json({
      message: 'User registered successfully',
      user,
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { emailOrUsername, password } = req.body;

    if (!emailOrUsername || !password) {
      return res.status(400).json({ error: 'Email/username and password are required' });
    }

    // Find user
    const user = db.prepare('SELECT * FROM users WHERE email = ? OR username = ?').get(emailOrUsername, emailOrUsername);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Update login info
    db.prepare('UPDATE users SET last_login_at = CURRENT_TIMESTAMP, login_count = login_count + 1 WHERE id = ?').run(user.id);

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // Return user data (without password)
    const { password_hash, ...userWithoutPassword } = user;

    console.log(`✅ User logged in: ${user.email} (${user.id})`);

    res.json({
      message: 'Login successful',
      user: userWithoutPassword,
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Analytics routes
app.post('/api/analytics/events', authenticateToken, (req, res) => {
  try {
    const { eventType, eventName, eventData, screenName, duration, networkType, batteryLevel, memoryUsage, cpuUsage } = req.body;
    
    const eventId = uuidv4();
    const stmt = db.prepare(`
      INSERT INTO analytics_events (
        id, user_id, event_type, event_name, event_data, screen_name, 
        duration_ms, network_type, battery_level, memory_usage_mb, cpu_usage_percent
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      eventId, req.user.userId, eventType, eventName, 
      eventData ? JSON.stringify(eventData) : null, screenName,
      duration, networkType, batteryLevel, memoryUsage, cpuUsage
    );

    // Emit real-time event
    io.to(`user_${req.user.userId}`).emit('analytics_event', {
      id: eventId,
      eventType,
      eventName,
      timestamp: new Date().toISOString()
    });

    res.json({ message: 'Event tracked successfully', eventId });
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ error: 'Failed to track event' });
  }
});

app.get('/api/analytics/events', authenticateToken, (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 100;
    const events = db.prepare(`
      SELECT * FROM analytics_events 
      WHERE user_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `).all(req.user.userId, limit);

    res.json({ events, total: events.length });
  } catch (error) {
    console.error('Analytics fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Network scanning routes
app.post('/api/scans', authenticateToken, (req, res) => {
  try {
    const { scanId, scanType, targetRange } = req.body;
    
    const id = uuidv4();
    const stmt = db.prepare(`
      INSERT INTO network_scans (id, user_id, scan_id, scan_type, target_range, scan_status)
      VALUES (?, ?, ?, ?, ?, 'running')
    `);
    
    stmt.run(id, req.user.userId, scanId, scanType, targetRange);

    // Emit real-time scan start
    io.to(`user_${req.user.userId}`).emit('scan_started', {
      id,
      scanId,
      scanType,
      targetRange,
      status: 'running'
    });

    res.json({ message: 'Scan started', scanId: id });
  } catch (error) {
    console.error('Scan creation error:', error);
    res.status(500).json({ error: 'Failed to start scan' });
  }
});

app.put('/api/scans/:scanId', authenticateToken, (req, res) => {
  try {
    const { scanId } = req.params;
    const { devicesFound, vulnerabilitiesFound, scanDuration, success, errorMessage, scanResults } = req.body;
    
    const stmt = db.prepare(`
      UPDATE network_scans 
      SET devices_found = ?, vulnerabilities_found = ?, scan_duration_ms = ?, 
          success = ?, error_message = ?, scan_results = ?, scan_status = ?, completed_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `);
    
    stmt.run(
      devicesFound, vulnerabilitiesFound, scanDuration, success ? 1 : 0, 
      errorMessage, scanResults ? JSON.stringify(scanResults) : null,
      success ? 'completed' : 'failed', scanId, req.user.userId
    );

    // Emit real-time scan completion
    io.to(`user_${req.user.userId}`).emit('scan_completed', {
      scanId,
      devicesFound,
      vulnerabilitiesFound,
      success,
      duration: scanDuration
    });

    res.json({ message: 'Scan updated successfully' });
  } catch (error) {
    console.error('Scan update error:', error);
    res.status(500).json({ error: 'Failed to update scan' });
  }
});

app.get('/api/scans', authenticateToken, (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const scans = db.prepare(`
      SELECT * FROM network_scans 
      WHERE user_id = ? 
      ORDER BY started_at DESC 
      LIMIT ?
    `).all(req.user.userId, limit);

    res.json({ scans, total: scans.length });
  } catch (error) {
    console.error('Scans fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch scans' });
  }
});

// Device routes
app.post('/api/devices', authenticateToken, (req, res) => {
  try {
    const { deviceId, deviceName, ipAddress, macAddress, deviceType, manufacturer, securityRisk, isKnownDevice } = req.body;
    
    const id = uuidv4();
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO network_devices 
      (id, user_id, device_id, device_name, ip_address, mac_address, device_type, manufacturer, security_risk, is_known_device, last_seen)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(id, req.user.userId, deviceId, deviceName, ipAddress, macAddress, deviceType, manufacturer, securityRisk, isKnownDevice ? 1 : 0);

    res.json({ message: 'Device saved successfully', deviceId: id });
  } catch (error) {
    console.error('Device save error:', error);
    res.status(500).json({ error: 'Failed to save device' });
  }
});

app.get('/api/devices', authenticateToken, (req, res) => {
  try {
    const devices = db.prepare(`
      SELECT * FROM network_devices 
      WHERE user_id = ? 
      ORDER BY last_seen DESC
    `).all(req.user.userId);

    res.json({ devices, total: devices.length });
  } catch (error) {
    console.error('Devices fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch devices' });
  }
});

// AI Insights routes
app.get('/api/insights', authenticateToken, (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const insights = db.prepare(`
      SELECT * FROM ai_insights 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `).all(req.user.userId, limit);

    res.json({ insights, total: insights.length });
  } catch (error) {
    console.error('Insights fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch insights' });
  }
});

// Dashboard data endpoint
app.get('/api/dashboard', authenticateToken, (req, res) => {
  try {
    // Get summary statistics
    const stats = {
      totalScans: db.prepare('SELECT COUNT(*) as count FROM network_scans WHERE user_id = ?').get(req.user.userId).count,
      totalDevices: db.prepare('SELECT COUNT(*) as count FROM network_devices WHERE user_id = ?').get(req.user.userId).count,
      totalEvents: db.prepare('SELECT COUNT(*) as count FROM analytics_events WHERE user_id = ?').get(req.user.userId).count,
      unreadInsights: db.prepare('SELECT COUNT(*) as count FROM ai_insights WHERE user_id = ? AND is_read = 0').get(req.user.userId).count
    };

    // Get recent data
    const recentScans = db.prepare('SELECT * FROM network_scans WHERE user_id = ? ORDER BY started_at DESC LIMIT 5').all(req.user.userId);
    const recentDevices = db.prepare('SELECT * FROM network_devices WHERE user_id = ? ORDER BY last_seen DESC LIMIT 10').all(req.user.userId);
    const recentInsights = db.prepare('SELECT * FROM ai_insights WHERE user_id = ? ORDER BY created_at DESC LIMIT 5').all(req.user.userId);

    res.json({
      stats,
      recentScans,
      recentDevices,
      recentInsights,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Dashboard fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('📡 Client connected:', socket.id);
  
  socket.on('authenticate', (token) => {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      socket.userId = decoded.userId;
      socket.join(`user_${decoded.userId}`);
      console.log(`✅ User ${decoded.userId} authenticated via WebSocket`);
      socket.emit('authenticated', { success: true });
    } catch (error) {
      socket.emit('authentication_error', { error: 'Invalid token' });
    }
  });
  
  socket.on('disconnect', () => {
    console.log('📡 Client disconnected:', socket.id);
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found on this server.',
    path: req.originalUrl
  });
});

// Initialize database and start server
initDatabase();

const PORT = process.env.PORT || 3001;
server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 SecureNet Pro Local Server running!');
  console.log(`📍 Server: http://localhost:${PORT}`);
  console.log(`📍 Health: http://localhost:${PORT}/health`);
  console.log(`📊 Database: SQLite (./securenet.db)`);
  console.log(`🔒 JWT Secret: ${JWT_SECRET.substring(0, 10)}...`);
  console.log(`📡 WebSocket: Ready for real-time connections`);
  console.log('✅ Ready for Android app testing!');
});

module.exports = { app, server, io };
