import { createClient } from '@supabase/supabase-js'

// Supabase configuration for SecureNet Pro
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Premium user authentication helpers
export const authHelpers = {
  // Sign up premium user
  async signUp(email, password, userData = {}) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          ...userData,
          subscription_plan: 'premium',
          subscription_status: 'active'
        }
      }
    })
    
    if (error) throw error
    return data
  },

  // Sign in user
  async signIn(email, password) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    return data
  },

  // Sign out user
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Get current user
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  },

  // Get user profile
  async getUserProfile(userId) {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  // Update user profile
  async updateProfile(userId, updates) {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Analytics helpers
export const analyticsHelpers = {
  // Track event
  async trackEvent(eventData) {
    const user = await authHelpers.getCurrentUser()
    if (!user) return
    
    const { error } = await supabase
      .from('analytics_events')
      .insert({
        user_id: user.id,
        session_id: user.id, // Simplified session ID
        ...eventData,
        timestamp: new Date().toISOString()
      })
    
    if (error) console.error('Analytics tracking error:', error)
  },

  // Get user analytics
  async getUserAnalytics(userId, limit = 100) {
    const { data, error } = await supabase
      .from('analytics_events')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  },

  // Get usage statistics
  async getUsageStatistics(userId, days = 30) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    const { data, error } = await supabase
      .from('usage_statistics')
      .select('*')
      .eq('user_id', userId)
      .gte('date', startDate.toISOString().split('T')[0])
      .order('date', { ascending: false })
    
    if (error) throw error
    return data
  }
}

// Network scanning helpers
export const networkHelpers = {
  // Save network scan
  async saveNetworkScan(scanData) {
    const user = await authHelpers.getCurrentUser()
    if (!user) throw new Error('User not authenticated')
    
    const { data, error } = await supabase
      .from('network_scans')
      .insert({
        user_id: user.id,
        ...scanData,
        started_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update scan results
  async updateScanResults(scanId, results) {
    const { data, error } = await supabase
      .from('network_scans')
      .update({
        ...results,
        completed_at: new Date().toISOString()
      })
      .eq('id', scanId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get user scans
  async getUserScans(userId, limit = 50) {
    const { data, error } = await supabase
      .from('network_scans')
      .select('*')
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  },

  // Save discovered device
  async saveNetworkDevice(deviceData) {
    const user = await authHelpers.getCurrentUser()
    if (!user) throw new Error('User not authenticated')
    
    const { data, error } = await supabase
      .from('network_devices')
      .upsert({
        user_id: user.id,
        ...deviceData,
        last_seen: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get user devices
  async getUserDevices(userId) {
    const { data, error } = await supabase
      .from('network_devices')
      .select('*')
      .eq('user_id', userId)
      .order('last_seen', { ascending: false })
    
    if (error) throw error
    return data
  }
}

// AI insights helpers
export const insightsHelpers = {
  // Get AI insights
  async getAIInsights(userId, limit = 20) {
    const { data, error } = await supabase
      .from('ai_insights')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  },

  // Mark insight as read
  async markInsightAsRead(insightId) {
    const { data, error } = await supabase
      .from('ai_insights')
      .update({ is_read: true })
      .eq('id', insightId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Generate new insights (this would typically be done server-side)
  async generateInsights(userId, analysisData) {
    const insights = []
    
    // Example insight generation logic
    if (analysisData.scans && analysisData.scans.length > 10) {
      insights.push({
        user_id: userId,
        insight_type: 'performance',
        category: 'scanning',
        title: 'High Scan Activity',
        description: `You've performed ${analysisData.scans.length} scans recently. Consider optimizing your scanning schedule.`,
        recommendation: 'Set up automated scanning to reduce manual effort.',
        severity: 'low',
        confidence_score: 0.8,
        data_points: { scan_count: analysisData.scans.length }
      })
    }
    
    if (analysisData.devices && analysisData.devices.some(d => d.security_risk === 'high')) {
      const highRiskDevices = analysisData.devices.filter(d => d.security_risk === 'high')
      insights.push({
        user_id: userId,
        insight_type: 'security',
        category: 'threat_detection',
        title: 'High-Risk Devices Detected',
        description: `Found ${highRiskDevices.length} devices with high security risk on your network.`,
        recommendation: 'Review and secure high-risk devices immediately.',
        severity: 'high',
        confidence_score: 0.95,
        data_points: { high_risk_count: highRiskDevices.length }
      })
    }
    
    // Save insights to database
    if (insights.length > 0) {
      const { data, error } = await supabase
        .from('ai_insights')
        .insert(insights)
        .select()
      
      if (error) throw error
      return data
    }
    
    return []
  }
}

// Real-time subscriptions
export const realtimeHelpers = {
  // Subscribe to user's analytics events
  subscribeToAnalytics(userId, callback) {
    return supabase
      .channel(`analytics:${userId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'analytics_events',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // Subscribe to network scans
  subscribeToScans(userId, callback) {
    return supabase
      .channel(`scans:${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'network_scans',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // Subscribe to AI insights
  subscribeToInsights(userId, callback) {
    return supabase
      .channel(`insights:${userId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'ai_insights',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // Unsubscribe from channel
  unsubscribe(subscription) {
    return supabase.removeChannel(subscription)
  }
}

// Dashboard data helpers
export const dashboardHelpers = {
  // Get dashboard overview data
  async getDashboardData(userId) {
    try {
      // Get recent scans
      const scans = await networkHelpers.getUserScans(userId, 10)
      
      // Get devices
      const devices = await networkHelpers.getUserDevices(userId)
      
      // Get insights
      const insights = await insightsHelpers.getAIInsights(userId, 5)
      
      // Get usage stats
      const usageStats = await analyticsHelpers.getUsageStatistics(userId, 7)
      
      // Calculate summary statistics
      const stats = {
        totalScans: scans.length,
        totalDevices: devices.length,
        activeThreats: devices.filter(d => d.security_risk === 'high' || d.security_risk === 'critical').length,
        unreadInsights: insights.filter(i => !i.is_read).length,
        networkHealth: calculateNetworkHealth(devices),
        lastScanTime: scans[0]?.started_at || null
      }
      
      return {
        stats,
        recentScans: scans.slice(0, 5),
        recentDevices: devices.slice(0, 10),
        insights: insights,
        usageStats: usageStats
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      throw error
    }
  }
}

// Helper function to calculate network health score
function calculateNetworkHealth(devices) {
  if (devices.length === 0) return 100
  
  const riskScores = {
    low: 1,
    medium: 3,
    high: 7,
    critical: 10
  }
  
  const totalRisk = devices.reduce((sum, device) => {
    return sum + (riskScores[device.security_risk] || 1)
  }, 0)
  
  const maxPossibleRisk = devices.length * 10
  const healthScore = Math.max(0, 100 - (totalRisk / maxPossibleRisk) * 100)
  
  return Math.round(healthScore)
}

export default supabase
