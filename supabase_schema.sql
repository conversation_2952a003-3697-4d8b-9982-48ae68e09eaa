-- 🚀 NETWORK GUARDIAN PRODUCTION DATABASE SCHEMA
-- Premium Network Security Platform - Supabase Setup
-- Execute this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- USER PROFILES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    last_name TEXT,
    username TEXT UNIQUE,
    subscription_tier TEXT DEFAULT 'premium' CHECK (subscription_tier IN ('free', 'premium', 'enterprise')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired')),
    subscription_expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    preferences JSONB DEFAULT '{}'::jsonb
);

-- ============================================================================
-- NETWORK SCANS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS network_scans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    scan_type TEXT NOT NULL CHECK (scan_type IN ('discovery', 'vulnerability', 'port', 'security', 'comprehensive')),
    status TEXT DEFAULT 'running' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    target_network TEXT,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    devices_found INTEGER DEFAULT 0,
    vulnerabilities_found INTEGER DEFAULT 0,
    security_issues INTEGER DEFAULT 0,
    scan_results JSONB DEFAULT '{}'::jsonb,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- DISCOVERED DEVICES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS discovered_devices (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    scan_id UUID REFERENCES network_scans(id) ON DELETE CASCADE,
    ip_address INET NOT NULL,
    mac_address TEXT,
    hostname TEXT,
    device_name TEXT,
    device_type TEXT,
    manufacturer TEXT,
    operating_system TEXT,
    is_online BOOLEAN DEFAULT TRUE,
    is_trusted BOOLEAN DEFAULT FALSE,
    is_known_device BOOLEAN DEFAULT FALSE,
    security_risk TEXT DEFAULT 'low' CHECK (security_risk IN ('low', 'medium', 'high', 'critical')),
    open_ports INTEGER[],
    services JSONB DEFAULT '[]'::jsonb,
    first_seen TIMESTAMPTZ DEFAULT NOW(),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    signal_strength INTEGER,
    connection_type TEXT,
    device_fingerprint TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- SECURITY EVENTS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS security_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    device_id UUID REFERENCES discovered_devices(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL CHECK (event_type IN ('new_device', 'suspicious_activity', 'vulnerability_detected', 'port_scan', 'malware_detected', 'unauthorized_access')),
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title TEXT NOT NULL,
    description TEXT,
    source_ip INET,
    destination_ip INET,
    port INTEGER,
    protocol TEXT,
    is_resolved BOOLEAN DEFAULT FALSE,
    is_false_positive BOOLEAN DEFAULT FALSE,
    resolution_notes TEXT,
    detected_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- VULNERABILITIES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS vulnerabilities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    device_id UUID REFERENCES discovered_devices(id) ON DELETE CASCADE,
    scan_id UUID REFERENCES network_scans(id) ON DELETE SET NULL,
    cve_id TEXT,
    vulnerability_name TEXT NOT NULL,
    description TEXT,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    cvss_score DECIMAL(3,1),
    affected_service TEXT,
    affected_port INTEGER,
    remediation_steps TEXT,
    is_patched BOOLEAN DEFAULT FALSE,
    patch_available BOOLEAN DEFAULT FALSE,
    exploit_available BOOLEAN DEFAULT FALSE,
    detected_at TIMESTAMPTZ DEFAULT NOW(),
    patched_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- USER ANALYTICS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS user_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    event_name TEXT NOT NULL,
    event_data JSONB DEFAULT '{}'::jsonb,
    session_id TEXT,
    device_info JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- NETWORK TRAFFIC TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS network_traffic (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    device_id UUID REFERENCES discovered_devices(id) ON DELETE CASCADE,
    source_ip INET NOT NULL,
    destination_ip INET NOT NULL,
    source_port INTEGER,
    destination_port INTEGER,
    protocol TEXT,
    bytes_transferred BIGINT DEFAULT 0,
    packets_count INTEGER DEFAULT 0,
    is_suspicious BOOLEAN DEFAULT FALSE,
    is_blocked BOOLEAN DEFAULT FALSE,
    threat_score INTEGER DEFAULT 0,
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- NOTIFICATIONS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('security_alert', 'new_device', 'scan_complete', 'vulnerability_found', 'system_update')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    is_read BOOLEAN DEFAULT FALSE,
    action_required BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription ON user_profiles(subscription_tier, subscription_status);

-- Network scans indexes
CREATE INDEX IF NOT EXISTS idx_network_scans_user_id ON network_scans(user_id);
CREATE INDEX IF NOT EXISTS idx_network_scans_status ON network_scans(status);
CREATE INDEX IF NOT EXISTS idx_network_scans_created_at ON network_scans(created_at DESC);

-- Discovered devices indexes
CREATE INDEX IF NOT EXISTS idx_discovered_devices_user_id ON discovered_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_ip ON discovered_devices(ip_address);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_mac ON discovered_devices(mac_address);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_last_seen ON discovered_devices(last_seen DESC);

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_detected_at ON security_events(detected_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(is_resolved);

-- Vulnerabilities indexes
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_user_id ON vulnerabilities(user_id);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_device_id ON vulnerabilities(device_id);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_cve ON vulnerabilities(cve_id);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_user_analytics_user_id ON user_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_event_name ON user_analytics(event_name);
CREATE INDEX IF NOT EXISTS idx_user_analytics_created_at ON user_analytics(created_at DESC);

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE discovered_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE vulnerabilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_traffic ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);

-- Network scans policies
CREATE POLICY "Users can view own scans" ON network_scans FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own scans" ON network_scans FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own scans" ON network_scans FOR UPDATE USING (auth.uid() = user_id);

-- Discovered devices policies
CREATE POLICY "Users can view own devices" ON discovered_devices FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own devices" ON discovered_devices FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own devices" ON discovered_devices FOR UPDATE USING (auth.uid() = user_id);

-- Security events policies
CREATE POLICY "Users can view own security events" ON security_events FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own security events" ON security_events FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own security events" ON security_events FOR UPDATE USING (auth.uid() = user_id);

-- Vulnerabilities policies
CREATE POLICY "Users can view own vulnerabilities" ON vulnerabilities FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own vulnerabilities" ON vulnerabilities FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own vulnerabilities" ON vulnerabilities FOR UPDATE USING (auth.uid() = user_id);

-- User analytics policies
CREATE POLICY "Users can view own analytics" ON user_analytics FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own analytics" ON user_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Network traffic policies
CREATE POLICY "Users can view own traffic" ON network_traffic FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own traffic" ON network_traffic FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_discovered_devices_updated_at BEFORE UPDATE ON discovered_devices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- SAMPLE DATA FOR TESTING
-- ============================================================================

-- This will be populated when users register through the app

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

-- Database schema setup complete!
-- Your Network Guardian production database is ready for deployment.
-- 
-- Next steps:
-- 1. Test user registration through the mobile app
-- 2. Verify data is being stored correctly
-- 3. Monitor performance and optimize as needed
-- 
-- 🚀 Ready for production deployment!
