#!/bin/bash

# SecureNet Pro Local Development Server Startup Script

echo "🚀 Starting SecureNet Pro Local Development Environment..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ required. Current version: $(node -v)"
    echo "   Please update Node.js from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Navigate to local-server directory
cd local-server

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Make sure you're in the correct directory."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

# Setup database if it doesn't exist
if [ ! -f "securenet.db" ]; then
    echo "🗄️ Setting up database and sample data..."
    npm run setup
    if [ $? -ne 0 ]; then
        echo "❌ Failed to setup database"
        exit 1
    fi
    echo "✅ Database setup complete"
else
    echo "✅ Database already exists"
fi

# Get local IP address for physical device testing
if command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
elif command -v ip &> /dev/null; then
    LOCAL_IP=$(ip route get 1 | awk '{print $7}' | head -1)
else
    LOCAL_IP="Unable to detect"
fi

echo ""
echo "🌐 Network Configuration:"
echo "   Emulator URL: http://********:3001"
echo "   Physical Device URL: http://$LOCAL_IP:3001"
echo ""

echo "🧪 Test Credentials:"
echo "   Email: <EMAIL>"
echo "   Username: testuser"
echo "   Password: testpass123"
echo ""

echo "🔧 API Endpoints:"
echo "   Health Check: http://localhost:3001/health"
echo "   Register: POST http://localhost:3001/api/auth/register"
echo "   Login: POST http://localhost:3001/api/auth/login"
echo "   Dashboard: GET http://localhost:3001/api/dashboard"
echo ""

echo "📱 Android App Setup:"
echo "   1. For emulator: No changes needed"
echo "   2. For physical device: Update PHYSICAL_DEVICE_URL in LocalApiClient.kt to http://$LOCAL_IP:3001/"
echo ""

echo "🚀 Starting server..."
echo "   Press Ctrl+C to stop the server"
echo ""

# Start the server
npm start
