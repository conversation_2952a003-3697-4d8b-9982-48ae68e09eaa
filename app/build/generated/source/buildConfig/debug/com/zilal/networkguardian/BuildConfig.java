/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.zilal.networkguardian;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.zilal.networkguardian";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0.0";
  // Field from default config.
  public static final String APP_NAME = "Zilal Network Security";
  // Field from default config.
  public static final boolean IS_PREMIUM_ONLY = false;
  // Field from default config.
  public static final String SUBSCRIPTION_PLAN = "enterprise";
  // Field from default config.
  public static final String SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sYnV3dXRtZGhoZGt0a3BzZ2hwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8";
  // Field from default config.
  public static final String SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sYnV3dXRtZGhoZGt0a3BzZ2hwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDk3Mjg3NCwiZXhwIjoyMDUwNTQ4ODc0fQ.Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8_Ej8";
  // Field from default config.
  public static final String SUPABASE_URL = "https://olbuwutmdhhdktkpsghp.supabase.co";
}
