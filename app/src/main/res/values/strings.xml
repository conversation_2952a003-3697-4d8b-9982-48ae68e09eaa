<resources>
    <!-- App Info -->
    <string name="app_name">Network Guardian</string>

    <!-- Common Actions -->
    <string name="action_go">Go</string>
    <string name="action_scan">Scan</string>
    <string name="action_stop">Stop</string>
    <string name="action_save">Save</string>
    <string name="action_share">Share</string>
    <string name="action_settings">Settings</string>

    <!-- Main Screens -->
    <string name="dashboard_title">Dashboard</string>
    <string name="scan_title">Network Scanner</string>
    <string name="results_title">Scan Results</string>
    <string name="settings_title">Settings</string>

    <!-- Scan Types -->
    <string name="scan_type_internal">Internal Network</string>
    <string name="scan_type_external">External Vulnerability</string>
    <string name="scan_type_port">Port Scanner</string>
    <string name="scan_type_traffic">Traffic Analysis</string>
    <string name="scan_type_wireless">Wireless Security</string>
    <string name="scan_type_behavioral">Behavioral Analysis</string>

    <!-- Scan Status -->
    <string name="status_idle">Ready to Scan</string>
    <string name="status_scanning">Scanning…</string>
    <string name="status_complete">Scan Complete</string>
    <string name="status_error">Scan Error</string>

    <!-- Results -->
    <string name="results_placeholder">(Results will appear here)</string>
    <string name="results_devices_found">Devices Found: %1$d</string>
    <string name="results_vulnerabilities_found">Vulnerabilities: %1$d</string>
    <string name="results_open_ports">Open Ports: %1$d</string>

    <!-- Settings Categories -->
    <string name="settings_category_apis">API Configuration</string>
    <string name="settings_category_scan">Scan Settings</string>
    <string name="settings_category_appearance">Appearance</string>

    <!-- API Settings -->
    <string name="settings_gemini_api">Gemini API Key</string>
    <string name="settings_virustotal_api">VirusTotal API Key</string>
    <string name="settings_shadowserver_api">ShadowServer API Key</string>

    <!-- Legacy strings (to be removed later) -->
    <string name="baking_title">Baking with Gemini</string>
    <string name="label_prompt">Prompt</string>
    <string name="prompt_placeholder">Provide a recipe for the baked goods in the image</string>
    <string name="image1_description">Cupcake</string>
    <string name="image2_description">Cookies</string>
    <string name="image3_description">Cake</string>
</resources>