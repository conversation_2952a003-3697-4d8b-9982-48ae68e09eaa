<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />

    <!-- VPN permission -->
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />

    <!-- Location permissions (needed for WiFi scanning on Android 9+) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Background execution -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- Required for FOREGROUND_SERVICE_CONNECTED_DEVICE -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <application
        android:name="com.zilal.networkguardian.NetworkGuardianApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Zilal"
        tools:targetApi="31">
        <activity
            android:name="com.zilal.networkguardian.ui.modern.SimpleModernActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.App.Starting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Keep old MainActivity for reference -->
        <activity
            android:name="com.zilal.networkguardian.MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.App.Starting" />

        <!-- Services and receivers for app functionality -->
        <service
            android:name="com.zilal.networkguardian.service.NetworkScannerService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync|connectedDevice" />

        <service
            android:name="com.zilal.networkguardian.service.DeviceMonitorService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync|connectedDevice" />

        <service
            android:name="com.zilal.networkguardian.service.DeviceMonitoringService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync|connectedDevice" />

        <service
            android:name="com.zilal.networkguardian.service.NetworkBlockingService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync|connectedDevice" />

        <service
            android:name="com.zilal.networkguardian.service.NetworkGuardianVpnService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.zilal.networkguardian.receiver.NetworkChangeReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="android.net.wifi.STATE_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.zilal.networkguardian.receiver.BootReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- FileProvider for app updates -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>