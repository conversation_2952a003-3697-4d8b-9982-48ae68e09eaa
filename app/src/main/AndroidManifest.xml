<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network and WiFi permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- System permissions for network analysis -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    
    <!-- Optional permissions for advanced features -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" 
        tools:ignore="SensitivePermissionProtectedByFirewall" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.NetworkGuardian"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Enhanced Mobile Main Activity with production features -->
        <activity
            android:name=".ui.mobile.EnhancedMobileMainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.NetworkGuardian"
            android:screenOrientation="portrait" />

        <!-- Mobile Main Activity with all features (backup) -->
        <activity
            android:name=".ui.mobile.MobileMainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.NetworkGuardian"
            android:screenOrientation="portrait" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.NetworkGuardian"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Network Monitoring Service -->
        <service
            android:name=".service.NetworkMonitoringService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Notification Service -->
        <service
            android:name=".service.NotificationService"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>
