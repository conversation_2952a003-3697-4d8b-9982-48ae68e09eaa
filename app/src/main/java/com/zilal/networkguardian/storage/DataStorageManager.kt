package com.zilal.networkguardian.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.NetworkTraffic
import com.zilal.networkguardian.model.Vulnerability
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.*
import kotlinx.serialization.json.Json
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Comprehensive Data Storage Manager
 * Handles both cloud and local storage with automatic synchronization
 */
@Singleton
class DataStorageManager @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val preferences: SharedPreferences = context.getSharedPreferences("network_guardian_prefs", Context.MODE_PRIVATE)
    private val json = Json { ignoreUnknownKeys = true }
    
    private val _storageMode = MutableStateFlow(getStorageMode())
    val storageMode: StateFlow<StorageMode> = _storageMode.asStateFlow()
    
    private val _syncState = MutableStateFlow<SyncState>(SyncState.Idle)
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    private val _localStorageSize = MutableStateFlow(0L)
    val localStorageSize: StateFlow<Long> = _localStorageSize.asStateFlow()
    
    companion object {
        private const val TAG = "DataStorageManager"
        private const val PREF_STORAGE_MODE = "storage_mode"
        private const val DEVICES_FILE = "network_devices.json"
        private const val TRAFFIC_FILE = "network_traffic.json"
        private const val VULNERABILITIES_FILE = "vulnerabilities.json"
        private const val SCAN_HISTORY_FILE = "scan_history.json"
        private const val MAX_LOCAL_STORAGE_MB = 100 // 100MB limit for local storage
    }
    
    /**
     * Storage modes
     */
    enum class StorageMode {
        LOCAL_ONLY,
        CLOUD_ONLY,
        HYBRID // Local with cloud sync
    }
    
    /**
     * Sync states
     */
    sealed class SyncState {
        object Idle : SyncState()
        object Syncing : SyncState()
        object Completed : SyncState()
        data class Error(val message: String) : SyncState()
    }
    
    /**
     * Data container for serialization
     */
    @Serializable
    data class NetworkData(
        val devices: List<SerializableNetworkDevice> = emptyList(),
        val trafficData: List<SerializableNetworkTraffic> = emptyList(),
        val vulnerabilities: List<SerializableVulnerability> = emptyList(),
        val scanHistory: List<ScanRecord> = emptyList(),
        val lastUpdated: Long = System.currentTimeMillis()
    )
    
    @Serializable
    data class SerializableNetworkDevice(
        val id: String,
        val ipAddress: String,
        val macAddress: String? = null,
        val hostname: String? = null,
        val manufacturer: String? = null,
        val model: String? = null,
        val deviceType: String? = null,
        val operatingSystem: String? = null,
        val customName: String? = null,
        val isOnline: Boolean = true,
        val openPorts: List<Int> = emptyList(),
        val services: Map<Int, String> = emptyMap(),
        val lastSeen: Long = System.currentTimeMillis(),
        val isBlocked: Boolean = false,
        val securityRisk: String = "UNKNOWN"
    )
    
    @Serializable
    data class SerializableNetworkTraffic(
        val deviceIp: String,
        val totalBytesReceived: Long = 0,
        val totalBytesSent: Long = 0,
        val packetsReceived: Long = 0,
        val packetsSent: Long = 0,
        val lastUpdated: Long = System.currentTimeMillis()
    )
    
    @Serializable
    data class SerializableVulnerability(
        val id: String,
        val name: String,
        val description: String,
        val severity: String,
        val deviceIp: String,
        val cveId: String? = null,
        val remediation: String? = null,
        val discoveredAt: Long = System.currentTimeMillis()
    )
    
    @Serializable
    data class ScanRecord(
        val id: String,
        val scanType: String,
        val devicesFound: Int,
        val vulnerabilitiesFound: Int,
        val startTime: Long,
        val endTime: Long,
        val duration: Long
    )
    
    init {
        // Calculate initial local storage size
        scope.launch {
            updateLocalStorageSize()
        }
    }
    
    /**
     * Set storage mode
     */
    fun setStorageMode(mode: StorageMode) {
        preferences.edit().putString(PREF_STORAGE_MODE, mode.name).apply()
        _storageMode.value = mode
        
        analyticsManager.trackEvent(
            eventName = "storage_mode_changed",
            properties = mapOf(
                "new_mode" to mode.name,
                "timestamp" to System.currentTimeMillis()
            )
        )
        
        // If switching to cloud, trigger sync
        if (mode == StorageMode.CLOUD_ONLY || mode == StorageMode.HYBRID) {
            syncToCloud()
        }
    }
    
    /**
     * Get current storage mode
     */
    private fun getStorageMode(): StorageMode {
        val modeName = preferences.getString(PREF_STORAGE_MODE, StorageMode.LOCAL_ONLY.name)
        return try {
            StorageMode.valueOf(modeName ?: StorageMode.LOCAL_ONLY.name)
        } catch (e: Exception) {
            StorageMode.LOCAL_ONLY
        }
    }
    
    /**
     * Save network devices
     */
    suspend fun saveNetworkDevices(devices: List<NetworkDevice>) {
        val serializableDevices = devices.map { device ->
            SerializableNetworkDevice(
                id = device.id,
                ipAddress = device.ipAddress,
                macAddress = device.macAddress,
                hostname = device.hostname,
                manufacturer = device.manufacturer,
                model = device.model,
                deviceType = device.deviceType?.name,
                operatingSystem = device.operatingSystem,
                customName = device.customName,
                isOnline = device.isOnline,
                openPorts = device.openPorts.toList(),
                services = device.services.toMap(),
                lastSeen = device.lastSeen,
                isBlocked = device.isBlocked,
                securityRisk = device.securityRisk.name
            )
        }
        
        when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> saveToLocal(DEVICES_FILE, serializableDevices)
            StorageMode.CLOUD_ONLY -> saveToCloud("devices", serializableDevices)
            StorageMode.HYBRID -> {
                saveToLocal(DEVICES_FILE, serializableDevices)
                saveToCloud("devices", serializableDevices)
            }
        }
    }
    
    /**
     * Load network devices
     */
    suspend fun loadNetworkDevices(): List<NetworkDevice> {
        return when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> loadDevicesFromLocal()
            StorageMode.CLOUD_ONLY -> loadDevicesFromCloud()
            StorageMode.HYBRID -> {
                val localDevices = loadDevicesFromLocal()
                val cloudDevices = loadDevicesFromCloud()
                mergeDeviceData(localDevices, cloudDevices)
            }
        }
    }
    
    /**
     * Save traffic data
     */
    suspend fun saveTrafficData(trafficData: Map<String, NetworkTraffic>) {
        val serializableTraffic = trafficData.values.map { traffic ->
            SerializableNetworkTraffic(
                deviceIp = traffic.deviceIp,
                totalBytesReceived = traffic.totalBytesReceived,
                totalBytesSent = traffic.totalBytesSent,
                packetsReceived = traffic.packetsReceived,
                packetsSent = traffic.packetsSent,
                lastUpdated = traffic.lastUpdated
            )
        }
        
        when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> saveToLocal(TRAFFIC_FILE, serializableTraffic)
            StorageMode.CLOUD_ONLY -> saveToCloud("traffic", serializableTraffic)
            StorageMode.HYBRID -> {
                saveToLocal(TRAFFIC_FILE, serializableTraffic)
                saveToCloud("traffic", serializableTraffic)
            }
        }
    }
    
    /**
     * Save vulnerabilities
     */
    suspend fun saveVulnerabilities(vulnerabilities: List<Vulnerability>, deviceIp: String) {
        val serializableVulns = vulnerabilities.map { vuln ->
            SerializableVulnerability(
                id = vuln.id,
                name = vuln.name,
                description = vuln.description,
                severity = vuln.severity.name,
                deviceIp = deviceIp,
                cveId = vuln.cveId,
                remediation = vuln.remediation,
                discoveredAt = System.currentTimeMillis()
            )
        }
        
        when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> saveToLocal(VULNERABILITIES_FILE, serializableVulns)
            StorageMode.CLOUD_ONLY -> saveToCloud("vulnerabilities", serializableVulns)
            StorageMode.HYBRID -> {
                saveToLocal(VULNERABILITIES_FILE, serializableVulns)
                saveToCloud("vulnerabilities", serializableVulns)
            }
        }
    }
    
    /**
     * Save scan record
     */
    suspend fun saveScanRecord(scanRecord: ScanRecord) {
        val existingRecords = loadScanHistory().toMutableList()
        existingRecords.add(scanRecord)
        
        // Keep only last 100 scan records
        val limitedRecords = existingRecords.takeLast(100)
        
        when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> saveToLocal(SCAN_HISTORY_FILE, limitedRecords)
            StorageMode.CLOUD_ONLY -> saveToCloud("scan_history", limitedRecords)
            StorageMode.HYBRID -> {
                saveToLocal(SCAN_HISTORY_FILE, limitedRecords)
                saveToCloud("scan_history", limitedRecords)
            }
        }
    }
    
    /**
     * Load scan history
     */
    suspend fun loadScanHistory(): List<ScanRecord> {
        return when (_storageMode.value) {
            StorageMode.LOCAL_ONLY -> loadFromLocal<List<ScanRecord>>(SCAN_HISTORY_FILE) ?: emptyList()
            StorageMode.CLOUD_ONLY -> loadFromCloud<List<ScanRecord>>("scan_history") ?: emptyList()
            StorageMode.HYBRID -> {
                val local = loadFromLocal<List<ScanRecord>>(SCAN_HISTORY_FILE) ?: emptyList()
                val cloud = loadFromCloud<List<ScanRecord>>("scan_history") ?: emptyList()
                (local + cloud).distinctBy { it.id }.sortedByDescending { it.startTime }
            }
        }
    }
    
    /**
     * Save data to local storage
     */
    private suspend fun saveToLocal(filename: String, data: Any) {
        withContext(Dispatchers.IO) {
            try {
                val file = File(context.filesDir, filename)
                val jsonString = json.encodeToString(data)
                file.writeText(jsonString)
                
                updateLocalStorageSize()
                
                Log.d(TAG, "Saved data to local file: $filename")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save data to local storage", e)
                analyticsManager.trackError("local_storage_save_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Load data from local storage
     */
    private suspend inline fun <reified T> loadFromLocal(filename: String): T? {
        return withContext(Dispatchers.IO) {
            try {
                val file = File(context.filesDir, filename)
                if (file.exists()) {
                    val jsonString = file.readText()
                    json.decodeFromString<T>(jsonString)
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load data from local storage", e)
                null
            }
        }
    }
    
    /**
     * Save data to cloud storage (Supabase)
     */
    private suspend fun saveToCloud(dataType: String, data: Any) {
        withContext(Dispatchers.IO) {
            try {
                // This would integrate with Supabase or your cloud storage
                // For now, we'll simulate cloud storage
                Log.d(TAG, "Saved $dataType to cloud storage")
                
                analyticsManager.trackEvent(
                    eventName = "cloud_storage_save",
                    properties = mapOf(
                        "data_type" to dataType,
                        "timestamp" to System.currentTimeMillis()
                    )
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save data to cloud storage", e)
                analyticsManager.trackError("cloud_storage_save_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Load data from cloud storage
     */
    private suspend inline fun <reified T> loadFromCloud(dataType: String): T? {
        return withContext(Dispatchers.IO) {
            try {
                // This would integrate with Supabase or your cloud storage
                // For now, we'll return null (no cloud data)
                Log.d(TAG, "Loaded $dataType from cloud storage")
                null
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load data from cloud storage", e)
                null
            }
        }
    }
    
    /**
     * Load devices from local storage
     */
    private suspend fun loadDevicesFromLocal(): List<NetworkDevice> {
        val serializableDevices = loadFromLocal<List<SerializableNetworkDevice>>(DEVICES_FILE) ?: emptyList()
        return serializableDevices.map { convertToNetworkDevice(it) }
    }
    
    /**
     * Load devices from cloud storage
     */
    private suspend fun loadDevicesFromCloud(): List<NetworkDevice> {
        val serializableDevices = loadFromCloud<List<SerializableNetworkDevice>>("devices") ?: emptyList()
        return serializableDevices.map { convertToNetworkDevice(it) }
    }
    
    /**
     * Convert serializable device to NetworkDevice
     */
    private fun convertToNetworkDevice(serializable: SerializableNetworkDevice): NetworkDevice {
        return NetworkDevice(
            id = serializable.id,
            ipAddress = serializable.ipAddress,
            macAddress = serializable.macAddress,
            hostname = serializable.hostname,
            manufacturer = serializable.manufacturer,
            model = serializable.model,
            deviceType = serializable.deviceType?.let { NetworkDevice.DeviceType.valueOf(it) },
            operatingSystem = serializable.operatingSystem,
            customName = serializable.customName,
            isOnline = serializable.isOnline,
            openPorts = serializable.openPorts.toMutableList(),
            services = serializable.services.toMutableMap(),
            lastSeen = serializable.lastSeen,
            isBlocked = serializable.isBlocked,
            securityRisk = NetworkDevice.SecurityRisk.valueOf(serializable.securityRisk)
        )
    }
    
    /**
     * Merge device data from local and cloud
     */
    private fun mergeDeviceData(localDevices: List<NetworkDevice>, cloudDevices: List<NetworkDevice>): List<NetworkDevice> {
        val mergedDevices = mutableMapOf<String, NetworkDevice>()
        
        // Add local devices
        localDevices.forEach { device ->
            mergedDevices[device.ipAddress] = device
        }
        
        // Merge cloud devices (cloud data takes precedence for newer data)
        cloudDevices.forEach { cloudDevice ->
            val localDevice = mergedDevices[cloudDevice.ipAddress]
            if (localDevice == null || cloudDevice.lastSeen > localDevice.lastSeen) {
                mergedDevices[cloudDevice.ipAddress] = cloudDevice
            }
        }
        
        return mergedDevices.values.toList()
    }
    
    /**
     * Sync data to cloud
     */
    fun syncToCloud() {
        if (_storageMode.value == StorageMode.LOCAL_ONLY) return
        
        _syncState.value = SyncState.Syncing
        
        scope.launch {
            try {
                // Load all local data and sync to cloud
                val devices = loadDevicesFromLocal()
                if (devices.isNotEmpty()) {
                    saveToCloud("devices", devices.map { convertToSerializableDevice(it) })
                }
                
                _syncState.value = SyncState.Completed
                
                // Clear local storage if in cloud-only mode
                if (_storageMode.value == StorageMode.CLOUD_ONLY) {
                    clearLocalStorage()
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Cloud sync failed", e)
                _syncState.value = SyncState.Error(e.message ?: "Sync failed")
            }
        }
    }
    
    /**
     * Convert NetworkDevice to serializable format
     */
    private fun convertToSerializableDevice(device: NetworkDevice): SerializableNetworkDevice {
        return SerializableNetworkDevice(
            id = device.id,
            ipAddress = device.ipAddress,
            macAddress = device.macAddress,
            hostname = device.hostname,
            manufacturer = device.manufacturer,
            model = device.model,
            deviceType = device.deviceType?.name,
            operatingSystem = device.operatingSystem,
            customName = device.customName,
            isOnline = device.isOnline,
            openPorts = device.openPorts.toList(),
            services = device.services.toMap(),
            lastSeen = device.lastSeen,
            isBlocked = device.isBlocked,
            securityRisk = device.securityRisk.name
        )
    }
    
    /**
     * Clear local storage
     */
    private suspend fun clearLocalStorage() {
        withContext(Dispatchers.IO) {
            try {
                val files = listOf(DEVICES_FILE, TRAFFIC_FILE, VULNERABILITIES_FILE, SCAN_HISTORY_FILE)
                files.forEach { filename ->
                    val file = File(context.filesDir, filename)
                    if (file.exists()) {
                        file.delete()
                    }
                }
                
                updateLocalStorageSize()
                
                Log.d(TAG, "Local storage cleared")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear local storage", e)
            }
        }
    }
    
    /**
     * Update local storage size
     */
    private suspend fun updateLocalStorageSize() {
        withContext(Dispatchers.IO) {
            try {
                val totalSize = context.filesDir.walkTopDown()
                    .filter { it.isFile }
                    .map { it.length() }
                    .sum()
                
                _localStorageSize.value = totalSize
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to calculate storage size", e)
            }
        }
    }
    
    /**
     * Get formatted storage size
     */
    fun getFormattedStorageSize(): String {
        val sizeBytes = _localStorageSize.value
        return when {
            sizeBytes < 1024 -> "$sizeBytes B"
            sizeBytes < 1024 * 1024 -> "${sizeBytes / 1024} KB"
            sizeBytes < 1024 * 1024 * 1024 -> "${sizeBytes / (1024 * 1024)} MB"
            else -> "${sizeBytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * Check if storage limit is exceeded
     */
    fun isStorageLimitExceeded(): Boolean {
        val sizeMB = _localStorageSize.value / (1024 * 1024)
        return sizeMB > MAX_LOCAL_STORAGE_MB
    }
}
