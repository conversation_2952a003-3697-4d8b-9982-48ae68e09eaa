package com.zilal.networkguardian.network

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.model.NetworkDevice
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlin.coroutines.coroutineContext
import java.net.*
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Free Network Scanner - Real network discovery for all users
 * NO SUBSCRIPTION REQUIRED - All features completely free
 */
@Singleton
class FreeNetworkScanner @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _scanState = MutableStateFlow<ScanState>(ScanState.Idle)
    val scanState: StateFlow<ScanState> = _scanState.asStateFlow()
    
    private val _discoveredDevices = MutableStateFlow<List<NetworkDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<NetworkDevice>> = _discoveredDevices.asStateFlow()
    
    private val _scanProgress = MutableStateFlow(0f)
    val scanProgress: StateFlow<Float> = _scanProgress.asStateFlow()
    
    private val deviceCache = ConcurrentHashMap<String, NetworkDevice>()
    private var scanJob: Job? = null
    
    companion object {
        private const val TAG = "FreeNetworkScanner"
        private const val PING_TIMEOUT = 2000 // 2 seconds
        private val COMMON_PORTS = listOf(22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 3389, 5900)
    }
    
    /**
     * Start professional network scanning with real-time validation
     */
    fun startNetworkScan(scanType: ScanType = ScanType.COMPREHENSIVE) {
        if (_scanState.value is ScanState.Scanning) {
            Log.w(TAG, "Network scan already in progress")
            return
        }

        // Validate network connectivity before starting
        if (!isNetworkAvailable()) {
            _scanState.value = ScanState.Error("No network connection available")
            analyticsManager.trackError("network_scan_error", "No network connection")
            return
        }

        val startTime = System.currentTimeMillis()
        _scanState.value = ScanState.Scanning
        _scanProgress.value = 0f
        deviceCache.clear()

        analyticsManager.trackEvent(
            eventName = "professional_network_scan_started",
            properties = mapOf(
                "scan_type" to scanType.name,
                "timestamp" to System.currentTimeMillis(),
                "network_available" to true
            )
        )
        
        scanJob = scope.launch {
            try {
                when (scanType) {
                    ScanType.QUICK -> performQuickScan()
                    ScanType.COMPREHENSIVE -> performComprehensiveScan()
                    ScanType.DEEP -> performDeepScan()
                    ScanType.VULNERABILITY -> performVulnerabilityScan()
                }
                
                val duration = System.currentTimeMillis() - startTime
                val devicesFound = deviceCache.size
                
                _scanState.value = ScanState.Completed(devicesFound)
                _discoveredDevices.value = deviceCache.values.toList()
                
                analyticsManager.trackNetworkScan(
                    scanType = scanType.name,
                    devicesFound = devicesFound,
                    duration = duration,
                    success = true
                )
                
                Log.i(TAG, "Free network scan completed: $devicesFound devices found in ${duration}ms")
                
            } catch (e: Exception) {
                Log.e(TAG, "Network scan failed", e)
                _scanState.value = ScanState.Error(e.message ?: "Scan failed")
                
                analyticsManager.trackError(
                    errorType = "free_network_scan_error",
                    errorMessage = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * Stop current scan
     */
    fun stopScan() {
        scanJob?.cancel()
        _scanState.value = ScanState.Idle
        _scanProgress.value = 0f
        
        analyticsManager.trackEvent(
            eventName = "free_network_scan_stopped",
            properties = mapOf("timestamp" to System.currentTimeMillis())
        )
    }
    
    /**
     * Quick scan - Ping sweep only
     */
    private suspend fun performQuickScan() {
        val networkInfo = getNetworkInfo()
        if (networkInfo == null) {
            throw Exception("No network connection available")
        }
        
        val (networkAddress, _) = networkInfo
        val totalHosts = 254
        var scannedHosts = 0
        
        Log.i(TAG, "Starting free quick scan on network: $networkAddress")
        
        // Real ping sweep
        for (i in 1..254) {
            if (!coroutineContext.isActive) break

            val hostIp = "${networkAddress.substringBeforeLast(".")}.${i}"

            scope.launch {
                if (pingHost(hostIp)) {
                    val device = createBasicDevice(hostIp)
                    deviceCache[hostIp] = device
                    _discoveredDevices.value = deviceCache.values.toList()

                    Log.d(TAG, "Device discovered: $hostIp")
                }
            }

            scannedHosts++
            _scanProgress.value = scannedHosts.toFloat() / totalHosts
            delay(50) // Reasonable delay for real network scanning
        }
    }
    
    /**
     * Comprehensive scan - Ping + Port scan + Service detection
     */
    private suspend fun performComprehensiveScan() {
        val networkInfo = getNetworkInfo()
        if (networkInfo == null) {
            throw Exception("No network connection available")
        }
        
        val (networkAddress, _) = networkInfo
        Log.i(TAG, "Starting free comprehensive scan on network: $networkAddress")
        
        // Phase 1: Host discovery (60% of progress)
        val activeHosts = mutableListOf<String>()
        for (i in 1..254) {
            if (!coroutineContext.isActive) break

            val hostIp = "${networkAddress.substringBeforeLast(".")}.${i}"

            if (pingHost(hostIp)) {
                activeHosts.add(hostIp)
                val device = createBasicDevice(hostIp)
                deviceCache[hostIp] = device
                _discoveredDevices.value = deviceCache.values.toList()

                Log.d(TAG, "Active host found: $hostIp")
            }

            _scanProgress.value = (i.toFloat() / 254) * 0.6f
        }
        
        // Phase 2: Port scanning and service detection (40% of progress)
        activeHosts.forEachIndexed { index, hostIp ->
            if (!coroutineContext.isActive) return@forEachIndexed

            val enhancedDevice = enhanceDeviceInfo(hostIp)
            deviceCache[hostIp] = enhancedDevice
            _discoveredDevices.value = deviceCache.values.toList()

            _scanProgress.value = 0.6f + ((index + 1).toFloat() / activeHosts.size) * 0.4f
        }
    }
    
    /**
     * Deep scan - Comprehensive + Advanced analysis
     */
    private suspend fun performDeepScan() {
        performComprehensiveScan()
        
        // Additional deep analysis on discovered devices
        val devices = deviceCache.values.toList()
        devices.forEachIndexed { index, device ->
            if (!coroutineContext.isActive) return@forEachIndexed

            // Perform additional analysis
            val deepAnalyzedDevice = device.copy(
                manufacturer = identifyManufacturer(device.macAddress ?: "Unknown"),
                operatingSystem = detectOperatingSystem(device.openPorts)
            )
            deviceCache[device.ipAddress] = deepAnalyzedDevice
            _discoveredDevices.value = deviceCache.values.toList()

            _scanProgress.value = 0.8f + ((index + 1).toFloat() / devices.size) * 0.2f
        }
    }
    
    /**
     * Vulnerability scan - Security assessment
     */
    private suspend fun performVulnerabilityScan() {
        performComprehensiveScan()
        
        // Security assessment
        val devices = deviceCache.values.toList()
        devices.forEach { device ->
            if (!coroutineContext.isActive) return@forEach

            val securityAssessment = assessDeviceSecurity(device)
            val updatedDevice = device.copy(
                securityRisk = securityAssessment.riskLevel,
                vulnerabilities = securityAssessment.vulnerabilities
            )
            deviceCache[device.ipAddress] = updatedDevice
        }
        
        _discoveredDevices.value = deviceCache.values.toList()
    }
    
    /**
     * Real ping implementation
     */
    private suspend fun pingHost(hostIp: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val address = InetAddress.getByName(hostIp)
                address.isReachable(PING_TIMEOUT)
            } catch (e: Exception) {
                Log.d(TAG, "Ping failed for $hostIp: ${e.message}")
                false
            }
        }
    }
    
    /**
     * Create basic device information
     */
    private suspend fun createBasicDevice(ipAddress: String): NetworkDevice {
        val hostname = getHostname(ipAddress)
        val macAddress = getMacAddress(ipAddress)
        
        return NetworkDevice(
            ipAddress = ipAddress,
            macAddress = macAddress,
            hostname = hostname,
            deviceType = identifyDeviceType(hostname),
            manufacturer = "Unknown",
            operatingSystem = "Unknown",
            isOnline = true,
            openPorts = mutableListOf(),
            services = mutableMapOf(),
            lastSeen = System.currentTimeMillis(),
            signalStrength = -1
        )
    }
    
    /**
     * Enhance device with port scanning and service detection
     */
    private suspend fun enhanceDeviceInfo(ipAddress: String): NetworkDevice {
        val basicDevice = deviceCache[ipAddress] ?: createBasicDevice(ipAddress)
        
        val openPorts = scanPorts(ipAddress)
        val services = detectServices(openPorts)
        val deviceType = identifyDeviceTypeFromPorts(openPorts)
        
        return basicDevice.copy(
            openPorts = openPorts.toMutableList(),
            services = services.toMutableMap(),
            deviceType = deviceType
        )
    }
    
    /**
     * Scan common ports
     */
    private suspend fun scanPorts(hostIp: String): List<Int> {
        val openPorts = mutableListOf<Int>()
        
        COMMON_PORTS.forEach { port ->
            if (isPortOpen(hostIp, port)) {
                openPorts.add(port)
                Log.d(TAG, "Open port found: $hostIp:$port")
            }
        }
        
        return openPorts
    }
    
    /**
     * Check if port is open
     */
    private suspend fun isPortOpen(hostIp: String, port: Int): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Socket().use { socket ->
                    socket.connect(InetSocketAddress(hostIp, port), 1000)
                    true
                }
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * Check if network is available for scanning
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val network = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

            networkCapabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET) == true &&
            networkCapabilities.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_VALIDATED) == true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check network availability", e)
            false
        }
    }

    /**
     * Get network information with enhanced validation
     */
    private fun getNetworkInfo(): Pair<String, String>? {
        try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val dhcpInfo = wifiManager.dhcpInfo

            if (dhcpInfo.ipAddress == 0) {
                Log.w(TAG, "No valid IP address from DHCP")
                return null
            }

            val ipAddress = String.format(
                "%d.%d.%d.%d",
                dhcpInfo.ipAddress and 0xff,
                dhcpInfo.ipAddress shr 8 and 0xff,
                dhcpInfo.ipAddress shr 16 and 0xff,
                dhcpInfo.ipAddress shr 24 and 0xff
            )

            // Validate IP address format
            if (!isValidIPAddress(ipAddress)) {
                Log.w(TAG, "Invalid IP address format: $ipAddress")
                return null
            }

            return Pair(ipAddress, "*************")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get network info", e)
            analyticsManager.trackError("network_info_error", e.message ?: "Unknown error")
            return null
        }
    }

    /**
     * Validate IP address format
     */
    private fun isValidIPAddress(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false

            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }
    
    // Helper methods
    private fun getHostname(ipAddress: String): String? {
        return try {
            InetAddress.getByName(ipAddress).hostName.takeIf { it != ipAddress }
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getMacAddress(ipAddress: String): String {
        return "Unknown" // Simplified for now
    }
    
    private fun identifyDeviceType(hostname: String?): NetworkDevice.DeviceType {
        if (hostname == null) return NetworkDevice.DeviceType.UNKNOWN

        return when {
            hostname.contains("router", ignoreCase = true) -> NetworkDevice.DeviceType.ROUTER
            hostname.contains("android", ignoreCase = true) -> NetworkDevice.DeviceType.MOBILE
            hostname.contains("iphone", ignoreCase = true) -> NetworkDevice.DeviceType.MOBILE
            hostname.contains("ipad", ignoreCase = true) -> NetworkDevice.DeviceType.MOBILE
            hostname.contains("macbook", ignoreCase = true) -> NetworkDevice.DeviceType.COMPUTER
            hostname.contains("windows", ignoreCase = true) -> NetworkDevice.DeviceType.COMPUTER
            hostname.contains("printer", ignoreCase = true) -> NetworkDevice.DeviceType.PRINTER
            hostname.contains("tv", ignoreCase = true) -> NetworkDevice.DeviceType.SMART_TV
            else -> NetworkDevice.DeviceType.COMPUTER
        }
    }

    private fun identifyDeviceTypeFromPorts(openPorts: List<Int>): NetworkDevice.DeviceType {
        return when {
            openPorts.contains(80) || openPorts.contains(443) -> NetworkDevice.DeviceType.ROUTER
            openPorts.contains(22) -> NetworkDevice.DeviceType.SERVER
            openPorts.contains(3389) -> NetworkDevice.DeviceType.COMPUTER
            openPorts.contains(445) || openPorts.contains(139) -> NetworkDevice.DeviceType.COMPUTER
            else -> NetworkDevice.DeviceType.UNKNOWN
        }
    }
    
    private fun identifyManufacturer(macAddress: String): String {
        return "Unknown" // Simplified for now
    }
    
    private fun detectServices(openPorts: List<Int>): Map<Int, String> {
        val services = mutableMapOf<Int, String>()
        
        openPorts.forEach { port ->
            val service = when (port) {
                22 -> "SSH"
                23 -> "Telnet"
                53 -> "DNS"
                80 -> "HTTP"
                135 -> "RPC"
                139 -> "NetBIOS"
                443 -> "HTTPS"
                445 -> "SMB"
                993 -> "IMAPS"
                995 -> "POP3S"
                3389 -> "RDP"
                5900 -> "VNC"
                else -> "Unknown Service"
            }
            services[port] = service
        }
        
        return services
    }
    
    private fun detectOperatingSystem(openPorts: List<Int>): String {
        return when {
            openPorts.contains(3389) -> "Windows"
            openPorts.contains(22) && !openPorts.contains(445) -> "Linux/Unix"
            openPorts.contains(445) || openPorts.contains(139) -> "Windows"
            openPorts.contains(5900) -> "macOS/Linux"
            else -> "Unknown"
        }
    }
    
    private fun assessDeviceSecurity(device: NetworkDevice): SecurityAssessment {
        val vulnerabilities = mutableListOf<com.zilal.networkguardian.model.Vulnerability>()
        var riskLevel = NetworkDevice.SecurityRisk.LOW

        // Real security checks
        if (device.openPorts.contains(23)) {
            vulnerabilities.add(
                com.zilal.networkguardian.model.Vulnerability(
                    id = "TELNET_DETECTED",
                    name = "Telnet Service",
                    description = "Unencrypted telnet service detected",
                    severity = com.zilal.networkguardian.model.Vulnerability.Severity.HIGH,
                    cveId = null,
                    remediation = "Disable telnet and use SSH instead"
                )
            )
            riskLevel = NetworkDevice.SecurityRisk.HIGH
        }

        if (device.openPorts.contains(21)) {
            vulnerabilities.add(
                com.zilal.networkguardian.model.Vulnerability(
                    id = "FTP_DETECTED",
                    name = "FTP Service",
                    description = "Potentially insecure FTP service detected",
                    severity = com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM,
                    cveId = null,
                    remediation = "Use SFTP or FTPS instead"
                )
            )
            if (riskLevel == NetworkDevice.SecurityRisk.LOW) riskLevel = NetworkDevice.SecurityRisk.MEDIUM
        }

        if (device.openPorts.size > 10) {
            vulnerabilities.add(
                com.zilal.networkguardian.model.Vulnerability(
                    id = "MANY_PORTS",
                    name = "Multiple Open Ports",
                    description = "Many open ports detected (${device.openPorts.size})",
                    severity = com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM,
                    cveId = null,
                    remediation = "Close unnecessary ports"
                )
            )
            if (riskLevel == NetworkDevice.SecurityRisk.LOW) riskLevel = NetworkDevice.SecurityRisk.MEDIUM
        }

        return SecurityAssessment(riskLevel, vulnerabilities)
    }
}

/**
 * Scan types
 */
enum class ScanType {
    QUICK,
    COMPREHENSIVE,
    DEEP,
    VULNERABILITY
}

/**
 * Scan states
 */
sealed class ScanState {
    object Idle : ScanState()
    object Scanning : ScanState()
    data class Completed(val devicesFound: Int) : ScanState()
    data class Error(val message: String) : ScanState()
}

/**
 * Security assessment result
 */
data class SecurityAssessment(
    val riskLevel: NetworkDevice.SecurityRisk,
    val vulnerabilities: List<com.zilal.networkguardian.model.Vulnerability>
)
