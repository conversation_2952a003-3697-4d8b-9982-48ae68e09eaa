package com.zilal.networkguardian.network

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.app.ActivityCompat
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Advanced Bluetooth Scanner - Like Fing's Bluetooth discovery
 * Provides comprehensive Bluetooth device scanning and analysis
 */
@Singleton
class BluetoothScanner @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bluetoothAdapter = bluetoothManager.adapter
    
    private val _bluetoothDevices = MutableStateFlow<List<BluetoothDeviceInfo>>(emptyList())
    val bluetoothDevices: StateFlow<List<BluetoothDeviceInfo>> = _bluetoothDevices.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()
    
    private val _bluetoothEnabled = MutableStateFlow(bluetoothAdapter?.isEnabled == true)
    val bluetoothEnabled: StateFlow<Boolean> = _bluetoothEnabled.asStateFlow()
    
    private val discoveredDevices = mutableSetOf<BluetoothDeviceInfo>()
    
    companion object {
        private const val TAG = "BluetoothScanner"
        private const val SCAN_DURATION = 12000L // 12 seconds
    }
    
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val device = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)
                    
                    device?.let {
                        val deviceInfo = analyzeBluetoothDevice(it, rssi.toInt())
                        discoveredDevices.add(deviceInfo)
                        _bluetoothDevices.value = discoveredDevices.toList().sortedByDescending { it.signalStrength }
                    }
                }
                BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                    Log.d(TAG, "Bluetooth discovery started")
                }
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    Log.d(TAG, "Bluetooth discovery finished")
                    _isScanning.value = false
                    
                    analyticsManager.trackEvent(
                        eventName = "bluetooth_scan_completed",
                        properties = mapOf(
                            "devices_found" to discoveredDevices.size,
                            "timestamp" to System.currentTimeMillis()
                        )
                    )
                }
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                    _bluetoothEnabled.value = state == BluetoothAdapter.STATE_ON
                }
            }
        }
    }
    
    init {
        // Register broadcast receiver
        val filter = IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_FOUND)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        }
        context.registerReceiver(bluetoothReceiver, filter)
    }
    
    /**
     * Start Bluetooth device scanning
     */
    fun startBluetoothScan() {
        if (!bluetoothAdapter.isEnabled) {
            Log.w(TAG, "Bluetooth is not enabled")
            return
        }
        
        if (_isScanning.value) {
            Log.w(TAG, "Bluetooth scan already in progress")
            return
        }
        
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) 
            != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Bluetooth scan permission not granted")
            return
        }
        
        _isScanning.value = true
        discoveredDevices.clear()
        
        scope.launch {
            try {
                // Add paired devices first
                addPairedDevices()
                
                // Start discovery
                if (bluetoothAdapter.isDiscovering) {
                    bluetoothAdapter.cancelDiscovery()
                }
                
                val discoveryStarted = bluetoothAdapter.startDiscovery()
                if (!discoveryStarted) {
                    Log.w(TAG, "Failed to start Bluetooth discovery")
                    _isScanning.value = false
                    return@launch
                }
                
                // Wait for scan to complete or timeout
                delay(SCAN_DURATION)
                
                if (bluetoothAdapter.isDiscovering) {
                    bluetoothAdapter.cancelDiscovery()
                }
                
                _isScanning.value = false
                
            } catch (e: Exception) {
                Log.e(TAG, "Bluetooth scan failed", e)
                _isScanning.value = false
                analyticsManager.trackError("bluetooth_scan_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Add paired devices to the list
     */
    private fun addPairedDevices() {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) 
            != PackageManager.PERMISSION_GRANTED) {
            return
        }
        
        try {
            val pairedDevices = bluetoothAdapter.bondedDevices
            pairedDevices?.forEach { device ->
                val deviceInfo = analyzeBluetoothDevice(device, 0, isPaired = true)
                discoveredDevices.add(deviceInfo)
            }
            _bluetoothDevices.value = discoveredDevices.toList()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get paired devices", e)
        }
    }
    
    /**
     * Analyze Bluetooth device
     */
    private fun analyzeBluetoothDevice(
        device: BluetoothDevice, 
        rssi: Int, 
        isPaired: Boolean = false
    ): BluetoothDeviceInfo {
        val name = try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) 
                == PackageManager.PERMISSION_GRANTED) {
                device.name ?: "Unknown Device"
            } else {
                "Unknown Device"
            }
        } catch (e: Exception) {
            "Unknown Device"
        }
        
        val address = device.address
        val deviceClass = device.bluetoothClass
        val deviceType = analyzeDeviceType(deviceClass?.majorDeviceClass ?: 0)
        val services = analyzeServices(deviceClass?.deviceClass ?: 0)
        val signalStrength = calculateSignalStrength(rssi)
        val security = analyzeBluetoothSecurity(device)
        val vendor = getVendorFromAddress(address)
        
        return BluetoothDeviceInfo(
            name = name,
            address = address,
            deviceType = deviceType,
            services = services,
            signalStrength = signalStrength,
            rssi = rssi,
            isPaired = isPaired,
            isConnected = isDeviceConnected(device),
            security = security,
            vendor = vendor,
            lastSeen = System.currentTimeMillis(),
            bluetoothClass = deviceClass?.deviceClass ?: 0
        )
    }
    
    /**
     * Analyze device type from Bluetooth class
     */
    private fun analyzeDeviceType(majorDeviceClass: Int): BluetoothDeviceType {
        return when (majorDeviceClass) {
            0x0100 -> BluetoothDeviceType.COMPUTER
            0x0200 -> BluetoothDeviceType.PHONE
            0x0300 -> BluetoothDeviceType.NETWORK
            0x0400 -> BluetoothDeviceType.AUDIO_VIDEO
            0x0500 -> BluetoothDeviceType.PERIPHERAL
            0x0600 -> BluetoothDeviceType.IMAGING
            0x0700 -> BluetoothDeviceType.WEARABLE
            0x0800 -> BluetoothDeviceType.TOY
            0x0900 -> BluetoothDeviceType.HEALTH
            else -> BluetoothDeviceType.UNKNOWN
        }
    }
    
    /**
     * Analyze services from device class
     */
    private fun analyzeServices(deviceClass: Int): List<String> {
        val services = mutableListOf<String>()
        
        if (deviceClass and 0x100000 != 0) services.add("Information")
        if (deviceClass and 0x080000 != 0) services.add("Telephony")
        if (deviceClass and 0x040000 != 0) services.add("Audio")
        if (deviceClass and 0x020000 != 0) services.add("Object Transfer")
        if (deviceClass and 0x010000 != 0) services.add("Capturing")
        if (deviceClass and 0x008000 != 0) services.add("Rendering")
        if (deviceClass and 0x004000 != 0) services.add("Networking")
        if (deviceClass and 0x002000 != 0) services.add("Positioning")
        
        return services
    }
    
    /**
     * Calculate signal strength from RSSI
     */
    private fun calculateSignalStrength(rssi: Int): Int {
        return when {
            rssi >= -50 -> 100
            rssi >= -60 -> 80
            rssi >= -70 -> 60
            rssi >= -80 -> 40
            rssi >= -90 -> 20
            else -> 10
        }.coerceIn(0, 100)
    }
    
    /**
     * Analyze Bluetooth security
     */
    private fun analyzeBluetoothSecurity(device: BluetoothDevice): BluetoothSecurity {
        val bondState = device.bondState
        val securityLevel = when (bondState) {
            BluetoothDevice.BOND_BONDED -> SecurityLevel.HIGH
            BluetoothDevice.BOND_BONDING -> SecurityLevel.MEDIUM
            else -> SecurityLevel.LOW
        }
        
        return BluetoothSecurity(
            bondState = bondState,
            securityLevel = securityLevel,
            isEncrypted = bondState == BluetoothDevice.BOND_BONDED,
            authenticationRequired = bondState != BluetoothDevice.BOND_NONE
        )
    }
    
    /**
     * Check if device is connected
     */
    private fun isDeviceConnected(device: BluetoothDevice): Boolean {
        // This would require additional Bluetooth profile connections to check
        // For now, return false as a placeholder
        return false
    }
    
    /**
     * Get vendor from Bluetooth address
     */
    private fun getVendorFromAddress(address: String): String {
        val oui = address.take(8).replace(":", "").uppercase()
        return when {
            oui.startsWith("00:1B:63") -> "Apple"
            oui.startsWith("00:50:56") -> "VMware"
            oui.startsWith("08:00:27") -> "VirtualBox"
            oui.startsWith("00:0C:29") -> "VMware"
            oui.startsWith("00:15:5D") -> "Microsoft"
            oui.startsWith("B8:27:EB") -> "Raspberry Pi"
            oui.startsWith("DC:A6:32") -> "Raspberry Pi"
            oui.startsWith("E4:5F:01") -> "Raspberry Pi"
            else -> "Unknown"
        }
    }
    
    /**
     * Stop Bluetooth scanning
     */
    fun stopScanning() {
        if (bluetoothAdapter.isDiscovering) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) 
                == PackageManager.PERMISSION_GRANTED) {
                bluetoothAdapter.cancelDiscovery()
            }
        }
        _isScanning.value = false
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        try {
            context.unregisterReceiver(bluetoothReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unregister receiver", e)
        }
        stopScanning()
    }
}

/**
 * Bluetooth Device Information
 */
data class BluetoothDeviceInfo(
    val name: String,
    val address: String,
    val deviceType: BluetoothDeviceType,
    val services: List<String>,
    val signalStrength: Int,
    val rssi: Int,
    val isPaired: Boolean,
    val isConnected: Boolean,
    val security: BluetoothSecurity,
    val vendor: String,
    val lastSeen: Long,
    val bluetoothClass: Int
)

/**
 * Bluetooth Security Information
 */
data class BluetoothSecurity(
    val bondState: Int,
    val securityLevel: SecurityLevel,
    val isEncrypted: Boolean,
    val authenticationRequired: Boolean
)

/**
 * Bluetooth Device Types
 */
enum class BluetoothDeviceType {
    UNKNOWN, COMPUTER, PHONE, NETWORK, AUDIO_VIDEO, 
    PERIPHERAL, IMAGING, WEARABLE, TOY, HEALTH
}
