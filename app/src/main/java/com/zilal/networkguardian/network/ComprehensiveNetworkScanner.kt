package com.zilal.networkguardian.network

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.Vulnerability
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.*
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.coroutineContext

/**
 * Comprehensive Network Scanner with Real Hardware Integration
 * Performs NMAP-style scanning, device identification, vendor detection, and traffic monitoring
 */
@Singleton
class ComprehensiveNetworkScanner @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val deviceCache = ConcurrentHashMap<String, NetworkDevice>()
    private val deviceNames = ConcurrentHashMap<String, String>() // Custom device names
    
    private val _discoveredDevices = MutableStateFlow<List<NetworkDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<NetworkDevice>> = _discoveredDevices.asStateFlow()
    
    private val _scanState = MutableStateFlow<ScanState>(ScanState.Idle)
    val scanState: StateFlow<ScanState> = _scanState.asStateFlow()
    
    private val _scanProgress = MutableStateFlow(0f)
    val scanProgress: StateFlow<Float> = _scanProgress.asStateFlow()
    
    private val _newDeviceDetected = MutableStateFlow<NetworkDevice?>(null)
    val newDeviceDetected: StateFlow<NetworkDevice?> = _newDeviceDetected.asStateFlow()
    
    companion object {
        private const val TAG = "ComprehensiveNetworkScanner"
        
        // Common ports for device identification
        private val COMMON_PORTS = listOf(
            21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, // Standard services
            135, 139, 445, 3389, // Windows
            548, 631, 5353, // macOS
            111, 2049, // Linux/Unix
            1900, 5000, 8080, 8443, // UPnP and web services
            554, 8554, // RTSP (cameras)
            1723, 4500, 500, // VPN
            5060, 5061, // SIP
            6667, 6697, // IRC
            25565, // Minecraft
            3306, 5432, 1433, // Databases
            6379, 11211, // Cache systems
            9100, 515 // Printers
        )
        
        // Device identification patterns
        private val DEVICE_PATTERNS = mapOf(
            "Windows" to listOf(135, 139, 445, 3389),
            "macOS" to listOf(548, 631, 5353),
            "Linux" to listOf(22, 111, 2049),
            "Router" to listOf(80, 443, 23, 8080),
            "Printer" to listOf(9100, 515, 631),
            "Camera" to listOf(554, 8554, 80),
            "Smart TV" to listOf(1900, 8080, 7001),
            "Gaming Console" to listOf(25565, 3074, 53),
            "NAS" to listOf(80, 443, 548, 2049),
            "IoT Device" to listOf(1900, 5353, 8080)
        )
    }
    
    /**
     * Start comprehensive network scanning with real hardware detection
     */
    fun startComprehensiveNetworkScan() {
        if (_scanState.value is ScanState.Scanning) {
            Log.w(TAG, "Comprehensive scan already in progress")
            return
        }
        
        if (!isNetworkAvailable()) {
            _scanState.value = ScanState.Error("No network connection available")
            return
        }
        
        _scanState.value = ScanState.Scanning
        _scanProgress.value = 0f
        
        scope.launch {
            try {
                performComprehensiveNetworkScan()
            } catch (e: Exception) {
                Log.e(TAG, "Comprehensive network scan failed", e)
                _scanState.value = ScanState.Error(e.message ?: "Scan failed")
                analyticsManager.trackError("comprehensive_scan_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Perform comprehensive network scanning
     */
    private suspend fun performComprehensiveNetworkScan() {
        val networkInfo = getNetworkInfo() ?: run {
            _scanState.value = ScanState.Error("Unable to determine network configuration")
            return
        }
        
        val (localIp, subnetMask) = networkInfo
        val networkAddress = getNetworkAddress(localIp, subnetMask)
        
        Log.i(TAG, "Starting comprehensive scan on network: $networkAddress")
        
        // Phase 1: ICMP Ping Sweep (30% of progress)
        val activeHosts = performICMPPingSweep(networkAddress)
        _scanProgress.value = 0.3f
        
        // Phase 2: ARP Scan for additional discovery (20% of progress)
        val arpHosts = performARPScan(networkAddress)
        val allHosts = (activeHosts + arpHosts).distinct()
        _scanProgress.value = 0.5f
        
        // Phase 3: Port scanning and service detection (30% of progress)
        performPortScanningAndServiceDetection(allHosts)
        _scanProgress.value = 0.8f
        
        // Phase 4: Device identification and vendor lookup (20% of progress)
        performDeviceIdentificationAndVendorLookup()
        _scanProgress.value = 1.0f
        
        _scanState.value = ScanState.Completed(deviceCache.size)
        
        analyticsManager.trackEvent(
            eventName = "comprehensive_network_scan_completed",
            properties = mapOf(
                "devices_found" to deviceCache.size,
                "active_hosts" to activeHosts.size,
                "arp_hosts" to arpHosts.size,
                "timestamp" to System.currentTimeMillis()
            )
        )
        
        Log.i(TAG, "Comprehensive network scan completed: ${deviceCache.size} devices found")
    }
    
    /**
     * Perform ICMP ping sweep to discover active hosts
     */
    private suspend fun performICMPPingSweep(networkAddress: String): List<String> {
        val activeHosts = mutableListOf<String>()
        val baseAddress = networkAddress.substringBeforeLast(".")
        
        Log.d(TAG, "Performing ICMP ping sweep on $baseAddress.1-254")
        
        // Use coroutines for parallel ping scanning
        val jobs = (1..254).map { i ->
            scope.async {
                val hostIp = "$baseAddress.$i"
                if (performICMPPing(hostIp)) {
                    synchronized(activeHosts) {
                        activeHosts.add(hostIp)
                    }
                    Log.d(TAG, "ICMP ping successful: $hostIp")
                }
            }
        }
        
        jobs.awaitAll()
        return activeHosts
    }
    
    /**
     * Perform ICMP ping to a specific host
     */
    private suspend fun performICMPPing(hostIp: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val address = InetAddress.getByName(hostIp)
                address.isReachable(3000) // 3 second timeout
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * Perform ARP scan for additional device discovery
     */
    private suspend fun performARPScan(networkAddress: String): List<String> {
        return withContext(Dispatchers.IO) {
            val arpHosts = mutableListOf<String>()
            
            try {
                // Read ARP table from system
                val process = Runtime.getRuntime().exec("cat /proc/net/arp")
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    line?.let { arpLine ->
                        val parts = arpLine.split("\\s+".toRegex())
                        if (parts.size >= 6 && parts[0].matches(Regex("\\d+\\.\\d+\\.\\d+\\.\\d+"))) {
                            val ip = parts[0]
                            val mac = parts[3]
                            
                            // Check if it's in our network range
                            if (ip.startsWith(networkAddress.substringBeforeLast("."))) {
                                arpHosts.add(ip)
                                Log.d(TAG, "ARP entry found: $ip -> $mac")
                            }
                        }
                    }
                }
                
                reader.close()
                process.waitFor()
                
            } catch (e: Exception) {
                Log.w(TAG, "ARP scan failed, using alternative method", e)
                // Fallback to network interface enumeration
                return@withContext performNetworkInterfaceEnumeration()
            }
            
            arpHosts
        }
    }
    
    /**
     * Fallback method for device discovery using network interfaces
     */
    private fun performNetworkInterfaceEnumeration(): List<String> {
        val hosts = mutableListOf<String>()
        
        try {
            val networkInterfaces = NetworkInterface.getNetworkInterfaces()
            
            while (networkInterfaces.hasMoreElements()) {
                val networkInterface = networkInterfaces.nextElement()
                
                if (!networkInterface.isLoopback && networkInterface.isUp) {
                    val addresses = networkInterface.inetAddresses
                    
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        
                        if (address is Inet4Address && !address.isLoopbackAddress) {
                            hosts.add(address.hostAddress ?: "")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Network interface enumeration failed", e)
        }
        
        return hosts
    }
    
    /**
     * Perform port scanning and service detection on discovered hosts
     */
    private suspend fun performPortScanningAndServiceDetection(hosts: List<String>) {
        Log.d(TAG, "Performing port scanning on ${hosts.size} hosts")
        
        hosts.forEachIndexed { index, hostIp ->
            if (!coroutineContext.isActive) return
            
            val device = scanHostPorts(hostIp)
            deviceCache[hostIp] = device
            _discoveredDevices.value = deviceCache.values.toList()
            
            // Check if this is a new device
            if (!deviceNames.containsKey(hostIp)) {
                _newDeviceDetected.value = device
            }
            
            _scanProgress.value = 0.5f + ((index + 1).toFloat() / hosts.size) * 0.3f
        }
    }
    
    /**
     * Scan ports on a specific host
     */
    private suspend fun scanHostPorts(hostIp: String): NetworkDevice {
        val openPorts = mutableListOf<Int>()
        val services = mutableMapOf<Int, String>()
        
        // Scan common ports in parallel
        val portJobs = COMMON_PORTS.map { port ->
            scope.async {
                if (isPortOpen(hostIp, port)) {
                    synchronized(openPorts) {
                        openPorts.add(port)
                        services[port] = identifyService(port)
                    }
                }
            }
        }
        
        portJobs.awaitAll()
        
        // Get device information
        val hostname = getHostname(hostIp)
        val macAddress = getMacAddress(hostIp)
        val deviceType = identifyDeviceType(openPorts, hostname)
        val manufacturer = identifyManufacturer(macAddress)
        val customName = deviceNames[hostIp]
        
        return NetworkDevice(
            ipAddress = hostIp,
            macAddress = macAddress,
            hostname = hostname,
            deviceType = deviceType,
            manufacturer = manufacturer,
            operatingSystem = identifyOperatingSystem(openPorts),
            isOnline = true,
            openPorts = openPorts,
            services = services,
            lastSeen = System.currentTimeMillis(),
            signalStrength = -1,
            customName = customName,
            vulnerabilities = mutableListOf()
        )
    }
    
    /**
     * Check if a port is open on a host
     */
    private suspend fun isPortOpen(hostIp: String, port: Int): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val socket = Socket()
                socket.connect(InetSocketAddress(hostIp, port), 2000) // 2 second timeout
                socket.close()
                true
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * Get hostname for an IP address
     */
    private suspend fun getHostname(ipAddress: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                val address = InetAddress.getByName(ipAddress)
                val hostname = address.hostName
                if (hostname != ipAddress) hostname else null
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * Get MAC address for an IP address
     */
    private suspend fun getMacAddress(ipAddress: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                // Try to get MAC from ARP table
                val process = Runtime.getRuntime().exec("cat /proc/net/arp")
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    line?.let { arpLine ->
                        val parts = arpLine.split("\\s+".toRegex())
                        if (parts.size >= 4 && parts[0] == ipAddress) {
                            reader.close()
                            return@withContext parts[3].takeIf { it != "00:00:00:00:00:00" }
                        }
                    }
                }
                
                reader.close()
                process.waitFor()
                null
                
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get MAC address for $ipAddress", e)
                null
            }
        }
    }
    
    /**
     * Check if network is available
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val network = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
            
            networkCapabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get network information
     */
    private fun getNetworkInfo(): Pair<String, String>? {
        try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val dhcpInfo = wifiManager.dhcpInfo
            
            if (dhcpInfo.ipAddress == 0) return null
            
            val ipAddress = String.format(
                "%d.%d.%d.%d",
                dhcpInfo.ipAddress and 0xff,
                dhcpInfo.ipAddress shr 8 and 0xff,
                dhcpInfo.ipAddress shr 16 and 0xff,
                dhcpInfo.ipAddress shr 24 and 0xff
            )
            
            return Pair(ipAddress, "*************")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get network info", e)
            return null
        }
    }
    
    /**
     * Get network address from IP and subnet mask
     */
    private fun getNetworkAddress(ipAddress: String, subnetMask: String): String {
        return ipAddress.substringBeforeLast(".") + ".0"
    }

    /**
     * Identify service running on a port
     */
    private fun identifyService(port: Int): String {
        return when (port) {
            21 -> "FTP"
            22 -> "SSH"
            23 -> "Telnet"
            25 -> "SMTP"
            53 -> "DNS"
            80 -> "HTTP"
            110 -> "POP3"
            143 -> "IMAP"
            443 -> "HTTPS"
            993 -> "IMAPS"
            995 -> "POP3S"
            135 -> "RPC Endpoint Mapper"
            139 -> "NetBIOS Session"
            445 -> "SMB"
            3389 -> "RDP"
            548 -> "AFP"
            631 -> "IPP"
            5353 -> "mDNS"
            111 -> "RPC"
            2049 -> "NFS"
            1900 -> "UPnP"
            5000 -> "UPnP"
            8080 -> "HTTP Proxy"
            8443 -> "HTTPS Alt"
            554 -> "RTSP"
            8554 -> "RTSP Alt"
            1723 -> "PPTP"
            4500 -> "IPSec NAT-T"
            500 -> "IPSec"
            5060 -> "SIP"
            5061 -> "SIP TLS"
            6667 -> "IRC"
            6697 -> "IRC SSL"
            25565 -> "Minecraft"
            3306 -> "MySQL"
            5432 -> "PostgreSQL"
            1433 -> "SQL Server"
            6379 -> "Redis"
            11211 -> "Memcached"
            9100 -> "JetDirect"
            515 -> "LPD"
            else -> "Unknown Service"
        }
    }

    /**
     * Identify device type based on open ports and hostname
     */
    private fun identifyDeviceType(openPorts: List<Int>, hostname: String?): NetworkDevice.DeviceType {
        // Check hostname patterns first
        hostname?.let { name ->
            when {
                name.contains("router", ignoreCase = true) -> return NetworkDevice.DeviceType.ROUTER
                name.contains("android", ignoreCase = true) -> return NetworkDevice.DeviceType.MOBILE
                name.contains("iphone", ignoreCase = true) -> return NetworkDevice.DeviceType.MOBILE
                name.contains("ipad", ignoreCase = true) -> return NetworkDevice.DeviceType.MOBILE
                name.contains("macbook", ignoreCase = true) -> return NetworkDevice.DeviceType.COMPUTER
                name.contains("windows", ignoreCase = true) -> return NetworkDevice.DeviceType.COMPUTER
                name.contains("printer", ignoreCase = true) -> return NetworkDevice.DeviceType.PRINTER
                name.contains("tv", ignoreCase = true) -> return NetworkDevice.DeviceType.SMART_TV
                name.contains("camera", ignoreCase = true) -> return NetworkDevice.DeviceType.CAMERA
                name.contains("nas", ignoreCase = true) -> return NetworkDevice.DeviceType.NAS
            }
        }

        // Check port patterns
        for ((deviceType, ports) in DEVICE_PATTERNS) {
            val matchingPorts = openPorts.intersect(ports.toSet())
            if (matchingPorts.isNotEmpty()) {
                return when (deviceType) {
                    "Windows" -> NetworkDevice.DeviceType.COMPUTER
                    "macOS" -> NetworkDevice.DeviceType.COMPUTER
                    "Linux" -> NetworkDevice.DeviceType.SERVER
                    "Router" -> NetworkDevice.DeviceType.ROUTER
                    "Printer" -> NetworkDevice.DeviceType.PRINTER
                    "Camera" -> NetworkDevice.DeviceType.CAMERA
                    "Smart TV" -> NetworkDevice.DeviceType.SMART_TV
                    "Gaming Console" -> NetworkDevice.DeviceType.GAMING_CONSOLE
                    "NAS" -> NetworkDevice.DeviceType.NAS
                    "IoT Device" -> NetworkDevice.DeviceType.IOT_DEVICE
                    else -> NetworkDevice.DeviceType.UNKNOWN
                }
            }
        }

        return NetworkDevice.DeviceType.UNKNOWN
    }

    /**
     * Identify operating system based on open ports
     */
    private fun identifyOperatingSystem(openPorts: List<Int>): String {
        return when {
            openPorts.containsAll(listOf(135, 139, 445)) -> "Windows"
            openPorts.contains(3389) -> "Windows Server"
            openPorts.containsAll(listOf(548, 631)) -> "macOS"
            openPorts.contains(22) && openPorts.contains(111) -> "Linux/Unix"
            openPorts.contains(22) -> "Linux"
            openPorts.contains(23) && openPorts.contains(80) -> "Router OS"
            else -> "Unknown"
        }
    }

    /**
     * Identify manufacturer from MAC address
     */
    private fun identifyManufacturer(macAddress: String?): String {
        if (macAddress == null) return "Unknown"

        val oui = macAddress.take(8).replace(":", "").uppercase()
        return when {
            oui.startsWith("00:1B:63") -> "Apple Inc."
            oui.startsWith("00:50:56") -> "VMware Inc."
            oui.startsWith("08:00:27") -> "Oracle VirtualBox"
            oui.startsWith("00:0C:29") -> "VMware Inc."
            oui.startsWith("00:15:5D") -> "Microsoft Corporation"
            oui.startsWith("B8:27:EB") -> "Raspberry Pi Foundation"
            oui.startsWith("DC:A6:32") -> "Raspberry Pi Foundation"
            oui.startsWith("E4:5F:01") -> "Raspberry Pi Foundation"
            oui.startsWith("00:16:3E") -> "Xensource Inc."
            oui.startsWith("52:54:00") -> "QEMU"
            oui.startsWith("00:1A:79") -> "Samsung Electronics"
            oui.startsWith("28:6A:BA") -> "Samsung Electronics"
            oui.startsWith("AC:5F:3E") -> "Samsung Electronics"
            oui.startsWith("00:26:BB") -> "Apple Inc."
            oui.startsWith("3C:15:C2") -> "Apple Inc."
            oui.startsWith("A4:5E:60") -> "Apple Inc."
            oui.startsWith("00:1F:F3") -> "Apple Inc."
            oui.startsWith("00:25:00") -> "Apple Inc."
            oui.startsWith("28:CF:E9") -> "Apple Inc."
            oui.startsWith("A8:96:75") -> "Apple Inc."
            oui.startsWith("00:23:DF") -> "Apple Inc."
            oui.startsWith("00:26:4A") -> "Apple Inc."
            oui.startsWith("04:0C:CE") -> "Apple Inc."
            oui.startsWith("00:03:93") -> "Apple Inc."
            oui.startsWith("00:17:F2") -> "Apple Inc."
            oui.startsWith("00:1C:B3") -> "Apple Inc."
            oui.startsWith("00:1E:C2") -> "Apple Inc."
            oui.startsWith("00:21:E9") -> "Apple Inc."
            oui.startsWith("00:23:12") -> "Apple Inc."
            oui.startsWith("00:24:36") -> "Apple Inc."
            oui.startsWith("00:25:BC") -> "Apple Inc."
            oui.startsWith("28:E0:2C") -> "Apple Inc."
            oui.startsWith("2C:B4:3A") -> "Apple Inc."
            oui.startsWith("30:90:AB") -> "Apple Inc."
            oui.startsWith("34:15:9E") -> "Apple Inc."
            oui.startsWith("34:36:3B") -> "Apple Inc."
            oui.startsWith("38:C9:86") -> "Apple Inc."
            oui.startsWith("3C:07:54") -> "Apple Inc."
            oui.startsWith("40:B3:95") -> "Apple Inc."
            oui.startsWith("44:D8:84") -> "Apple Inc."
            oui.startsWith("48:74:6E") -> "Apple Inc."
            oui.startsWith("4C:74:03") -> "Apple Inc."
            oui.startsWith("50:EA:D6") -> "Apple Inc."
            oui.startsWith("54:72:4F") -> "Apple Inc."
            oui.startsWith("58:55:CA") -> "Apple Inc."
            oui.startsWith("5C:59:48") -> "Apple Inc."
            oui.startsWith("5C:95:AE") -> "Apple Inc."
            oui.startsWith("60:03:08") -> "Apple Inc."
            oui.startsWith("60:C5:47") -> "Apple Inc."
            oui.startsWith("64:20:9F") -> "Apple Inc."
            oui.startsWith("68:AB:BC") -> "Apple Inc."
            oui.startsWith("6C:72:20") -> "Apple Inc."
            oui.startsWith("6C:94:66") -> "Apple Inc."
            oui.startsWith("70:11:24") -> "Apple Inc."
            oui.startsWith("70:56:81") -> "Apple Inc."
            oui.startsWith("78:31:C1") -> "Apple Inc."
            oui.startsWith("78:4F:43") -> "Apple Inc."
            oui.startsWith("7C:6D:62") -> "Apple Inc."
            oui.startsWith("7C:C3:A1") -> "Apple Inc."
            oui.startsWith("7C:D1:C3") -> "Apple Inc."
            oui.startsWith("80:92:9F") -> "Apple Inc."
            oui.startsWith("84:38:35") -> "Apple Inc."
            oui.startsWith("84:FC:FE") -> "Apple Inc."
            oui.startsWith("88:1F:A1") -> "Apple Inc."
            oui.startsWith("8C:58:77") -> "Apple Inc."
            oui.startsWith("8C:7C:92") -> "Apple Inc."
            oui.startsWith("90:72:40") -> "Apple Inc."
            oui.startsWith("90:B0:ED") -> "Apple Inc."
            oui.startsWith("94:E9:79") -> "Apple Inc."
            oui.startsWith("98:03:D8") -> "Apple Inc."
            oui.startsWith("9C:04:EB") -> "Apple Inc."
            oui.startsWith("9C:29:3F") -> "Apple Inc."
            oui.startsWith("9C:F3:87") -> "Apple Inc."
            oui.startsWith("A0:99:9B") -> "Apple Inc."
            oui.startsWith("A4:B1:97") -> "Apple Inc."
            oui.startsWith("A8:20:66") -> "Apple Inc."
            oui.startsWith("A8:51:AB") -> "Apple Inc."
            oui.startsWith("A8:88:08") -> "Apple Inc."
            oui.startsWith("AC:1F:74") -> "Apple Inc."
            oui.startsWith("AC:3C:0B") -> "Apple Inc."
            oui.startsWith("AC:87:A3") -> "Apple Inc."
            oui.startsWith("B0:65:BD") -> "Apple Inc."
            oui.startsWith("B4:18:D1") -> "Apple Inc."
            oui.startsWith("B8:09:8A") -> "Apple Inc."
            oui.startsWith("B8:C7:5D") -> "Apple Inc."
            oui.startsWith("B8:E8:56") -> "Apple Inc."
            oui.startsWith("BC:52:B7") -> "Apple Inc."
            oui.startsWith("BC:67:1C") -> "Apple Inc."
            oui.startsWith("BC:92:6B") -> "Apple Inc."
            oui.startsWith("C0:9A:D0") -> "Apple Inc."
            oui.startsWith("C4:B3:01") -> "Apple Inc."
            oui.startsWith("C8:BC:C8") -> "Apple Inc."
            oui.startsWith("C8:E0:EB") -> "Apple Inc."
            oui.startsWith("CC:08:8D") -> "Apple Inc."
            oui.startsWith("CC:25:EF") -> "Apple Inc."
            oui.startsWith("D0:23:DB") -> "Apple Inc."
            oui.startsWith("D0:A6:37") -> "Apple Inc."
            oui.startsWith("D4:9A:20") -> "Apple Inc."
            oui.startsWith("D8:30:62") -> "Apple Inc."
            oui.startsWith("D8:A2:5E") -> "Apple Inc."
            oui.startsWith("DC:2B:2A") -> "Apple Inc."
            oui.startsWith("DC:56:E7") -> "Apple Inc."
            oui.startsWith("DC:86:D8") -> "Apple Inc."
            oui.startsWith("DC:9B:9C") -> "Apple Inc."
            oui.startsWith("E0:AC:CB") -> "Apple Inc."
            oui.startsWith("E0:B9:BA") -> "Apple Inc."
            oui.startsWith("E4:8B:7F") -> "Apple Inc."
            oui.startsWith("E4:CE:8F") -> "Apple Inc."
            oui.startsWith("E8:8D:28") -> "Apple Inc."
            oui.startsWith("EC:35:86") -> "Apple Inc."
            oui.startsWith("F0:18:98") -> "Apple Inc."
            oui.startsWith("F0:B4:79") -> "Apple Inc."
            oui.startsWith("F0:DB:E2") -> "Apple Inc."
            oui.startsWith("F4:0F:24") -> "Apple Inc."
            oui.startsWith("F4:1B:A1") -> "Apple Inc."
            oui.startsWith("F4:37:B7") -> "Apple Inc."
            oui.startsWith("F4:F1:5A") -> "Apple Inc."
            oui.startsWith("F8:1E:DF") -> "Apple Inc."
            oui.startsWith("F8:27:93") -> "Apple Inc."
            oui.startsWith("F8:2D:7C") -> "Apple Inc."
            oui.startsWith("F8:4F:AD") -> "Apple Inc."
            oui.startsWith("FC:25:3F") -> "Apple Inc."
            oui.startsWith("FC:E9:98") -> "Apple Inc."
            else -> "Unknown Manufacturer"
        }
    }

    /**
     * Perform device identification and vendor lookup
     */
    private suspend fun performDeviceIdentificationAndVendorLookup() {
        deviceCache.values.forEach { device ->
            if (!coroutineContext.isActive) return

            // Enhanced device identification
            val enhancedDevice = device.copy(
                manufacturer = identifyManufacturer(device.macAddress),
                operatingSystem = identifyOperatingSystem(device.openPorts)
            )

            deviceCache[device.ipAddress] = enhancedDevice
            _discoveredDevices.value = deviceCache.values.toList()
        }
    }

    /**
     * Save custom device name
     */
    fun saveDeviceName(ipAddress: String, customName: String) {
        deviceNames[ipAddress] = customName

        // Update device in cache
        deviceCache[ipAddress]?.let { device ->
            val updatedDevice = device.copy(customName = customName)
            deviceCache[ipAddress] = updatedDevice
            _discoveredDevices.value = deviceCache.values.toList()
        }

        analyticsManager.trackEvent(
            eventName = "device_name_saved",
            properties = mapOf(
                "ip_address" to ipAddress,
                "custom_name" to customName,
                "timestamp" to System.currentTimeMillis()
            )
        )
    }

    /**
     * Get device by IP address
     */
    fun getDevice(ipAddress: String): NetworkDevice? {
        return deviceCache[ipAddress]
    }

    /**
     * Stop scanning
     */
    fun stopScanning() {
        _scanState.value = ScanState.Idle
        _scanProgress.value = 0f
    }
}
