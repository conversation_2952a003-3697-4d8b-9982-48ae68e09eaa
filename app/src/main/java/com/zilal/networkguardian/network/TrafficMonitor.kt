package com.zilal.networkguardian.network

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.model.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.InetAddress
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Advanced Traffic Monitor for real-time network traffic analysis
 * Monitors device traffic, applications, websites, and network behavior
 */
@Singleton
class TrafficMonitor @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val deviceTraffic = ConcurrentHashMap<String, NetworkTraffic>()
    private val activeConnections = ConcurrentHashMap<String, MutableList<NetworkConnection>>()
    
    private val _monitoringState = MutableStateFlow<MonitoringState>(MonitoringState.Stopped)
    val monitoringState: StateFlow<MonitoringState> = _monitoringState.asStateFlow()
    
    private val _deviceTrafficData = MutableStateFlow<Map<String, NetworkTraffic>>(emptyMap())
    val deviceTrafficData: StateFlow<Map<String, NetworkTraffic>> = _deviceTrafficData.asStateFlow()
    
    private var monitoringJob: Job? = null
    
    companion object {
        private const val TAG = "TrafficMonitor"
        private const val MONITORING_INTERVAL = 5000L // 5 seconds
        
        // Common application patterns
        private val APPLICATION_PATTERNS = mapOf(
            "chrome" to ApplicationCategory.BROWSER,
            "firefox" to ApplicationCategory.BROWSER,
            "safari" to ApplicationCategory.BROWSER,
            "whatsapp" to ApplicationCategory.MESSAGING,
            "telegram" to ApplicationCategory.MESSAGING,
            "facebook" to ApplicationCategory.SOCIAL_MEDIA,
            "instagram" to ApplicationCategory.SOCIAL_MEDIA,
            "twitter" to ApplicationCategory.SOCIAL_MEDIA,
            "youtube" to ApplicationCategory.STREAMING,
            "netflix" to ApplicationCategory.STREAMING,
            "spotify" to ApplicationCategory.STREAMING,
            "zoom" to ApplicationCategory.PRODUCTIVITY,
            "teams" to ApplicationCategory.PRODUCTIVITY,
            "skype" to ApplicationCategory.MESSAGING
        )
        
        // Website categories
        private val WEBSITE_CATEGORIES = mapOf(
            "google.com" to WebsiteCategory.SEARCH,
            "bing.com" to WebsiteCategory.SEARCH,
            "facebook.com" to WebsiteCategory.SOCIAL_MEDIA,
            "instagram.com" to WebsiteCategory.SOCIAL_MEDIA,
            "twitter.com" to WebsiteCategory.SOCIAL_MEDIA,
            "youtube.com" to WebsiteCategory.STREAMING,
            "netflix.com" to WebsiteCategory.STREAMING,
            "amazon.com" to WebsiteCategory.SHOPPING,
            "ebay.com" to WebsiteCategory.SHOPPING,
            "github.com" to WebsiteCategory.TECHNOLOGY,
            "stackoverflow.com" to WebsiteCategory.TECHNOLOGY,
            "reddit.com" to WebsiteCategory.ENTERTAINMENT,
            "news.com" to WebsiteCategory.NEWS,
            "cnn.com" to WebsiteCategory.NEWS,
            "bbc.com" to WebsiteCategory.NEWS
        )
    }
    
    /**
     * Start traffic monitoring for a specific device
     */
    fun startMonitoring(deviceIp: String) {
        if (_monitoringState.value is MonitoringState.Monitoring) {
            Log.w(TAG, "Traffic monitoring already in progress")
            return
        }
        
        _monitoringState.value = MonitoringState.Monitoring(deviceIp)
        
        monitoringJob = scope.launch {
            try {
                monitorDeviceTraffic(deviceIp)
            } catch (e: Exception) {
                Log.e(TAG, "Traffic monitoring failed", e)
                _monitoringState.value = MonitoringState.Error(e.message ?: "Monitoring failed")
                analyticsManager.trackError("traffic_monitoring_error", e.message ?: "Unknown error")
            }
        }
        
        analyticsManager.trackEvent(
            eventName = "traffic_monitoring_started",
            properties = mapOf(
                "device_ip" to deviceIp,
                "timestamp" to System.currentTimeMillis()
            )
        )
    }
    
    /**
     * Monitor traffic for all discovered devices
     */
    fun startMonitoringAllDevices(deviceIps: List<String>) {
        if (_monitoringState.value is MonitoringState.Monitoring) {
            Log.w(TAG, "Traffic monitoring already in progress")
            return
        }
        
        _monitoringState.value = MonitoringState.MonitoringAll(deviceIps)
        
        monitoringJob = scope.launch {
            try {
                deviceIps.forEach { deviceIp ->
                    launch {
                        monitorDeviceTraffic(deviceIp)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Multi-device traffic monitoring failed", e)
                _monitoringState.value = MonitoringState.Error(e.message ?: "Monitoring failed")
                analyticsManager.trackError("multi_device_monitoring_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Monitor traffic for a specific device
     */
    private suspend fun monitorDeviceTraffic(deviceIp: String) {
        while (scope.isActive && _monitoringState.value !is MonitoringState.Stopped) {
            try {
                // Get network connections for the device
                val connections = getNetworkConnections(deviceIp)
                activeConnections[deviceIp] = connections.toMutableList()
                
                // Analyze traffic patterns
                val trafficData = analyzeTrafficPatterns(deviceIp, connections)
                deviceTraffic[deviceIp] = trafficData
                
                // Update state
                _deviceTrafficData.value = deviceTraffic.toMap()
                
                Log.d(TAG, "Traffic data updated for $deviceIp: ${connections.size} connections")
                
            } catch (e: Exception) {
                Log.w(TAG, "Failed to monitor traffic for $deviceIp", e)
            }
            
            delay(MONITORING_INTERVAL)
        }
    }
    
    /**
     * Get network connections for a device
     */
    private suspend fun getNetworkConnections(deviceIp: String): List<NetworkConnection> {
        return withContext(Dispatchers.IO) {
            val connections = mutableListOf<NetworkConnection>()
            
            try {
                // Read network connections from /proc/net/tcp and /proc/net/udp
                val tcpConnections = readConnectionsFromProc("/proc/net/tcp", "TCP")
                val udpConnections = readConnectionsFromProc("/proc/net/udp", "UDP")
                
                connections.addAll(tcpConnections.filter { it.remoteIp == deviceIp || it.localPort != 0 })
                connections.addAll(udpConnections.filter { it.remoteIp == deviceIp || it.localPort != 0 })
                
                // Enhance connections with additional information
                connections.forEach { connection ->
                    enhanceConnectionInfo(connection)
                }
                
            } catch (e: Exception) {
                Log.w(TAG, "Failed to read network connections", e)
            }
            
            connections
        }
    }
    
    /**
     * Read connections from /proc/net files
     */
    private fun readConnectionsFromProc(procFile: String, protocol: String): List<NetworkConnection> {
        val connections = mutableListOf<NetworkConnection>()
        
        try {
            val process = Runtime.getRuntime().exec("cat $procFile")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            
            var line: String?
            var isFirstLine = true
            
            while (reader.readLine().also { line = it } != null) {
                if (isFirstLine) {
                    isFirstLine = false
                    continue // Skip header
                }
                
                line?.let { connectionLine ->
                    parseConnectionLine(connectionLine, protocol)?.let { connection ->
                        connections.add(connection)
                    }
                }
            }
            
            reader.close()
            process.waitFor()
            
        } catch (e: Exception) {
            Log.w(TAG, "Failed to read $procFile", e)
        }
        
        return connections
    }
    
    /**
     * Parse a connection line from /proc/net files
     */
    private fun parseConnectionLine(line: String, protocol: String): NetworkConnection? {
        try {
            val parts = line.trim().split("\\s+".toRegex())
            if (parts.size < 4) return null
            
            // Parse local address and port
            val localAddress = parts[1].split(":")
            if (localAddress.size != 2) return null
            
            val localIp = hexToIp(localAddress[0])
            val localPort = localAddress[1].toInt(16)
            
            // Parse remote address and port
            val remoteAddress = parts[2].split(":")
            if (remoteAddress.size != 2) return null
            
            val remoteIp = hexToIp(remoteAddress[0])
            val remotePort = remoteAddress[1].toInt(16)
            
            // Parse connection state
            val stateHex = parts[3]
            val state = parseConnectionState(stateHex)
            
            return NetworkConnection(
                localPort = localPort,
                remoteIp = remoteIp,
                remotePort = remotePort,
                protocol = protocol,
                state = state,
                lastActivity = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse connection line: $line", e)
            return null
        }
    }
    
    /**
     * Convert hex IP address to dotted decimal
     */
    private fun hexToIp(hexIp: String): String {
        return try {
            val ip = hexIp.toLong(16)
            "${(ip and 0xFF)}.${(ip shr 8 and 0xFF)}.${(ip shr 16 and 0xFF)}.${(ip shr 24 and 0xFF)}"
        } catch (e: Exception) {
            "0.0.0.0"
        }
    }
    
    /**
     * Parse connection state from hex value
     */
    private fun parseConnectionState(stateHex: String): ConnectionState {
        return try {
            when (stateHex.toInt(16)) {
                1 -> ConnectionState.ESTABLISHED
                2 -> ConnectionState.SYN_SENT
                3 -> ConnectionState.SYN_RECV
                4 -> ConnectionState.FIN_WAIT1
                5 -> ConnectionState.FIN_WAIT2
                6 -> ConnectionState.TIME_WAIT
                7 -> ConnectionState.CLOSE
                8 -> ConnectionState.CLOSE_WAIT
                9 -> ConnectionState.LAST_ACK
                10 -> ConnectionState.LISTEN
                11 -> ConnectionState.CLOSING
                else -> ConnectionState.UNKNOWN
            }
        } catch (e: Exception) {
            ConnectionState.UNKNOWN
        }
    }
    
    /**
     * Enhance connection information with hostname and application data
     */
    private suspend fun enhanceConnectionInfo(connection: NetworkConnection) {
        try {
            // Resolve hostname
            val hostname = withContext(Dispatchers.IO) {
                try {
                    InetAddress.getByName(connection.remoteIp).hostName
                } catch (e: Exception) {
                    null
                }
            }
            
            // Update connection with hostname
            if (hostname != null && hostname != connection.remoteIp) {
                // This would require modifying the connection object
                // For now, we'll log the information
                Log.d(TAG, "Resolved ${connection.remoteIp} to $hostname")
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "Failed to enhance connection info", e)
        }
    }
    
    /**
     * Analyze traffic patterns for a device
     */
    private fun analyzeTrafficPatterns(deviceIp: String, connections: List<NetworkConnection>): NetworkTraffic {
        val websites = mutableListOf<WebsiteAccess>()
        val applications = mutableListOf<ApplicationTraffic>()
        val protocols = mutableMapOf<String, Long>()
        
        // Analyze connections
        connections.forEach { connection ->
            // Count protocol usage
            protocols[connection.protocol] = protocols.getOrDefault(connection.protocol, 0) + 1
            
            // Identify websites
            connection.remoteHostname?.let { hostname ->
                val domain = extractDomain(hostname)
                val category = WEBSITE_CATEGORIES[domain] ?: WebsiteCategory.UNKNOWN
                val isSecure = connection.remotePort == 443
                
                websites.add(
                    WebsiteAccess(
                        domain = domain,
                        accessCount = 1,
                        category = category,
                        isSecure = isSecure
                    )
                )
            }
        }
        
        // Group and aggregate website data
        val aggregatedWebsites = websites.groupBy { it.domain }
            .map { (domain, accesses) ->
                WebsiteAccess(
                    domain = domain,
                    accessCount = accesses.size,
                    category = accesses.first().category,
                    isSecure = accesses.any { it.isSecure }
                )
            }
        
        return NetworkTraffic(
            deviceIp = deviceIp,
            connections = connections,
            applications = applications,
            websites = aggregatedWebsites,
            protocols = protocols
        )
    }
    
    /**
     * Extract domain from hostname
     */
    private fun extractDomain(hostname: String): String {
        return try {
            val parts = hostname.split(".")
            if (parts.size >= 2) {
                "${parts[parts.size - 2]}.${parts[parts.size - 1]}"
            } else {
                hostname
            }
        } catch (e: Exception) {
            hostname
        }
    }
    
    /**
     * Get traffic data for a specific device
     */
    fun getDeviceTraffic(deviceIp: String): NetworkTraffic? {
        return deviceTraffic[deviceIp]
    }
    
    /**
     * Stop traffic monitoring
     */
    fun stopMonitoring() {
        monitoringJob?.cancel()
        _monitoringState.value = MonitoringState.Stopped
        
        analyticsManager.trackEvent(
            eventName = "traffic_monitoring_stopped",
            properties = mapOf(
                "timestamp" to System.currentTimeMillis()
            )
        )
    }
}

/**
 * Traffic monitoring states
 */
sealed class MonitoringState {
    object Stopped : MonitoringState()
    data class Monitoring(val deviceIp: String) : MonitoringState()
    data class MonitoringAll(val deviceIps: List<String>) : MonitoringState()
    data class Error(val message: String) : MonitoringState()
}
