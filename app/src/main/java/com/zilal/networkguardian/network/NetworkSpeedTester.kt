package com.zilal.networkguardian.network

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.net.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt

/**
 * Network Speed Tester - Like Fing's speed test functionality
 * Provides real internet speed testing with download/upload speeds, latency, and jitter
 */
@Singleton
class NetworkSpeedTester @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _speedTestState = MutableStateFlow<SpeedTestState>(SpeedTestState.Idle)
    val speedTestState: StateFlow<SpeedTestState> = _speedTestState.asStateFlow()
    
    private val _speedTestResults = MutableStateFlow<SpeedTestResults?>(null)
    val speedTestResults: StateFlow<SpeedTestResults?> = _speedTestResults.asStateFlow()
    
    private val _currentProgress = MutableStateFlow(0f)
    val currentProgress: StateFlow<Float> = _currentProgress.asStateFlow()
    
    companion object {
        private const val TAG = "NetworkSpeedTester"
        
        // Test servers (using public speed test servers)
        private val TEST_SERVERS = listOf(
            "http://speedtest.ftp.otenet.gr/files/test1Mb.db",
            "http://speedtest.ftp.otenet.gr/files/test10Mb.db",
            "http://speedtest.ftp.otenet.gr/files/test100Mb.db"
        )
        
        private val PING_HOSTS = listOf(
            "*******",      // Google DNS
            "*******",     // Cloudflare DNS
            "**************" // OpenDNS
        )
        
        private const val DOWNLOAD_TIMEOUT = 30000 // 30 seconds
        private const val UPLOAD_TIMEOUT = 30000   // 30 seconds
        private const val PING_TIMEOUT = 5000      // 5 seconds
        private const val PING_COUNT = 10
    }
    
    /**
     * Start comprehensive speed test
     */
    fun startSpeedTest() {
        if (_speedTestState.value is SpeedTestState.Testing) {
            Log.w(TAG, "Speed test already in progress")
            return
        }
        
        scope.launch {
            try {
                _speedTestState.value = SpeedTestState.Testing
                _currentProgress.value = 0f
                
                val startTime = System.currentTimeMillis()
                
                // Phase 1: Ping Test (20% of progress)
                _speedTestState.value = SpeedTestState.Testing("Testing latency...")
                val pingResults = performPingTest()
                _currentProgress.value = 0.2f
                
                // Phase 2: Download Test (40% of progress)
                _speedTestState.value = SpeedTestState.Testing("Testing download speed...")
                val downloadSpeed = performDownloadTest()
                _currentProgress.value = 0.6f
                
                // Phase 3: Upload Test (40% of progress)
                _speedTestState.value = SpeedTestState.Testing("Testing upload speed...")
                val uploadSpeed = performUploadTest()
                _currentProgress.value = 1.0f
                
                val totalTime = System.currentTimeMillis() - startTime
                
                val results = SpeedTestResults(
                    downloadSpeed = downloadSpeed,
                    uploadSpeed = uploadSpeed,
                    latency = pingResults.averageLatency,
                    jitter = pingResults.jitter,
                    packetLoss = pingResults.packetLoss,
                    testDuration = totalTime,
                    timestamp = System.currentTimeMillis(),
                    serverUsed = "Speed Test Server",
                    connectionType = getConnectionType()
                )
                
                _speedTestResults.value = results
                _speedTestState.value = SpeedTestState.Completed(results)
                
                analyticsManager.trackEvent(
                    eventName = "speed_test_completed",
                    properties = mapOf(
                        "download_mbps" to downloadSpeed,
                        "upload_mbps" to uploadSpeed,
                        "latency_ms" to pingResults.averageLatency,
                        "duration_ms" to totalTime
                    )
                )
                
                Log.i(TAG, "Speed test completed: ${downloadSpeed}Mbps down, ${uploadSpeed}Mbps up, ${pingResults.averageLatency}ms latency")
                
            } catch (e: Exception) {
                Log.e(TAG, "Speed test failed", e)
                _speedTestState.value = SpeedTestState.Error(e.message ?: "Speed test failed")
                analyticsManager.trackError("speed_test_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Perform ping test for latency and jitter
     */
    private suspend fun performPingTest(): PingResults {
        val pingTimes = mutableListOf<Long>()
        var successfulPings = 0
        
        repeat(PING_COUNT) { i ->
            PING_HOSTS.forEach { host ->
                try {
                    val startTime = System.nanoTime()
                    val address = InetAddress.getByName(host)
                    val reachable = address.isReachable(PING_TIMEOUT)
                    val endTime = System.nanoTime()
                    
                    if (reachable) {
                        val pingTime = (endTime - startTime) / 1_000_000 // Convert to milliseconds
                        pingTimes.add(pingTime)
                        successfulPings++
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "Ping failed for $host: ${e.message}")
                }
            }
            
            _currentProgress.value = 0.05f + (i.toFloat() / PING_COUNT) * 0.15f
            delay(100) // Small delay between pings
        }
        
        val averageLatency = if (pingTimes.isNotEmpty()) {
            pingTimes.average().roundToInt()
        } else {
            -1
        }
        
        val jitter = if (pingTimes.size > 1) {
            val mean = pingTimes.average()
            val variance = pingTimes.map { (it - mean) * (it - mean) }.average()
            kotlin.math.sqrt(variance).roundToInt()
        } else {
            0
        }
        
        val packetLoss = ((PING_COUNT * PING_HOSTS.size - successfulPings).toFloat() / 
                         (PING_COUNT * PING_HOSTS.size)) * 100
        
        return PingResults(averageLatency, jitter, packetLoss.roundToInt())
    }
    
    /**
     * Perform download speed test
     */
    private suspend fun performDownloadTest(): Double {
        return withContext(Dispatchers.IO) {
            try {
                val testUrl = TEST_SERVERS[1] // Use 10MB file
                val url = URL(testUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = DOWNLOAD_TIMEOUT
                connection.readTimeout = DOWNLOAD_TIMEOUT
                
                val startTime = System.currentTimeMillis()
                val inputStream = BufferedInputStream(connection.inputStream)
                
                var totalBytes = 0L
                val buffer = ByteArray(8192)
                var bytesRead: Int
                
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    totalBytes += bytesRead
                    
                    // Update progress
                    val elapsed = System.currentTimeMillis() - startTime
                    if (elapsed > 0) {
                        val currentSpeed = (totalBytes * 8.0) / (elapsed / 1000.0) / 1_000_000 // Mbps
                        _currentProgress.value = 0.2f + (elapsed.toFloat() / DOWNLOAD_TIMEOUT) * 0.4f
                    }
                    
                    // Stop if we've been testing for too long
                    if (System.currentTimeMillis() - startTime > DOWNLOAD_TIMEOUT) {
                        break
                    }
                }
                
                inputStream.close()
                connection.disconnect()
                
                val endTime = System.currentTimeMillis()
                val durationSeconds = (endTime - startTime) / 1000.0
                
                if (durationSeconds > 0) {
                    val bitsDownloaded = totalBytes * 8.0
                    val mbps = bitsDownloaded / durationSeconds / 1_000_000
                    mbps
                } else {
                    0.0
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Download test failed", e)
                0.0
            }
        }
    }
    
    /**
     * Perform upload speed test
     */
    private suspend fun performUploadTest(): Double {
        return withContext(Dispatchers.IO) {
            try {
                // Create test data
                val testData = ByteArray(1024 * 1024) // 1MB of test data
                for (i in testData.indices) {
                    testData[i] = (i % 256).toByte()
                }
                
                // Use httpbin.org for upload testing
                val url = URL("https://httpbin.org/post")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.connectTimeout = UPLOAD_TIMEOUT
                connection.readTimeout = UPLOAD_TIMEOUT
                connection.setRequestProperty("Content-Type", "application/octet-stream")
                
                val startTime = System.currentTimeMillis()
                val outputStream = BufferedOutputStream(connection.outputStream)
                
                var totalBytes = 0L
                val chunkSize = 8192
                
                for (i in testData.indices step chunkSize) {
                    val end = minOf(i + chunkSize, testData.size)
                    val chunk = testData.sliceArray(i until end)
                    outputStream.write(chunk)
                    outputStream.flush()
                    
                    totalBytes += chunk.size
                    
                    // Update progress
                    val elapsed = System.currentTimeMillis() - startTime
                    if (elapsed > 0) {
                        _currentProgress.value = 0.6f + (elapsed.toFloat() / UPLOAD_TIMEOUT) * 0.4f
                    }
                    
                    // Stop if we've been testing for too long
                    if (System.currentTimeMillis() - startTime > UPLOAD_TIMEOUT) {
                        break
                    }
                }
                
                outputStream.close()
                
                val responseCode = connection.responseCode
                connection.disconnect()
                
                val endTime = System.currentTimeMillis()
                val durationSeconds = (endTime - startTime) / 1000.0
                
                if (durationSeconds > 0 && responseCode == 200) {
                    val bitsUploaded = totalBytes * 8.0
                    val mbps = bitsUploaded / durationSeconds / 1_000_000
                    mbps
                } else {
                    0.0
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Upload test failed", e)
                0.0
            }
        }
    }
    
    /**
     * Get current connection type
     */
    private fun getConnectionType(): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val network = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
            
            when {
                networkCapabilities?.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                networkCapabilities?.hasTransport(android.net.NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "Cellular"
                networkCapabilities?.hasTransport(android.net.NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "Ethernet"
                else -> "Unknown"
            }
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    /**
     * Stop speed test
     */
    fun stopSpeedTest() {
        _speedTestState.value = SpeedTestState.Idle
        _currentProgress.value = 0f
    }
    
    /**
     * Get speed test history
     */
    fun getSpeedTestHistory(): List<SpeedTestResults> {
        // This would typically load from a database
        // For now, return the current result if available
        return _speedTestResults.value?.let { listOf(it) } ?: emptyList()
    }
}

/**
 * Speed test states
 */
sealed class SpeedTestState {
    object Idle : SpeedTestState()
    data class Testing(val phase: String = "Testing...") : SpeedTestState()
    data class Completed(val results: SpeedTestResults) : SpeedTestState()
    data class Error(val message: String) : SpeedTestState()
}

/**
 * Speed test results
 */
data class SpeedTestResults(
    val downloadSpeed: Double, // Mbps
    val uploadSpeed: Double,   // Mbps
    val latency: Int,          // ms
    val jitter: Int,           // ms
    val packetLoss: Int,       // percentage
    val testDuration: Long,    // ms
    val timestamp: Long,
    val serverUsed: String,
    val connectionType: String
)

/**
 * Ping test results
 */
data class PingResults(
    val averageLatency: Int,
    val jitter: Int,
    val packetLoss: Int
)
