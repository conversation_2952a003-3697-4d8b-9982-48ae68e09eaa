package com.zilal.networkguardian.network

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import android.util.Log
import androidx.core.app.ActivityCompat
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Advanced WiFi Network Analyzer - Like Fing's WiFi analysis
 * Provides comprehensive WiFi network scanning and analysis
 */
@Singleton
class WiFiAnalyzer @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    
    private val _wifiNetworks = MutableStateFlow<List<WiFiNetwork>>(emptyList())
    val wifiNetworks: StateFlow<List<WiFiNetwork>> = _wifiNetworks.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()
    
    private val _currentNetwork = MutableStateFlow<WiFiNetwork?>(null)
    val currentNetwork: StateFlow<WiFiNetwork?> = _currentNetwork.asStateFlow()
    
    companion object {
        private const val TAG = "WiFiAnalyzer"
    }
    
    /**
     * Start WiFi network scanning
     */
    fun startWiFiScan() {
        if (_isScanning.value) return
        
        _isScanning.value = true
        
        scope.launch {
            try {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
                    != PackageManager.PERMISSION_GRANTED) {
                    Log.w(TAG, "Location permission not granted for WiFi scanning")
                    _isScanning.value = false
                    return@launch
                }
                
                // Trigger WiFi scan
                val scanStarted = wifiManager.startScan()
                if (!scanStarted) {
                    Log.w(TAG, "Failed to start WiFi scan")
                    _isScanning.value = false
                    return@launch
                }
                
                // Wait for scan results
                delay(3000) // Give time for scan to complete
                
                val scanResults = wifiManager.scanResults
                val wifiNetworks = scanResults.map { scanResult ->
                    analyzeWiFiNetwork(scanResult)
                }.sortedByDescending { it.signalStrength }
                
                _wifiNetworks.value = wifiNetworks
                
                // Update current network
                updateCurrentNetwork()
                
                analyticsManager.trackEvent(
                    eventName = "wifi_scan_completed",
                    properties = mapOf(
                        "networks_found" to wifiNetworks.size,
                        "timestamp" to System.currentTimeMillis()
                    )
                )
                
                Log.i(TAG, "WiFi scan completed: ${wifiNetworks.size} networks found")
                
            } catch (e: Exception) {
                Log.e(TAG, "WiFi scan failed", e)
                analyticsManager.trackError("wifi_scan_error", e.message ?: "Unknown error")
            } finally {
                _isScanning.value = false
            }
        }
    }
    
    /**
     * Analyze individual WiFi network
     */
    private fun analyzeWiFiNetwork(scanResult: ScanResult): WiFiNetwork {
        val ssid = scanResult.SSID?.takeIf { it.isNotEmpty() } ?: "Hidden Network"
        val bssid = scanResult.BSSID
        val frequency = scanResult.frequency
        val channel = getChannelFromFrequency(frequency)
        val signalStrength = WifiManager.calculateSignalLevel(scanResult.level, 100)
        val security = analyzeSecurity(scanResult.capabilities)
        val bandwidth = getBandwidthFromFrequency(frequency)
        val congestion = analyzeCongestion(frequency, _wifiNetworks.value)
        
        return WiFiNetwork(
            ssid = ssid,
            bssid = bssid,
            frequency = frequency,
            channel = channel,
            signalStrength = signalStrength,
            signalLevel = scanResult.level,
            security = security,
            capabilities = scanResult.capabilities,
            bandwidth = bandwidth,
            congestion = congestion,
            isCurrentNetwork = isCurrentNetwork(bssid),
            vendor = getVendorFromBSSID(bssid),
            lastSeen = System.currentTimeMillis()
        )
    }
    
    /**
     * Update current connected network information
     */
    private fun updateCurrentNetwork() {
        try {
            val connectionInfo = wifiManager.connectionInfo
            val currentBSSID = connectionInfo?.bssid
            
            if (currentBSSID != null) {
                val currentNet = _wifiNetworks.value.find { it.bssid == currentBSSID }
                _currentNetwork.value = currentNet?.copy(
                    isCurrentNetwork = true,
                    linkSpeed = connectionInfo.linkSpeed,
                    ipAddress = connectionInfo.ipAddress,
                    networkId = connectionInfo.networkId
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update current network", e)
        }
    }
    
    /**
     * Get WiFi channel from frequency
     */
    private fun getChannelFromFrequency(frequency: Int): Int {
        return when {
            frequency in 2412..2484 -> (frequency - 2412) / 5 + 1 // 2.4 GHz
            frequency in 5170..5825 -> (frequency - 5000) / 5 // 5 GHz
            frequency in 5925..7125 -> (frequency - 5925) / 5 + 1 // 6 GHz (WiFi 6E)
            else -> 0
        }
    }
    
    /**
     * Analyze security protocols
     */
    private fun analyzeSecurity(capabilities: String): WiFiSecurity {
        val protocols = mutableListOf<String>()
        var securityType = WiFiSecurityType.OPEN
        
        when {
            capabilities.contains("WPA3") -> {
                protocols.add("WPA3")
                securityType = WiFiSecurityType.WPA3
            }
            capabilities.contains("WPA2") -> {
                protocols.add("WPA2")
                securityType = WiFiSecurityType.WPA2
            }
            capabilities.contains("WPA") -> {
                protocols.add("WPA")
                securityType = WiFiSecurityType.WPA
            }
            capabilities.contains("WEP") -> {
                protocols.add("WEP")
                securityType = WiFiSecurityType.WEP
            }
        }
        
        val encryption = when {
            capabilities.contains("CCMP") -> "AES"
            capabilities.contains("TKIP") -> "TKIP"
            else -> "None"
        }
        
        val authentication = when {
            capabilities.contains("PSK") -> "PSK"
            capabilities.contains("EAP") -> "Enterprise"
            capabilities.contains("SAE") -> "SAE"
            else -> "Open"
        }
        
        val securityLevel = when (securityType) {
            WiFiSecurityType.WPA3 -> SecurityLevel.HIGH
            WiFiSecurityType.WPA2 -> SecurityLevel.MEDIUM
            WiFiSecurityType.WPA -> SecurityLevel.LOW
            WiFiSecurityType.WEP -> SecurityLevel.VERY_LOW
            WiFiSecurityType.OPEN -> SecurityLevel.NONE
        }
        
        return WiFiSecurity(
            type = securityType,
            protocols = protocols,
            encryption = encryption,
            authentication = authentication,
            securityLevel = securityLevel
        )
    }
    
    /**
     * Get bandwidth from frequency
     */
    private fun getBandwidthFromFrequency(frequency: Int): String {
        return when {
            frequency in 2412..2484 -> "2.4 GHz"
            frequency in 5170..5825 -> "5 GHz"
            frequency in 5925..7125 -> "6 GHz"
            else -> "Unknown"
        }
    }
    
    /**
     * Analyze network congestion
     */
    private fun analyzeCongestion(frequency: Int, existingNetworks: List<WiFiNetwork>): CongestionLevel {
        val channel = getChannelFromFrequency(frequency)
        val sameChannelNetworks = existingNetworks.count { 
            getChannelFromFrequency(it.frequency) == channel 
        }
        
        return when {
            sameChannelNetworks >= 5 -> CongestionLevel.HIGH
            sameChannelNetworks >= 3 -> CongestionLevel.MEDIUM
            sameChannelNetworks >= 1 -> CongestionLevel.LOW
            else -> CongestionLevel.NONE
        }
    }
    
    /**
     * Check if this is the current connected network
     */
    private fun isCurrentNetwork(bssid: String): Boolean {
        return try {
            val connectionInfo = wifiManager.connectionInfo
            connectionInfo?.bssid == bssid
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get vendor from BSSID (MAC address)
     */
    private fun getVendorFromBSSID(bssid: String): String {
        val oui = bssid.take(8).replace(":", "").uppercase()
        return when {
            oui.startsWith("00:1B:63") -> "Apple"
            oui.startsWith("00:50:56") -> "VMware"
            oui.startsWith("08:00:27") -> "VirtualBox"
            oui.startsWith("00:0C:29") -> "VMware"
            oui.startsWith("00:15:5D") -> "Microsoft"
            oui.startsWith("B8:27:EB") -> "Raspberry Pi"
            oui.startsWith("DC:A6:32") -> "Raspberry Pi"
            oui.startsWith("E4:5F:01") -> "Raspberry Pi"
            oui.startsWith("00:16:3E") -> "Xensource"
            oui.startsWith("52:54:00") -> "QEMU"
            else -> "Unknown"
        }
    }
    
    /**
     * Get detailed channel analysis
     */
    fun getChannelAnalysis(): List<ChannelInfo> {
        val networks = _wifiNetworks.value
        val channels = mutableMapOf<Int, MutableList<WiFiNetwork>>()
        
        networks.forEach { network ->
            val channel = network.channel
            channels.getOrPut(channel) { mutableListOf() }.add(network)
        }
        
        return channels.map { (channel, networksOnChannel) ->
            val avgSignal = networksOnChannel.map { it.signalStrength }.average().toInt()
            val congestion = when (networksOnChannel.size) {
                0 -> CongestionLevel.NONE
                1 -> CongestionLevel.LOW
                in 2..4 -> CongestionLevel.MEDIUM
                else -> CongestionLevel.HIGH
            }
            
            ChannelInfo(
                channel = channel,
                frequency = networksOnChannel.firstOrNull()?.frequency ?: 0,
                networkCount = networksOnChannel.size,
                averageSignal = avgSignal,
                congestion = congestion,
                networks = networksOnChannel
            )
        }.sortedBy { it.channel }
    }
    
    /**
     * Stop WiFi scanning
     */
    fun stopScanning() {
        _isScanning.value = false
    }
}

/**
 * WiFi Network data class
 */
data class WiFiNetwork(
    val ssid: String,
    val bssid: String,
    val frequency: Int,
    val channel: Int,
    val signalStrength: Int,
    val signalLevel: Int,
    val security: WiFiSecurity,
    val capabilities: String,
    val bandwidth: String,
    val congestion: CongestionLevel,
    val isCurrentNetwork: Boolean,
    val vendor: String,
    val lastSeen: Long,
    val linkSpeed: Int = 0,
    val ipAddress: Int = 0,
    val networkId: Int = -1
)

/**
 * WiFi Security information
 */
data class WiFiSecurity(
    val type: WiFiSecurityType,
    val protocols: List<String>,
    val encryption: String,
    val authentication: String,
    val securityLevel: SecurityLevel
)

/**
 * Channel information
 */
data class ChannelInfo(
    val channel: Int,
    val frequency: Int,
    val networkCount: Int,
    val averageSignal: Int,
    val congestion: CongestionLevel,
    val networks: List<WiFiNetwork>
)

/**
 * WiFi Security Types
 */
enum class WiFiSecurityType {
    OPEN, WEP, WPA, WPA2, WPA3
}

/**
 * Security Levels
 */
enum class SecurityLevel {
    NONE, VERY_LOW, LOW, MEDIUM, HIGH
}

/**
 * Congestion Levels
 */
enum class CongestionLevel {
    NONE, LOW, MEDIUM, HIGH
}
