package com.zilal.networkguardian.network

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.api.SupabaseClient
import com.zilal.networkguardian.bridge.NetworkScannerBridge
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.ScanResult
import com.zilal.networkguardian.model.Vulnerability
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.UUID

/**
 * Enhanced network scanner that uses the Airborne Security Scanner bridge
 */
class AirborneNetworkScanner(private val context: Context) {

    companion object {
        private const val TAG = "AirborneNetworkScanner"
    }

    private val networkScannerBridge = NetworkScannerBridge(context)

    /**
     * Perform a network scan using the Airborne Security Scanner
     */
    fun scanNetwork(): Flow<ScanResult> {
        Log.d(TAG, "Starting Airborne network scan")

        // Start the scan
        networkScannerBridge.startScan()

        // Return the flow of scan results
        return networkScannerBridge.scanResultFlow.map { scanResult ->
            // Enhance the scan result with additional information
            enhanceScanResult(scanResult)
        }
    }

    /**
     * Stop the current scan
     */
    fun stopScan() {
        networkScannerBridge.stopScan()
    }

    /**
     * Check if a scan is currently in progress
     */
    fun isScanning(): Boolean {
        return networkScannerBridge.isScanning()
    }

    /**
     * Get the list of discovered devices
     */
    fun getDiscoveredDevices(): List<NetworkDevice> {
        return networkScannerBridge.getDiscoveredDevices()
    }

    /**
     * Enhance scan result with additional information
     */
    private fun enhanceScanResult(scanResult: ScanResult): ScanResult {
        val enhancedDevices = scanResult.devices.map { device ->
            enhanceDeviceInfo(device)
        }

        val vulnerabilities = findVulnerabilities(enhancedDevices)

        val enhancedScanResult = scanResult.copy(
            devices = enhancedDevices,
            vulnerabilities = vulnerabilities
        )

        // TODO: Save scan results to Supabase (temporarily disabled for build fix)
        /*
        val supabaseClient = SupabaseClient(context)
        Thread {
            try {
                runBlocking {
                    // Convert to Supabase DTO
                    val scanResultDto = SupabaseClient.NetworkScanDto(
                        userId = "user123",
                        scanType = "AIRBORNE_SCAN",
                        status = "completed",
                        startTime = enhancedScanResult.startTime,
                        endTime = enhancedScanResult.endTime,
                        results = SupabaseClient.ScanResults(
                            devicesFound = enhancedScanResult.devices.size,
                            vulnerabilitiesFound = enhancedScanResult.vulnerabilities.size,
                            openPorts = enhancedScanResult.devices.sumOf { it.openPorts?.size ?: 0 },
                            securityIssues = enhancedScanResult.vulnerabilities.size
                        )
                    )

                    val savedScanId = supabaseClient.saveScanResultEnhanced(scanResultDto)
                    Log.d(TAG, "Saved Airborne scan results to Supabase with ID: $savedScanId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error saving Airborne scan results to Supabase: ${e.message}")
            }
        }.start()
        */

        return enhancedScanResult
    }

    /**
     * Enhance device information with additional details
     */
    private fun enhanceDeviceInfo(device: NetworkDevice): NetworkDevice {
        // Determine device type based on open ports
        val deviceType = determineDeviceType(device.openPorts)

        // Determine manufacturer based on MAC address
        val manufacturer = device.macAddress?.let { determineManufacturer(it) }

        return device.copy(
            deviceType = deviceType,
            manufacturer = manufacturer
        )
    }

    /**
     * Determine device type based on open ports
     */
    private fun determineDeviceType(openPorts: List<Int>?): NetworkDevice.DeviceType {
        if (openPorts == null || openPorts.isEmpty()) {
            return NetworkDevice.DeviceType.UNKNOWN
        }

        return when {
            // Web server
            openPorts.contains(80) || openPorts.contains(443) || openPorts.contains(8080) || openPorts.contains(8443) ->
                NetworkDevice.DeviceType.SERVER

            // SSH server
            openPorts.contains(22) ->
                NetworkDevice.DeviceType.SERVER

            // File sharing
            openPorts.contains(445) || openPorts.contains(139) ->
                NetworkDevice.DeviceType.COMPUTER

            // Database
            openPorts.contains(3306) || openPorts.contains(5432) || openPorts.contains(1433) ->
                NetworkDevice.DeviceType.SERVER

            // Remote desktop
            openPorts.contains(3389) ->
                NetworkDevice.DeviceType.COMPUTER

            // IoT devices often have telnet
            openPorts.contains(23) ->
                NetworkDevice.DeviceType.UNKNOWN

            else -> NetworkDevice.DeviceType.UNKNOWN
        }
    }

    /**
     * Determine manufacturer based on MAC address
     */
    private fun determineManufacturer(macAddress: String): String? {
        // This would normally use a MAC address database
        // For now, return null as a placeholder
        return null
    }

    /**
     * Find vulnerabilities in the discovered devices
     */
    private fun findVulnerabilities(devices: List<NetworkDevice>): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()

        devices.forEach { device ->
            // Check for open telnet port (security risk)
            if (device.openPorts?.contains(23) == true) {
                vulnerabilities.add(
                    Vulnerability(
                        id = UUID.randomUUID().toString(),
                        deviceId = device.id,
                        name = "Open Telnet Port",
                        description = "Device ${device.ipAddress} has an open Telnet port (23), which transmits data in cleartext and is a security risk.",
                        severity = Vulnerability.Severity.HIGH,
                        detectionTimestamp = System.currentTimeMillis(),
                        deviceIpAddress = device.ipAddress
                    )
                )
            }

            // Check for open FTP port (potential security risk)
            if (device.openPorts?.contains(21) == true) {
                vulnerabilities.add(
                    Vulnerability(
                        id = UUID.randomUUID().toString(),
                        deviceId = device.id,
                        name = "Open FTP Port",
                        description = "Device ${device.ipAddress} has an open FTP port (21), which may transmit credentials in cleartext.",
                        severity = Vulnerability.Severity.MEDIUM,
                        detectionTimestamp = System.currentTimeMillis(),
                        deviceIpAddress = device.ipAddress
                    )
                )
            }

            // Check for open SMB ports (potential security risk)
            if (device.openPorts?.contains(445) == true || device.openPorts?.contains(139) == true) {
                vulnerabilities.add(
                    Vulnerability(
                        id = UUID.randomUUID().toString(),
                        deviceId = device.id,
                        name = "Open SMB Ports",
                        description = "Device ${device.ipAddress} has open SMB ports (139/445), which could be vulnerable to various attacks if not properly secured.",
                        severity = Vulnerability.Severity.MEDIUM,
                        detectionTimestamp = System.currentTimeMillis(),
                        deviceIpAddress = device.ipAddress
                    )
                )
            }
        }

        return vulnerabilities
    }
}
