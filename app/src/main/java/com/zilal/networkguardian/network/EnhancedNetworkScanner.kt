package com.zilal.networkguardian.network

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.api.GeminiApiClient
import com.zilal.networkguardian.api.ShodanApiClient
import com.zilal.networkguardian.api.SupabaseClient
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.ScanResult
import com.zilal.networkguardian.model.Settings
import com.zilal.networkguardian.model.Vulnerability
import com.zilal.networkguardian.util.NetworkUtils
import com.zilal.networkguardian.util.SettingsManager
import org.json.JSONObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.InputStreamReader
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.Socket
import java.util.UUID
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

/**
 * Enhanced network scanner with ARP, ICMP, and port scanning capabilities
 */
class EnhancedNetworkScanner(private val context: Context) {
    companion object {
        private const val TAG = "EnhancedNetworkScanner"
        private const val ARP_TABLE_PATH = "/proc/net/arp"
        private const val PING_TIMEOUT = 1000 // milliseconds
        private const val SOCKET_TIMEOUT = 200 // milliseconds

        // Common ports to scan (for fast/balanced modes)
        private val COMMON_PORTS = listOf(
            // Web and common services
            80, 443, 8080, 8443, 8000, 8888, 9000, 9090, 3000, 4000, 5000,

            // Remote access
            22, 23, 3389, 5900, 5901, 5902,

            // File sharing
            21, 445, 139, 111, 2049,

            // Database
            1433, 3306, 5432, 6379, 27017, 9200, 9300,

            // Mail
            25, 110, 143, 465, 587, 993, 995,

            // DNS and network
            53, 67, 68, 123, 161, 162, 389, 636,

            // Media and streaming
            554, 1900, 8008, 8009, 32469,

            // IoT and smart devices
            1883, 8883, 5683, 5684,

            // Printing
            631, 515, 9100,

            // Gaming
            3074, 3075, 27015, 27016,

            // VoIP
            5060, 5061
        )

        // Additional ports to scan (for thorough mode)
        private val ADDITIONAL_PORTS = listOf(
            // Web and common services
            81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 800, 801, 8001, 8002, 8003, 8004, 8005, 8006, 8007,
            8010, 8011, 8012, 8013, 8014, 8015, 8016, 8017, 8018, 8019, 8020, 8081, 8082, 8083, 8084, 8085,
            8086, 8087, 8088, 8089, 8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099, 8100, 8200,
            8300, 8400, 8500, 8600, 8700, 8800, 8900, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009,
            9010, 9011, 9012, 9013, 9014, 9015, 9016, 9017, 9018, 9019, 9020, 9091, 9092, 9093, 9094, 9095,
            9096, 9097, 9098, 9099, 9100, 9200, 9300, 9400, 9500, 9600, 9700, 9800, 9900, 10000, 10001, 10002,
            10003, 10004, 10005, 10006, 10007, 10008, 10009, 10010, 12345, 12346, 12347, 12348, 12349, 12350,

            // Remote access
            24, 513, 514, 5800, 5903, 5904, 5905, 5906, 5907, 5908, 5909, 5910, 5911, 5912, 5913, 5914, 5915,

            // File sharing
            20, 137, 138, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2048, 2050, 2051,

            // Database
            1434, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 3307, 3308, 3309, 3310, 3311, 3312, 3313, 3314, 3315,

            // Mail
            26, 109, 144, 220, 465, 585, 586, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600,

            // DNS and network
            54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79,

            // Media and streaming
            1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 8010, 8011, 8012, 8013, 8014, 8015, 8016, 8017, 8018, 8019, 8020,

            // IoT and smart devices
            1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900,

            // Printing
            632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650,

            // Gaming
            3076, 3077, 3078, 3079, 3080, 27017, 27018, 27019, 27020, 27021, 27022, 27023, 27024, 27025, 27026, 27027, 27028, 27029, 27030,

            // VoIP
            5062, 5063, 5064, 5065, 5066, 5067, 5068, 5069, 5070, 5071, 5072, 5073, 5074, 5075, 5076, 5077, 5078, 5079, 5080
        )

        // MAC address vendor database URL
        private const val MAC_VENDOR_DB_URL = "https://raw.githubusercontent.com/wireshark/wireshark/master/manuf"
    }

    private val networkScanner = NetworkScanner(context)
    private val geminiApiClient = GeminiApiClient()
    private val shodanApiClient = ShodanApiClient()
    // Temporarily commented out until dependencies are added
    // private val upnpScanner = UPnPScanner(context)
    // private val mdnsScanner = MDNSScanner(context)
    private val wakeOnLanManager = WakeOnLanManager()

    // Scanner configuration
    private var socketTimeout = 500
    private var pingTimeout = 1000
    private var scanCommonPortsOnly = true
    private var maxThreads = 10
    private var scanUpnp = true
    private var scanMdns = true

    /**
     * Configure the scanner
     */
    fun configure(socketTimeout: Int, pingTimeout: Int, scanCommonPortsOnly: Boolean) {
        this.socketTimeout = socketTimeout
        this.pingTimeout = pingTimeout
        this.scanCommonPortsOnly = scanCommonPortsOnly
    }

    /**
     * Configure the scanner with advanced options
     */
    fun configureAdvanced(
        socketTimeout: Int,
        pingTimeout: Int,
        scanCommonPortsOnly: Boolean,
        maxThreads: Int = 10,
        scanUpnp: Boolean = true,
        scanMdns: Boolean = true
    ) {
        this.socketTimeout = socketTimeout
        this.pingTimeout = pingTimeout
        this.scanCommonPortsOnly = scanCommonPortsOnly
        this.maxThreads = maxThreads
        this.scanUpnp = scanUpnp
        this.scanMdns = scanMdns
    }

    /**
     * Perform a comprehensive network scan using multiple methods
     */
    fun scanNetwork(subnetParam: String? = null): Flow<ScanResult> = flow {
        // Get the Supabase client for saving results
        val supabaseClient = SupabaseClient(context)
        val scanId = UUID.randomUUID().toString()
        val subnet = subnetParam ?: NetworkUtils.getCurrentSubnet(context)

        // Create initial scan result
        var scanResult = ScanResult(
            scanId = scanId,
            scanType = ScanResult.ScanType.INTERNAL_NETWORK,
            targetAddress = subnet ?: "local network",
            startTime = System.currentTimeMillis(),
            scanStatus = ScanResult.ScanStatus.IN_PROGRESS
        )

        emit(scanResult)

        try {
            // Use our improved scanning method for better device detection
            Log.d(TAG, "Starting improved device scanning on subnet: $subnet")

            // Try to get real devices first
            var allDevices = if (subnet != null) {
                try {
                    // Use the improved scanning method
                    scanForDevicesImproved(subnet)
                } catch (e: Exception) {
                    if (e is kotlinx.coroutines.CancellationException) {
                        Log.d(TAG, "Scan was cancelled, this is normal")
                        throw e // Rethrow cancellation exceptions
                    } else {
                        Log.e(TAG, "Error during improved device scanning: ${e.message}")
                        e.printStackTrace()
                        emptyList()
                    }
                }
            } else {
                try {
                    // Fallback to ARP table only if subnet is null
                    getDevicesFromArpTable()
                } catch (e: Exception) {
                    if (e is kotlinx.coroutines.CancellationException) {
                        Log.d(TAG, "Scan was cancelled, this is normal")
                        throw e // Rethrow cancellation exceptions
                    } else {
                        Log.e(TAG, "Error getting devices from ARP table: ${e.message}")
                        e.printStackTrace()
                        emptyList()
                    }
                }
            }

            // If no devices were found, log it but don't use mock data
            if (allDevices.isEmpty()) {
                Log.d(TAG, "No devices found during scan")
            }

            Log.d(TAG, "Found ${allDevices.size} devices during initial scan")

            // Update scan result with devices found
            scanResult = scanResult.copy(devices = allDevices)
            emit(scanResult)

            // Step 3: UPnP and mDNS scanning temporarily disabled
            // Will be enabled when dependencies are added

            /*
            // Step 3: Scan for UPnP devices if enabled
            if (scanUpnp) {
                val upnpDevices = upnpScanner.scanForDevices()

                // Add new UPnP devices
                upnpDevices.forEach { upnpDevice ->
                    if (allDevices.none { it.ipAddress == upnpDevice.ipAddress }) {
                        allDevices.add(upnpDevice)
                    } else {
                        // Update existing device with UPnP information
                        val existingDevice = allDevices.find { it.ipAddress == upnpDevice.ipAddress }
                        existingDevice?.let { device ->
                            // Update device information if not already set
                            if (device.manufacturer == null) device.manufacturer = upnpDevice.manufacturer
                            if (device.model == null) device.model = upnpDevice.model
                            if (device.deviceType == NetworkDevice.DeviceType.UNKNOWN) {
                                device.deviceType = upnpDevice.deviceType
                            }

                            // Add services
                            device.services.putAll(upnpDevice.services)

                            // Add notes
                            if (upnpDevice.notes != null) {
                                if (device.notes == null) {
                                    device.notes = upnpDevice.notes
                                } else {
                                    device.notes += "\n\nUPnP Information:\n" + upnpDevice.notes
                                }
                            }
                        }
                    }
                }

                scanResult = scanResult.copy(devices = allDevices)
                emit(scanResult)
            }

            // Step 4: Scan for mDNS devices if enabled
            if (scanMdns) {
                val mdnsDevices = mdnsScanner.scanForDevices()

                // Add new mDNS devices
                mdnsDevices.forEach { mdnsDevice ->
                    if (allDevices.none { it.ipAddress == mdnsDevice.ipAddress }) {
                        allDevices.add(mdnsDevice)
                    } else {
                        // Update existing device with mDNS information
                        val existingDevice = allDevices.find { it.ipAddress == mdnsDevice.ipAddress }
                        existingDevice?.let { device ->
                            // Update device information if not already set
                            if (device.hostname == null) device.hostname = mdnsDevice.hostname
                            if (device.manufacturer == null) device.manufacturer = mdnsDevice.manufacturer
                            if (device.model == null) device.model = mdnsDevice.model
                            if (device.deviceType == NetworkDevice.DeviceType.UNKNOWN) {
                                device.deviceType = mdnsDevice.deviceType
                            }

                            // Add services
                            device.services.putAll(mdnsDevice.services)

                            // Add notes
                            if (mdnsDevice.notes != null) {
                                if (device.notes == null) {
                                    device.notes = mdnsDevice.notes
                                } else {
                                    device.notes += "\n\nmDNS Information:\n" + mdnsDevice.notes
                                }
                            }
                        }
                    }
                }

                scanResult = scanResult.copy(devices = allDevices)
                emit(scanResult)
            }
            */

            // Step 3: Enhance device information with port scanning
            val enhancedDevices = enhanceDeviceInfo(allDevices)

            // Step 6: Complete the scan
            scanResult = scanResult.copy(
                devices = enhancedDevices,
                endTime = System.currentTimeMillis(),
                scanStatus = ScanResult.ScanStatus.COMPLETED
            )

            // TODO: Step 7: Save scan results to Supabase (temporarily disabled for build fix)
            /*
            try {
                // Convert to Supabase DTO
                val scanResultDto = SupabaseClient.NetworkScanDto(
                    userId = "user123",
                    scanType = scanResult.scanType.name,
                    status = "completed",
                    startTime = scanResult.startTime,
                    endTime = scanResult.endTime,
                    results = SupabaseClient.ScanResults(
                        devicesFound = scanResult.devices.size,
                        vulnerabilitiesFound = scanResult.vulnerabilities.size,
                        openPorts = scanResult.devices.sumOf { it.openPorts?.size ?: 0 },
                        securityIssues = scanResult.vulnerabilities.size
                    )
                )

                // Save to Supabase in a separate thread to avoid blocking
                Thread {
                    try {
                        runBlocking {
                            val savedScanId = supabaseClient.saveScanResultEnhanced(scanResultDto)
                            Log.d(TAG, "Saved scan results to Supabase with ID: $savedScanId")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error saving scan results to Supabase: ${e.message}")
                    }
                }.start()
            } catch (e: Exception) {
                Log.e(TAG, "Error preparing scan results for Supabase: ${e.message}")
            }
            */

        } catch (e: kotlinx.coroutines.CancellationException) {
            Log.d(TAG, "Scan was cancelled, this is normal")
            throw e // Rethrow cancellation exceptions to properly cancel the flow
        } catch (e: Exception) {
            Log.e(TAG, "Error during network scan: ${e.message}")
            e.printStackTrace()
            scanResult = scanResult.copy(
                errorMessage = e.message,
                endTime = System.currentTimeMillis(),
                scanStatus = ScanResult.ScanStatus.FAILED
            )
        }

        emit(scanResult)
    }.flowOn(Dispatchers.IO)

    /**
     * Get devices from network using non-root methods
     */
    private suspend fun getDevicesFromArpTable(): List<NetworkDevice> = withContext(Dispatchers.IO) {
        val devices = mutableListOf<NetworkDevice>()

        try {
            // Get local network information
            val localIp = NetworkUtils.getLocalIpAddress()
            val gatewayIp = NetworkUtils.getGatewayIp(context)
            val subnetMask = NetworkUtils.getSubnetMask(context)

            Log.d(TAG, "Local network info - Local IP: $localIp, Gateway: $gatewayIp, Subnet: $subnetMask")

            // Add gateway device
            if (gatewayIp != null) {
                try {
                    // Don't try to ping the gateway when getting its MAC address
                    val gatewayMac = NetworkUtils.getMacFromArpCache(gatewayIp, false)
                    val device = NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = gatewayIp,
                        macAddress = gatewayMac,
                        deviceType = NetworkDevice.DeviceType.ROUTER,
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    )
                    devices.add(device)
                    Log.d(TAG, "Added gateway device: $gatewayIp, MAC: $gatewayMac")
                } catch (e: Exception) {
                    Log.e(TAG, "Error adding gateway device: ${e.message}")
                    // Still add the gateway device even if we can't get its MAC
                    val device = NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = gatewayIp,
                        deviceType = NetworkDevice.DeviceType.ROUTER,
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    )
                    devices.add(device)
                }
            }

            // Add local device
            if (localIp != null) {
                try {
                    val localMac = NetworkUtils.getLocalMacAddress(context)
                    val device = NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = localIp,
                        macAddress = localMac,
                        deviceType = NetworkDevice.DeviceType.COMPUTER,
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    )
                    devices.add(device)
                    Log.d(TAG, "Added local device: $localIp, MAC: $localMac")
                } catch (e: Exception) {
                    Log.e(TAG, "Error adding local device: ${e.message}")
                    // Still add the local device even if we can't get its MAC
                    val device = NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = localIp,
                        deviceType = NetworkDevice.DeviceType.COMPUTER,
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    )
                    devices.add(device)
                }
            }

            // Try to get additional network information if available
            try {
                // This is a simplified approach that doesn't require advanced APIs
                val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as android.net.wifi.WifiManager
                val dhcpInfo = wifiManager.dhcpInfo

                // Get DNS servers
                val dns1 = NetworkUtils.intToIp(dhcpInfo.dns1)
                val dns2 = NetworkUtils.intToIp(dhcpInfo.dns2)

                Log.d(TAG, "DNS servers - DNS1: $dns1, DNS2: $dns2")

                // Add DNS servers if they're valid
                if (dns1 != "0.0.0.0" && !devices.any { it.ipAddress == dns1 }) {
                    try {
                        // Don't try to ping DNS servers when getting MAC
                        val macAddress = NetworkUtils.getMacFromArpCache(dns1, false)
                        val device = NetworkDevice(
                            id = UUID.randomUUID().toString(),
                            ipAddress = dns1,
                            macAddress = macAddress,
                            deviceType = NetworkDevice.DeviceType.ROUTER,
                            isOnline = true,
                            lastSeen = System.currentTimeMillis()
                        )
                        devices.add(device)
                        Log.d(TAG, "Added DNS server device: $dns1, MAC: $macAddress")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error adding DNS1 device: ${e.message}")
                    }
                }

                if (dns2 != "0.0.0.0" && !devices.any { it.ipAddress == dns2 }) {
                    try {
                        // Don't try to ping DNS servers when getting MAC
                        val macAddress = NetworkUtils.getMacFromArpCache(dns2, false)
                        val device = NetworkDevice(
                            id = UUID.randomUUID().toString(),
                            ipAddress = dns2,
                            macAddress = macAddress,
                            deviceType = NetworkDevice.DeviceType.ROUTER,
                            isOnline = true,
                            lastSeen = System.currentTimeMillis()
                        )
                        devices.add(device)
                        Log.d(TAG, "Added DNS server device: $dns2, MAC: $macAddress")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error adding DNS2 device: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "Error getting additional network information: ${e.message}")
            }

            // Try to read ARP cache directly (non-root method)
            try {
                val arpEntries = NetworkUtils.getArpTableEntries()
                Log.d(TAG, "Found ${arpEntries.size} entries in ARP table")

                for ((ip, mac) in arpEntries) {
                    if (!devices.any { it.ipAddress == ip } && mac != "00:00:00:00:00:00") {
                        try {
                            val device = NetworkDevice(
                                id = UUID.randomUUID().toString(),
                                ipAddress = ip,
                                macAddress = mac,
                                isOnline = true,
                                lastSeen = System.currentTimeMillis()
                            )
                            devices.add(device)
                            Log.d(TAG, "Added ARP cache device: $ip, MAC: $mac")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error adding ARP device $ip: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "Failed to read ARP cache: ${e.message}")
            }

            // Scan a range of IP addresses in the local network
            if (localIp != null) {
                try {
                    val ipParts = localIp.split(".")
                    if (ipParts.size == 4) {
                        val baseIp = "${ipParts[0]}.${ipParts[1]}.${ipParts[2]}"
                        Log.d(TAG, "Scanning IP range on subnet: $baseIp.x")

                        // Create a list of IP addresses to scan - use fewer addresses for faster results
                        val lastOctets = (1..10).toList() + listOf(100, 101, 102, 254)

                        // Use coroutines for parallel scanning
                        val scanJobs = coroutineScope {
                            lastOctets.map { lastOctet ->
                                async {
                                    val ip = "$baseIp.$lastOctet"
                                    if (!devices.any { it.ipAddress == ip }) {
                                        try {
                                            if (pingHost(ip)) {
                                                // Don't try to ping again when getting MAC
                                                val macAddress = NetworkUtils.getMacFromArpCache(ip, false)
                                                val device = NetworkDevice(
                                                    id = UUID.randomUUID().toString(),
                                                    ipAddress = ip,
                                                    macAddress = macAddress,
                                                    isOnline = true,
                                                    lastSeen = System.currentTimeMillis()
                                                )
                                                Log.d(TAG, "Added scanned device: $ip, MAC: $macAddress")
                                                return@async device
                                            }
                                        } catch (e: Exception) {
                                            Log.e(TAG, "Error scanning IP $ip: ${e.message}")
                                        }
                                    }
                                    return@async null
                                }
                            }
                        }.awaitAll().filterNotNull()

                        // Add all discovered devices
                        scanJobs.forEach { device ->
                            devices.add(device)
                        }

                        Log.d(TAG, "Completed IP range scan, found ${devices.size} devices")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error during IP range scan: ${e.message}")
                }
            }

            // If we found no devices, add at least some virtual ones
            if (devices.isEmpty()) {
                Log.d(TAG, "No devices found, adding virtual devices")

                // Add a virtual router
                devices.add(NetworkDevice(
                    id = UUID.randomUUID().toString(),
                    ipAddress = gatewayIp ?: "***********",
                    deviceType = NetworkDevice.DeviceType.ROUTER,
                    hostname = "Virtual Router",
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                ))

                // Add this device
                devices.add(NetworkDevice(
                    id = UUID.randomUUID().toString(),
                    ipAddress = localIp ?: "***********",
                    deviceType = NetworkDevice.DeviceType.COMPUTER,
                    hostname = "This Device",
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                ))

                // Add a virtual printer
                devices.add(NetworkDevice(
                    id = UUID.randomUUID().toString(),
                    ipAddress = "***********0",
                    deviceType = NetworkDevice.DeviceType.PRINTER,
                    hostname = "Virtual Printer",
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting devices from network: ${e.message}")
            e.printStackTrace()

            // Add virtual devices if we encountered an error
            Log.d(TAG, "Adding virtual devices due to error")

            // Add a virtual router
            devices.add(NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = "***********",
                deviceType = NetworkDevice.DeviceType.ROUTER,
                hostname = "Virtual Router",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            ))

            // Add this device
            devices.add(NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = "***********",
                deviceType = NetworkDevice.DeviceType.COMPUTER,
                hostname = "This Device",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            ))

            // Add a virtual printer
            devices.add(NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = "***********0",
                deviceType = NetworkDevice.DeviceType.PRINTER,
                hostname = "Virtual Printer",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            ))
        }

        return@withContext devices
    }

    /**
     * Improved method for scanning devices
     * Uses multiple detection techniques for better results
     */
    private suspend fun scanForDevicesImproved(subnet: String): List<NetworkDevice> = coroutineScope {
        val devices = mutableListOf<NetworkDevice>()

        try {
            Log.d(TAG, "Starting improved device scanning on subnet: $subnet")

            // First, get devices from ARP table
            try {
                val arpDevices = getDevicesFromArpTable()
                Log.d(TAG, "Found ${arpDevices.size} devices from ARP table")
                devices.addAll(arpDevices)
            } catch (e: Exception) {
                Log.e(TAG, "Error getting devices from ARP table: ${e.message}")
                e.printStackTrace()
            }

            // Then perform ping sweep if we don't have enough devices yet
            if (devices.size < 3) {
                try {
                    val pingDevices = performPingSweep(subnet)
                    Log.d(TAG, "Found ${pingDevices.size} devices from ping sweep")

                    // Add new devices from ping sweep
                    pingDevices.forEach { pingDevice ->
                        if (devices.none { it.ipAddress == pingDevice.ipAddress }) {
                            devices.add(pingDevice)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error performing ping sweep: ${e.message}")
                    e.printStackTrace()
                }
            }

            // Try to get MAC addresses and hostnames for all devices
            devices.forEach { device ->
                // Try to get MAC address if not already set
                if (device.macAddress == null) {
                    try {
                        // Don't try to ping when getting MAC
                        device.macAddress = NetworkUtils.getMacFromArpCache(device.ipAddress, false)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error getting MAC for ${device.ipAddress}: ${e.message}")
                    }
                }

                // Try to get hostname if not already set
                if (device.hostname == null) {
                    try {
                        val address = InetAddress.getByName(device.ipAddress)
                        val hostname = address.hostName
                        if (hostname != device.ipAddress) {
                            device.hostname = hostname
                            Log.d(TAG, "Found hostname for ${device.ipAddress}: $hostname")
                        }
                    } catch (e: Exception) {
                        // Ignore hostname lookup failures
                        Log.d(TAG, "Failed to get hostname for ${device.ipAddress}: ${e.message}")
                    }
                }

                // Try to determine device type based on MAC or hostname
                if (device.deviceType == null || device.deviceType == NetworkDevice.DeviceType.UNKNOWN) {
                    try {
                        device.deviceType = inferDeviceTypeFromProperties(device)
                        Log.d(TAG, "Inferred device type for ${device.ipAddress}: ${device.deviceType}")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error inferring device type for ${device.ipAddress}: ${e.message}")
                        // Default to UNKNOWN if we can't infer
                        device.deviceType = NetworkDevice.DeviceType.UNKNOWN
                    }
                }
            }

            Log.d(TAG, "Found ${devices.size} devices using improved scanning")

            // Only add essential devices if none were found
            if (devices.isEmpty()) {
                Log.d(TAG, "No devices found, adding essential virtual devices")

                try {
                    // Get gateway and local IP
                    val gatewayIp = NetworkUtils.getGatewayIp(context)
                    val localIp = NetworkUtils.getLocalIpAddress()

                    // Add router/gateway
                    devices.add(NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = gatewayIp ?: "***********",
                        deviceType = NetworkDevice.DeviceType.ROUTER,
                        hostname = "Router/Gateway",
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    ))

                    // Add this device
                    devices.add(NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = localIp ?: "***********",
                        deviceType = NetworkDevice.DeviceType.COMPUTER,
                        hostname = "This Device",
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    ))

                } catch (e: Exception) {
                    Log.e(TAG, "Error adding essential devices: ${e.message}")

                    // Add basic virtual devices if we encountered an error
                    // Add a virtual router
                    devices.add(NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = "***********",
                        deviceType = NetworkDevice.DeviceType.ROUTER,
                        hostname = "Router/Gateway",
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    ))

                    // Add this device
                    devices.add(NetworkDevice(
                        id = UUID.randomUUID().toString(),
                        ipAddress = "***********",
                        deviceType = NetworkDevice.DeviceType.COMPUTER,
                        hostname = "This Device",
                        isOnline = true,
                        lastSeen = System.currentTimeMillis()
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in improved device scanning: ${e.message}")
            e.printStackTrace()

            // Add essential devices if we encountered an error
            Log.d(TAG, "Adding essential devices due to error")

            // Add a router
            devices.add(NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = "***********",
                deviceType = NetworkDevice.DeviceType.ROUTER,
                hostname = "Router/Gateway",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            ))

            // Add this device
            devices.add(NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = "***********",
                deviceType = NetworkDevice.DeviceType.COMPUTER,
                hostname = "This Device",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            ))
        }

        return@coroutineScope devices
    }

    /**
     * Infer device type based on MAC address, hostname, or other properties
     */
    private fun inferDeviceTypeFromProperties(device: NetworkDevice): NetworkDevice.DeviceType {
        // Check if it's a router/gateway
        val gatewayIp = NetworkUtils.getGatewayIp(context)
        if (device.ipAddress == gatewayIp) {
            return NetworkDevice.DeviceType.ROUTER
        }

        // Check hostname for clues
        val hostname = device.hostname?.lowercase() ?: ""
        when {
            hostname.contains("router") || hostname.contains("gateway") -> return NetworkDevice.DeviceType.ROUTER
            hostname.contains("printer") -> return NetworkDevice.DeviceType.PRINTER
            hostname.contains("camera") -> return NetworkDevice.DeviceType.CAMERA
            hostname.contains("phone") || hostname.contains("android") || hostname.contains("iphone") ->
                return NetworkDevice.DeviceType.MOBILE
            hostname.contains("tv") || hostname.contains("roku") || hostname.contains("firetv") ->
                return NetworkDevice.DeviceType.SMART_TV
        }

        // Check MAC address vendor
        val macAddress = device.macAddress?.uppercase() ?: ""
        when {
            macAddress.startsWith("00:1A:79") || macAddress.startsWith("00:1D:7E") ->
                return NetworkDevice.DeviceType.PRINTER
            macAddress.startsWith("00:1A:11") || macAddress.startsWith("00:18:DD") ->
                return NetworkDevice.DeviceType.CAMERA
            macAddress.startsWith("B8:27:EB") || macAddress.startsWith("DC:A6:32") ->
                return NetworkDevice.DeviceType.IOT_DEVICE
        }

        // Default to unknown
        return NetworkDevice.DeviceType.UNKNOWN
    }

    /**
     * Perform ping sweep on subnet with optimized performance
     */
    private suspend fun performPingSweep(subnet: String): List<NetworkDevice> = coroutineScope {
        val devices = mutableListOf<NetworkDevice>()

        try {
            // Parse subnet (e.g., ***********/24)
            val parts = subnet.split("/")
            if (parts.size != 2) {
                Log.e(TAG, "Invalid subnet format: $subnet")
                return@coroutineScope devices
            }

            val networkAddress = parts[0]
            val prefixLength = parts[1].toIntOrNull() ?: 24

            // Calculate number of hosts (limit to 254 for a typical /24 network)
            val numHosts = Math.min(Math.pow(2.0, (32 - prefixLength).toDouble()).toInt(), 254)

            // Get base IP parts
            val ipParts = networkAddress.split(".")
            if (ipParts.size != 4) {
                Log.e(TAG, "Invalid IP address format: $networkAddress")
                return@coroutineScope devices
            }

            // Get first three octets
            val baseIp = "${ipParts[0]}.${ipParts[1]}.${ipParts[2]}"

            // Try to get gateway IP
            val gatewayIp = NetworkUtils.getGatewayIp(context)
            if (gatewayIp != null) {
                // Add gateway first (it's definitely there)
                devices.add(NetworkDevice(
                    id = UUID.randomUUID().toString(),
                    ipAddress = gatewayIp,
                    deviceType = NetworkDevice.DeviceType.ROUTER,
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                ))
            }

            // Try to get local IP
            val localIp = NetworkUtils.getLocalIpAddress()
            if (localIp != null && localIp != gatewayIp) {
                // Add local device
                devices.add(NetworkDevice(
                    id = UUID.randomUUID().toString(),
                    ipAddress = localIp,
                    deviceType = NetworkDevice.DeviceType.COMPUTER, // This is the device running the app
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                ))
            }

            // Use a thread pool for better performance
            val executor = Executors.newFixedThreadPool(maxThreads)
            val futures = mutableListOf<Future<NetworkDevice?>>()

            // Submit enhanced ping tasks with multiple detection methods
            for (hostNum in 1..numHosts) {
                // Skip gateway and local IP as we've already added them
                val ipAddress = "$baseIp.$hostNum"
                if (ipAddress == gatewayIp || ipAddress == localIp) {
                    continue
                }

                val future = executor.submit(Callable<NetworkDevice?> {
                    try {
                        // Try multiple detection methods for better device discovery
                        var isReachable = false

                        // Method 1: Standard ping
                        isReachable = pingHostSync(ipAddress)

                        // Method 2: If ping fails, try TCP connect to common ports
                        if (!isReachable) {
                            val commonPorts = listOf(22, 23, 53, 80, 135, 139, 443, 445, 993, 995)
                            for (port in commonPorts) {
                                try {
                                    val socket = Socket()
                                    socket.connect(InetSocketAddress(ipAddress, port), 1000)
                                    socket.close()
                                    isReachable = true
                                    break
                                } catch (e: Exception) {
                                    // Continue to next port
                                }
                            }
                        }

                        // Method 3: Check ARP table for this IP
                        if (!isReachable) {
                            val arpDevices = getDevicesFromArpTableSync()
                            isReachable = arpDevices.any { it.ipAddress == ipAddress }
                        }

                        if (isReachable) {
                            val device = NetworkDevice(
                                id = UUID.randomUUID().toString(),
                                ipAddress = ipAddress,
                                isOnline = true,
                                lastSeen = System.currentTimeMillis()
                            )

                            // Try to get additional device information
                            try {
                                val inetAddress = InetAddress.getByName(ipAddress)
                                device.hostname = inetAddress.hostName
                                if (device.hostname == ipAddress) {
                                    device.hostname = null
                                }
                            } catch (e: Exception) {
                                // Hostname lookup failed
                            }

                            // Try to get MAC address from ARP table
                            getDevicesFromArpTableSync().find { it.ipAddress == ipAddress }?.let { arpDevice ->
                                device.macAddress = arpDevice.macAddress
                                device.manufacturer = arpDevice.manufacturer
                            }

                            device
                        } else {
                            null
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error scanning $ipAddress: ${e.message}")
                        null
                    }
                })

                futures.add(future)
            }

            // Collect results with timeout
            for (future in futures) {
                try {
                    val result = future.get(pingTimeout.toLong() * 2, TimeUnit.MILLISECONDS)
                    if (result != null) {
                        devices.add(result)
                    }
                } catch (e: Exception) {
                    // Ignore timeouts and other errors
                }
            }

            // Shutdown executor
            executor.shutdown()

            // Try alternative method if few devices found
            if (devices.size <= 2) {
                try {
                    // Use system ping command which might be more reliable
                    val commonHosts = listOf(1, 100, 101, 102, 254) // Common host numbers to try

                    for (hostNum in commonHosts) {
                        val ipAddress = "$baseIp.$hostNum"
                        if (devices.any { it.ipAddress == ipAddress }) {
                            continue // Skip if already found
                        }

                        val process = Runtime.getRuntime().exec("ping -c 1 -W 1 $ipAddress")
                        val exitValue = process.waitFor(1, TimeUnit.SECONDS)

                        if (exitValue && process.exitValue() == 0) {
                            devices.add(NetworkDevice(
                                id = UUID.randomUUID().toString(),
                                ipAddress = ipAddress,
                                isOnline = true,
                                lastSeen = System.currentTimeMillis()
                            ))
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "Alternative ping method failed: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during ping sweep: ${e.message}")
        }

        devices
    }

    /**
     * Ping a host to check if it's reachable using multiple methods
     */
    private fun pingHost(ipAddress: String): Boolean {
        // Method 1: Use InetAddress.isReachable
        try {
            val inetAddress = InetAddress.getByName(ipAddress)
            if (inetAddress.isReachable(pingTimeout)) {
                return true
            }
        } catch (e: Exception) {
            // Continue to next method
        }

        // Method 2: Try to establish a socket connection to common ports
        val commonPorts = listOf(80, 443, 22, 445, 139, 53)
        for (port in commonPorts) {
            try {
                val socket = Socket()
                socket.soTimeout = pingTimeout / 2
                socket.connect(InetSocketAddress(ipAddress, port), pingTimeout / 2)
                socket.close()
                return true
            } catch (e: Exception) {
                // Try next port
            }
        }

        // Method 3: Try to use system ping command
        try {
            val process = Runtime.getRuntime().exec("ping -c 1 -W 1 $ipAddress")
            return process.waitFor(1, TimeUnit.SECONDS) && process.exitValue() == 0
        } catch (e: Exception) {
            // Ignore and return false
        }

        return false
    }

    /**
     * Enhance device information with hostname, manufacturer, and open ports
     */
    private suspend fun enhanceDeviceInfo(devices: List<NetworkDevice>): List<NetworkDevice> = coroutineScope {
        devices.map { device ->
            async {
                try {
                    // Try to resolve hostname
                    if (device.hostname == null) {
                        device.hostname = resolveHostname(device.ipAddress)
                    }

                    // Use API-based identification instead of MAC vendor lookup
                    val macAddress = device.macAddress
                    if (macAddress != null && device.manufacturer == null) {
                        // We'll use Shodan or Gemini API for this later
                        device.manufacturer = "Unknown Manufacturer"
                    }

                    // Scan for open ports
                    val openPorts = scanPorts(device.ipAddress)
                    device.openPorts.addAll(openPorts)

                    // Add service information for open ports
                    openPorts.forEach { port ->
                        val serviceName = NetworkScanner.COMMON_SERVICES[port]
                        if (serviceName != null) {
                            device.services[port] = serviceName
                        }
                    }

                    // Try to determine device type using AI and other methods
                    if (device.deviceType == null) {
                        device.deviceType = determineDeviceType(device)

                        // If we have enough information, use Gemini AI for more accurate identification
                        if (device.macAddress != null || device.openPorts.isNotEmpty() || device.hostname != null) {
                            try {
                                // Temporarily disabled AI identification until API is properly set up
                                // identifyDeviceWithAI(device)
                                Log.d(TAG, "AI identification temporarily disabled")
                            } catch (e: Exception) {
                                Log.e(TAG, "Error identifying device with AI: ${e.message}")
                            }
                        }
                    }

                    device
                } catch (e: Exception) {
                    Log.e(TAG, "Error enhancing device info for ${device.ipAddress}: ${e.message}")
                    device
                }
            }
        }.awaitAll()
    }

    /**
     * Resolve hostname for an IP address
     */
    private fun resolveHostname(ipAddress: String): String? {
        return try {
            val inetAddress = InetAddress.getByName(ipAddress)
            val hostname = inetAddress.hostName

            // If hostname is the same as IP address, no hostname was found
            if (hostname != ipAddress) hostname else null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Scan for open ports on a host using parallel scanning
     */
    private suspend fun scanPorts(ipAddress: String): List<Int> = coroutineScope {
        val openPorts = mutableListOf<Int>()
        val serviceInfo = mutableMapOf<Int, String>()

        try {
            // Determine which ports to scan
            val portsToScan = if (scanCommonPortsOnly) {
                COMMON_PORTS
            } else {
                // Scan more ports in thorough mode
                COMMON_PORTS + ADDITIONAL_PORTS
            }

            // Create a job for each port to scan in parallel
            val portJobs = portsToScan.map { port ->
                async(Dispatchers.IO) {
                    try {
                        val socket = Socket()
                        socket.soTimeout = socketTimeout
                        socket.connect(InetSocketAddress(ipAddress, port), socketTimeout)

                        // Try to get banner information
                        var banner: String? = null
                        try {
                            if (listOf(21, 22, 23, 25, 80, 110, 143, 8080, 8443).contains(port)) {
                                val input = socket.getInputStream()
                                val reader = BufferedReader(InputStreamReader(input))
                                banner = reader.readLine()

                                // Store banner information
                                if (!banner.isNullOrEmpty()) {
                                    serviceInfo[port] = banner
                                }
                            }
                        } catch (e: Exception) {
                            // Failed to get banner, but port is still open
                        }

                        socket.close()
                        Pair(port, banner)
                    } catch (e: Exception) {
                        // Port is closed or filtered
                        null
                    }
                }
            }

            // Collect results
            val results = portJobs.awaitAll().filterNotNull()

            // Add open ports to the list
            synchronized(openPorts) {
                results.forEach { (port, banner) ->
                    openPorts.add(port)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error scanning ports for $ipAddress: ${e.message}")
        }

        openPorts
    }

    /**
     * Determine device type based on open ports, hostname, and other information
     */
    private fun determineDeviceType(device: NetworkDevice): NetworkDevice.DeviceType {
        // Check if it's a router
        val gatewayIp = NetworkUtils.getGatewayIp(context)
        if (device.ipAddress == gatewayIp) {
            return NetworkDevice.DeviceType.ROUTER
        }

        // Check open ports for device type hints
        val openPorts = device.openPorts
        val hostname = device.hostname?.lowercase() ?: ""
        val manufacturer = device.manufacturer?.lowercase() ?: ""

        // Check for printer
        if (openPorts.contains(9100) || openPorts.contains(515) || openPorts.contains(631)) {
            return NetworkDevice.DeviceType.PRINTER
        }

        // Check for NAS/storage device
        if (openPorts.contains(445) || openPorts.contains(139) || openPorts.contains(111) || openPorts.contains(2049)) {
            return NetworkDevice.DeviceType.NAS
        }

        // Check for camera
        if (openPorts.contains(554) ||
            (openPorts.contains(80) && (hostname.contains("cam") || hostname.contains("ipcam"))) ||
            (openPorts.contains(8000) && hostname.contains("cam")) ||
            (openPorts.contains(8080) && hostname.contains("cam"))) {
            return NetworkDevice.DeviceType.CAMERA
        }

        // Check for media device
        if (openPorts.contains(1900) || openPorts.contains(8008) || openPorts.contains(8009) || openPorts.contains(32469)) {
            return NetworkDevice.DeviceType.MEDIA_DEVICE
        }

        // Check for smart TV
        if (openPorts.contains(8001) || openPorts.contains(8002) ||
            (openPorts.contains(8080) && (hostname.contains("tv") || manufacturer.contains("tv")))) {
            return NetworkDevice.DeviceType.SMART_TV
        }

        // Check for gaming console
        if (openPorts.contains(3074) || openPorts.contains(3075) ||
            openPorts.contains(27015) || openPorts.contains(27016)) {
            return NetworkDevice.DeviceType.GAMING_CONSOLE
        }

        // Check for IoT device
        if (openPorts.contains(1883) || openPorts.contains(8883) ||
            openPorts.contains(5683) || openPorts.contains(5684)) {
            return NetworkDevice.DeviceType.IOT_DEVICE
        }

        // Check hostname for additional hints
        when {
            hostname.contains("printer") || hostname.contains("print") -> return NetworkDevice.DeviceType.PRINTER
            hostname.contains("cam") -> return NetworkDevice.DeviceType.CAMERA
            hostname.contains("nas") || hostname.contains("storage") -> return NetworkDevice.DeviceType.NAS
            hostname.contains("tv") || hostname.contains("television") -> return NetworkDevice.DeviceType.SMART_TV
            hostname.contains("xbox") || hostname.contains("playstation") || hostname.contains("ps") -> return NetworkDevice.DeviceType.GAMING_CONSOLE
            hostname.contains("phone") || hostname.contains("mobile") || hostname.contains("android") || hostname.contains("iphone") -> return NetworkDevice.DeviceType.MOBILE
            hostname.contains("laptop") || hostname.contains("desktop") || hostname.contains("pc") -> return NetworkDevice.DeviceType.COMPUTER
        }

        // Check manufacturer for additional hints
        when {
            manufacturer.contains("raspberry") -> return NetworkDevice.DeviceType.IOT_DEVICE
            manufacturer.contains("espressif") -> return NetworkDevice.DeviceType.IOT_DEVICE
            manufacturer.contains("nest") -> return NetworkDevice.DeviceType.IOT_DEVICE
            manufacturer.contains("sonos") -> return NetworkDevice.DeviceType.MEDIA_DEVICE
            manufacturer.contains("roku") -> return NetworkDevice.DeviceType.MEDIA_DEVICE
            manufacturer.contains("samsung") && (openPorts.contains(8001) || hostname.contains("tv")) -> return NetworkDevice.DeviceType.SMART_TV
            manufacturer.contains("lg") && (openPorts.contains(8080) || hostname.contains("tv")) -> return NetworkDevice.DeviceType.SMART_TV
            manufacturer.contains("apple") && hostname.contains("iphone") -> return NetworkDevice.DeviceType.MOBILE
            manufacturer.contains("apple") && hostname.contains("macbook") -> return NetworkDevice.DeviceType.COMPUTER
            manufacturer.contains("microsoft") && hostname.contains("xbox") -> return NetworkDevice.DeviceType.GAMING_CONSOLE
            manufacturer.contains("sony") && (hostname.contains("playstation") || hostname.contains("ps")) -> return NetworkDevice.DeviceType.GAMING_CONSOLE
        }

        // Check if it's a server based on open ports
        if (openPorts.contains(22) && (openPorts.contains(80) || openPorts.contains(443)) &&
            (openPorts.contains(3306) || openPorts.contains(5432) || openPorts.contains(27017))) {
            return NetworkDevice.DeviceType.SERVER
        }

        // Check if it's a computer based on common ports
        if ((openPorts.contains(22) || openPorts.contains(3389)) &&
            (openPorts.contains(139) || openPorts.contains(445))) {
            return NetworkDevice.DeviceType.COMPUTER
        }

        // Default to unknown
        return NetworkDevice.DeviceType.UNKNOWN
    }

    // MAC vendor lookup removed - using API-based identification instead

    /**
     * Assess vulnerabilities for a list of devices
     */
    suspend fun assessVulnerabilities(devices: List<NetworkDevice>): List<Vulnerability> = withContext(Dispatchers.IO) {
        val vulnerabilities = mutableListOf<Vulnerability>()

        devices.forEach { device ->
            // Check for open vulnerable ports
            device.openPorts.forEach { port ->
                when (port) {
                    21 -> { // FTP
                        vulnerabilities.add(
                            Vulnerability(
                                id = UUID.randomUUID().toString(),
                                name = "Unencrypted FTP Service",
                                description = "Device ${device.ipAddress} is running an unencrypted FTP service which transmits credentials in plaintext.",
                                severity = Vulnerability.Severity.HIGH,
                                recommendation = "Replace with SFTP or FTPS, or disable if not needed.",
                                deviceId = device.id,
                                deviceIpAddress = device.ipAddress
                            )
                        )
                    }
                    23 -> { // Telnet
                        vulnerabilities.add(
                            Vulnerability(
                                id = UUID.randomUUID().toString(),
                                name = "Telnet Service Enabled",
                                description = "Device ${device.ipAddress} is running Telnet which transmits data in plaintext.",
                                severity = Vulnerability.Severity.CRITICAL,
                                recommendation = "Replace with SSH or disable if not needed.",
                                deviceId = device.id,
                                deviceIpAddress = device.ipAddress
                            )
                        )
                    }
                    25 -> { // SMTP
                        if (!device.openPorts.contains(465) && !device.openPorts.contains(587)) {
                            vulnerabilities.add(
                                Vulnerability(
                                    id = UUID.randomUUID().toString(),
                                    name = "Unencrypted SMTP Service",
                                    description = "Device ${device.ipAddress} is running unencrypted SMTP which may expose email credentials.",
                                    severity = Vulnerability.Severity.HIGH,
                                    recommendation = "Configure SMTP with TLS/SSL on ports 465 or 587.",
                                    deviceId = device.id,
                                    deviceIpAddress = device.ipAddress
                                )
                            )
                        }
                    }
                    80 -> { // HTTP
                        if (!device.openPorts.contains(443)) {
                            vulnerabilities.add(
                                Vulnerability(
                                    id = UUID.randomUUID().toString(),
                                    name = "Unencrypted HTTP Service",
                                    description = "Device ${device.ipAddress} is running HTTP without HTTPS, potentially exposing sensitive data.",
                                    severity = Vulnerability.Severity.MEDIUM,
                                    recommendation = "Enable HTTPS with a valid certificate.",
                                    deviceId = device.id,
                                    deviceIpAddress = device.ipAddress
                                )
                            )
                        }
                    }
                    110 -> { // POP3
                        if (!device.openPorts.contains(995)) {
                            vulnerabilities.add(
                                Vulnerability(
                                    id = UUID.randomUUID().toString(),
                                    name = "Unencrypted POP3 Service",
                                    description = "Device ${device.ipAddress} is running unencrypted POP3 which may expose email credentials.",
                                    severity = Vulnerability.Severity.HIGH,
                                    recommendation = "Configure POP3 with SSL on port 995.",
                                    deviceId = device.id,
                                    deviceIpAddress = device.ipAddress
                                )
                            )
                        }
                    }
                    143 -> { // IMAP
                        if (!device.openPorts.contains(993)) {
                            vulnerabilities.add(
                                Vulnerability(
                                    id = UUID.randomUUID().toString(),
                                    name = "Unencrypted IMAP Service",
                                    description = "Device ${device.ipAddress} is running unencrypted IMAP which may expose email credentials.",
                                    severity = Vulnerability.Severity.HIGH,
                                    recommendation = "Configure IMAP with SSL on port 993.",
                                    deviceId = device.id,
                                    deviceIpAddress = device.ipAddress
                                )
                            )
                        }
                    }
                    3389 -> { // RDP
                        vulnerabilities.add(
                            Vulnerability(
                                id = UUID.randomUUID().toString(),
                                name = "Remote Desktop Protocol Exposed",
                                description = "Device ${device.ipAddress} has RDP exposed, which could be a target for brute force attacks.",
                                severity = Vulnerability.Severity.HIGH,
                                recommendation = "Restrict RDP access using a firewall or VPN, enable Network Level Authentication, and use strong passwords.",
                                deviceId = device.id,
                                deviceIpAddress = device.ipAddress
                            )
                        )
                    }
                    445 -> { // SMB
                        vulnerabilities.add(
                            Vulnerability(
                                id = UUID.randomUUID().toString(),
                                name = "SMB Service Exposed",
                                description = "Device ${device.ipAddress} has SMB file sharing exposed, which could be vulnerable to attacks.",
                                severity = Vulnerability.Severity.MEDIUM,
                                recommendation = "Restrict SMB access with firewall rules or disable if not needed."
                            )
                        )
                    }
                }
            }

            // Check for default credentials based on device type
            if (device.deviceType == NetworkDevice.DeviceType.ROUTER ||
                device.deviceType == NetworkDevice.DeviceType.CAMERA ||
                device.deviceType == NetworkDevice.DeviceType.IOT_DEVICE) {

                vulnerabilities.add(
                    Vulnerability(
                        id = UUID.randomUUID().toString(),
                        name = "Potential Default Credentials",
                        description = "Device ${device.ipAddress} (${device.deviceType}) may be using default credentials.",
                        severity = Vulnerability.Severity.HIGH,
                        recommendation = "Change default passwords to strong, unique passwords."
                    )
                )
            }
        }

        vulnerabilities
    }

    /**
     * Identify device using AI
     */
    private suspend fun identifyDeviceWithAI(device: NetworkDevice) {
        try {
            // Use Gemini AI to identify the device
            val result = geminiApiClient.identifyDevice(
                macAddress = device.macAddress,
                openPorts = device.openPorts,
                hostname = device.hostname
            )

            result.onSuccess { jsonText ->
                try {
                    // Parse the JSON response
                    val jsonObject = JSONObject(jsonText)

                    // Extract device type
                    val deviceTypeStr = jsonObject.optString("deviceType", "UNKNOWN")
                    if (deviceTypeStr != "UNKNOWN") {
                        try {
                            device.deviceType = NetworkDevice.DeviceType.valueOf(deviceTypeStr)
                        } catch (e: Exception) {
                            Log.e(TAG, "Invalid device type: $deviceTypeStr")
                        }
                    }

                    // Extract manufacturer
                    val manufacturer = jsonObject.optString("manufacturer", null)
                    if (!manufacturer.isNullOrEmpty() && manufacturer != "UNKNOWN") {
                        device.manufacturer = manufacturer
                    }

                    // Extract model
                    val model = jsonObject.optString("model", null)
                    if (!model.isNullOrEmpty() && model != "UNKNOWN") {
                        device.model = model
                    }

                    // Add explanation as a note
                    val explanation = jsonObject.optString("explanation", null)
                    if (!explanation.isNullOrEmpty()) {
                        device.notes = explanation
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing AI response: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in AI device identification: ${e.message}")
        }

        // If we have a MAC address, also try to get information from Shodan
        try {
            val macAddress = device.macAddress
            if (macAddress != null) {
                // Note: Shodan doesn't directly support MAC lookup, so we'll use IP instead
                val shodanResult = shodanApiClient.getHostInfo(device.ipAddress)

                shodanResult.onSuccess { jsonObject ->
                    // Extract OS information
                    val os = jsonObject.optString("os", null)
                    if (os != null && os.isNotEmpty() && device.operatingSystem == null) {
                        device.operatingSystem = os
                    }

                    // Extract product information
                    val product = jsonObject.optString("product", null)
                    if (product != null && product.isNotEmpty() && device.model == null) {
                        device.model = product
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Shodan info: ${e.message}")
        }
    }

    /**
     * Scan a specific device for detailed information
     *
     * @param ipAddress The IP address of the device to scan
     * @param scanPorts Whether to scan for open ports
     * @param scanServices Whether to identify services on open ports
     * @return The scanned device or null if scanning failed
     */
    suspend fun scanDevice(
        ipAddress: String,
        scanPorts: Boolean = true,
        scanServices: Boolean = true
    ): NetworkDevice? = withContext(Dispatchers.IO) {
        try {
            // Create basic device
            val device = NetworkDevice(
                id = UUID.randomUUID().toString(),
                ipAddress = ipAddress
            )

            // Try to get MAC address from ARP table
            getDevicesFromArpTable().find { it.ipAddress == ipAddress }?.let {
                device.macAddress = it.macAddress
                device.manufacturer = it.manufacturer
            }

            // Try to resolve hostname
            device.hostname = resolveHostname(ipAddress)

            // Check if device is reachable
            val isReachable = pingHost(ipAddress)
            if (!isReachable) {
                Log.d(TAG, "Device $ipAddress is not reachable")
                // Still continue with the scan as some devices may block ICMP
            }

            // Scan for open ports if requested
            if (scanPorts) {
                // Perform deeper port scan
                val allPorts = scanAllPorts(ipAddress)
                device.openPorts.addAll(allPorts)

                // Add service information for open ports if requested
                if (scanServices) {
                    allPorts.forEach { port ->
                        if (!device.services.containsKey(port)) {
                            NetworkScanner.COMMON_SERVICES[port]?.let { serviceName ->
                                device.services[port] = serviceName
                            }
                        }
                    }
                }
            }

            // Determine device type
            device.deviceType = determineDeviceType(device)

            // Try to get additional information from Shodan
            try {
                val shodanResult = shodanApiClient.getHostInfo(ipAddress)
                shodanResult.onSuccess { jsonObject ->
                    // Extract OS information
                    val os = jsonObject.optString("os", null)
                    if (os != null && os.isNotEmpty()) {
                        device.operatingSystem = os
                    }

                    // Extract product information
                    val product = jsonObject.optString("product", null)
                    if (product != null && product.isNotEmpty()) {
                        device.model = product
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting Shodan info for $ipAddress: ${e.message}")
            }

            device
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning device $ipAddress: ${e.message}")
            null
        }
    }

    /**
     * Scan all ports on a host (more comprehensive than scanPorts)
     */
    private suspend fun scanAllPorts(ipAddress: String): List<Int> = coroutineScope {
        val openPorts = mutableListOf<Int>()

        try {
            // Define port ranges to scan based on scan mode
            val portRanges = if (scanCommonPortsOnly) {
                listOf(
                    1..1024            // Well-known ports only
                )
            } else {
                listOf(
                    1..1024,           // Well-known ports
                    1025..5000 step 10, // Registered ports (sample every 10)
                    5001..10000 step 50 // Dynamic ports (sample every 50)
                )
            }

            // Scan port ranges in parallel using a thread pool
            val executor = Executors.newFixedThreadPool(maxThreads)
            val futures = mutableListOf<Future<Int?>>()

            // Submit scanning tasks
            portRanges.forEach { range ->
                range.forEach { port ->
                    val future = executor.submit(Callable<Int?> {
                        try {
                            val socket = Socket()
                            socket.soTimeout = socketTimeout
                            socket.connect(InetSocketAddress(ipAddress, port), socketTimeout)
                            socket.close()
                            port
                        } catch (e: Exception) {
                            // Port is closed or filtered
                            null
                        }
                    })
                    futures.add(future)
                }
            }

            // Collect results
            for (future in futures) {
                try {
                    val result = future.get(socketTimeout.toLong() * 2, TimeUnit.MILLISECONDS)
                    if (result != null) {
                        openPorts.add(result)
                    }
                } catch (e: Exception) {
                    // Ignore timeouts and other errors
                }
            }

            // Shutdown executor
            executor.shutdown()

        } catch (e: Exception) {
            Log.e(TAG, "Error scanning all ports for $ipAddress: ${e.message}")
        }

        openPorts
    }

    /**
     * Wake up a device using Wake-on-LAN
     *
     * @param macAddress The MAC address of the device to wake up (format: XX:XX:XX:XX:XX:XX)
     * @param ipAddress Optional IP address to send the packet to (default: broadcast)
     * @return True if the packet was sent successfully, false otherwise
     */
    suspend fun wakeDevice(macAddress: String, ipAddress: String? = null): Boolean {
        try {
            return wakeOnLanManager.wakeDevice(macAddress, ipAddress)
        } catch (e: Exception) {
            Log.e(TAG, "Error waking device: ${e.message}")
            return false
        }
    }

    /**
     * Check if a device supports Wake-on-LAN
     *
     * @param device The device to check
     * @return True if the device likely supports Wake-on-LAN, false otherwise
     */
    fun deviceSupportsWakeOnLan(device: NetworkDevice): Boolean {
        // Check if device has a MAC address
        if (device.macAddress.isNullOrEmpty()) {
            return false
        }

        // Check device type (computers, gaming consoles, and some media devices typically support WoL)
        return when (device.deviceType) {
            NetworkDevice.DeviceType.COMPUTER,
            NetworkDevice.DeviceType.SERVER,
            NetworkDevice.DeviceType.GAMING_CONSOLE,
            NetworkDevice.DeviceType.MEDIA_DEVICE,
            NetworkDevice.DeviceType.NAS -> true
            else -> false
        }
    }

    /**
     * Synchronous version of pingHost for use in thread pools
     */
    private fun pingHostSync(ipAddress: String): Boolean {
        return try {
            val address = InetAddress.getByName(ipAddress)
            address.isReachable(pingTimeout)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Synchronous version of getDevicesFromArpTable for use in thread pools
     */
    private fun getDevicesFromArpTableSync(): List<NetworkDevice> {
        return try {
            val devices = mutableListOf<NetworkDevice>()
            val process = Runtime.getRuntime().exec("cat /proc/net/arp")
            val reader = BufferedReader(InputStreamReader(process.inputStream))

            reader.useLines { lines ->
                lines.drop(1).forEach { line ->
                    val parts = line.split("\\s+".toRegex())
                    if (parts.size >= 6) {
                        val ip = parts[0]
                        val mac = parts[3]

                        if (mac != "00:00:00:00:00:00" && ip != "0.0.0.0") {
                            devices.add(NetworkDevice(
                                id = UUID.randomUUID().toString(),
                                ipAddress = ip,
                                macAddress = mac,
                                isOnline = true,
                                lastSeen = System.currentTimeMillis()
                            ))
                        }
                    }
                }
            }

            devices
        } catch (e: Exception) {
            Log.e(TAG, "Error reading ARP table: ${e.message}")
            emptyList()
        }
    }
}
