package com.zilal.networkguardian.network

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import com.zilal.networkguardian.model.NetworkDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import org.xml.sax.InputSource
import org.xml.sax.SAXException
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.io.StringReader
import java.net.HttpURLConnection
import java.net.InetAddress
import java.net.MulticastSocket
import java.net.URL
import java.net.DatagramPacket
import java.net.DatagramSocket
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException

/**
 * Manager class for UPnP (Universal Plug and Play) discovery
 * This class discovers UPnP devices on the network
 */
class UPnPDiscoveryManager(private val context: Context) {

    private val TAG = "UPnPDiscoveryManager"
    private val SSDP_PORT = 1900
    private val SSDP_ADDR = "***************"
    private val SSDP_MX = 3
    private val SSDP_ST = "ssdp:all"
    private val SSDP_SEARCH_MSG = """
        M-SEARCH * HTTP/1.1
        HOST: $SSDP_ADDR:$SSDP_PORT
        MAN: "ssdp:discover"
        MX: $SSDP_MX
        ST: $SSDP_ST

        """.trimIndent().replace("\n", "\r\n")

    private val discoveredDevices = ConcurrentHashMap<String, NetworkDevice>()
    private val discoveryChannel = Channel<NetworkDevice>(Channel.BUFFERED)
    private var isDiscovering = false

    /**
     * Start discovering UPnP devices
     * @return Flow of discovered devices
     */
    fun startDiscovery(): Flow<NetworkDevice> = flow {
        if (isDiscovering) {
            Log.d(TAG, "UPnP discovery already in progress")
            return@flow
        }

        isDiscovering = true
        discoveredDevices.clear()

        try {
            // Acquire multicast lock to allow multicast packets
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val multicastLock = wifiManager.createMulticastLock("UPnPDiscoveryManager")
            multicastLock.setReferenceCounted(true)
            multicastLock.acquire()

            withContext(Dispatchers.IO) {
                try {
                    // Create socket for SSDP discovery
                    val socket = DatagramSocket()
                    socket.soTimeout = (SSDP_MX * 1000) + 1000 // MX seconds + 1 second buffer

                    // Send SSDP discovery message
                    val ssdpAddr = InetAddress.getByName(SSDP_ADDR)
                    val requestBytes = SSDP_SEARCH_MSG.toByteArray()
                    val requestPacket = DatagramPacket(requestBytes, requestBytes.size, ssdpAddr, SSDP_PORT)
                    socket.send(requestPacket)

                    Log.d(TAG, "Sent UPnP discovery request")

                    // Receive responses
                    val responseBuffer = ByteArray(8192)
                    val responsePacket = DatagramPacket(responseBuffer, responseBuffer.size)

                    val endTime = System.currentTimeMillis() + (SSDP_MX * 1000) + 2000
                    while (System.currentTimeMillis() < endTime) {
                        try {
                            socket.receive(responsePacket)
                            val response = String(responsePacket.data, 0, responsePacket.length)
                            val deviceInfo = parseUPnPResponse(response)

                            if (deviceInfo != null) {
                                val ipAddress = responsePacket.address.hostAddress
                                val device = createDeviceFromUPnP(deviceInfo, ipAddress)

                                if (!discoveredDevices.containsKey(ipAddress)) {
                                    discoveredDevices[ipAddress] = device
                                    discoveryChannel.trySend(device)
                                    // Don't emit here, we'll collect and emit outside withContext

                                    Log.d(TAG, "UPnP found device: $ipAddress, ${device.hostname}")
                                }
                            }
                        } catch (e: IOException) {
                            // Socket timeout, continue
                        }
                    }

                    socket.close()
                } catch (e: Exception) {
                    Log.e(TAG, "Error during UPnP discovery: ${e.message}")
                }
            }

            // Now emit all discovered devices
            for (device in discoveredDevices.values) {
                emit(device)
            }

            // Release multicast lock
            if (multicastLock.isHeld) {
                multicastLock.release()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during UPnP discovery: ${e.message}")
        } finally {
            isDiscovering = false
        }
    }

    /**
     * Parse UPnP response to extract device information
     */
    private fun parseUPnPResponse(response: String): Map<String, String>? {
        try {
            val deviceInfo = mutableMapOf<String, String>()
            val lines = response.split("\r\n")

            // Extract location URL
            val locationLine = lines.find { it.startsWith("LOCATION:", ignoreCase = true) }
            val location = locationLine?.substring(9)?.trim()

            if (location != null) {
                deviceInfo["LOCATION"] = location

                // Extract server info
                val serverLine = lines.find { it.startsWith("SERVER:", ignoreCase = true) }
                if (serverLine != null) {
                    deviceInfo["SERVER"] = serverLine.substring(7).trim()
                }

                // Extract device type
                val stLine = lines.find { it.startsWith("ST:", ignoreCase = true) }
                if (stLine != null) {
                    deviceInfo["ST"] = stLine.substring(3).trim()
                }

                // Extract USN (Unique Service Name)
                val usnLine = lines.find { it.startsWith("USN:", ignoreCase = true) }
                if (usnLine != null) {
                    deviceInfo["USN"] = usnLine.substring(4).trim()
                }

                // Fetch device description XML
                try {
                    val descriptionXml = fetchDeviceDescription(location)
                    if (descriptionXml != null) {
                        val deviceDetails = parseDeviceDescription(descriptionXml)
                        deviceInfo.putAll(deviceDetails)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching device description: ${e.message}")
                }

                return deviceInfo
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing UPnP response: ${e.message}")
        }

        return null
    }

    /**
     * Fetch device description XML from location URL
     */
    private fun fetchDeviceDescription(locationUrl: String): String? {
        try {
            val url = URL(locationUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = 5000
            connection.readTimeout = 5000

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = StringBuilder()
                var line: String?

                while (reader.readLine().also { line = it } != null) {
                    response.append(line).append("\n")
                }

                reader.close()
                return response.toString()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching device description: ${e.message}")
        }

        return null
    }

    /**
     * Parse device description XML to extract device details
     */
    private fun parseDeviceDescription(xml: String): Map<String, String> {
        val deviceDetails = mutableMapOf<String, String>()

        try {
            val factory = DocumentBuilderFactory.newInstance()
            val builder = factory.newDocumentBuilder()
            val document = builder.parse(InputSource(StringReader(xml)))

            // Extract device friendly name
            val friendlyNameNodes = document.getElementsByTagName("friendlyName")
            if (friendlyNameNodes.length > 0) {
                deviceDetails["friendlyName"] = friendlyNameNodes.item(0).textContent
            }

            // Extract device manufacturer
            val manufacturerNodes = document.getElementsByTagName("manufacturer")
            if (manufacturerNodes.length > 0) {
                deviceDetails["manufacturer"] = manufacturerNodes.item(0).textContent
            }

            // Extract device model name
            val modelNameNodes = document.getElementsByTagName("modelName")
            if (modelNameNodes.length > 0) {
                deviceDetails["modelName"] = modelNameNodes.item(0).textContent
            }

            // Extract device type
            val deviceTypeNodes = document.getElementsByTagName("deviceType")
            if (deviceTypeNodes.length > 0) {
                deviceDetails["deviceType"] = deviceTypeNodes.item(0).textContent
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing device description XML: ${e.message}")
        }

        return deviceDetails
    }

    /**
     * Create a NetworkDevice from UPnP device information
     */
    private fun createDeviceFromUPnP(deviceInfo: Map<String, String>, ipAddress: String): NetworkDevice {
        // Use friendly name as hostname, or fallback to model name or "UPnP Device"
        val hostname = deviceInfo["friendlyName"] ?: deviceInfo["modelName"] ?: "UPnP Device"

        // Determine device type based on UPnP device type
        val deviceType = determineDeviceType(deviceInfo)

        // Get manufacturer
        val manufacturer = deviceInfo["manufacturer"] ?: "Unknown"

        // Create the device
        return NetworkDevice(
            ipAddress = ipAddress,
            hostname = hostname,
            macAddress = null, // Will be filled in later
            deviceType = deviceType,
            manufacturer = manufacturer,
            lastSeen = System.currentTimeMillis(),
            detectionMethod = "UPnP"
        )
    }

    /**
     * Determine device type based on UPnP device information
     */
    private fun determineDeviceType(deviceInfo: Map<String, String>): NetworkDevice.DeviceType {
        val deviceTypeStr = deviceInfo["deviceType"] ?: ""
        val friendlyName = deviceInfo["friendlyName"]?.lowercase() ?: ""
        val modelName = deviceInfo["modelName"]?.lowercase() ?: ""
        val server = deviceInfo["SERVER"]?.lowercase() ?: ""

        return when {
            deviceTypeStr.contains("MediaRenderer") ||
                friendlyName.contains("tv") ||
                modelName.contains("tv") -> NetworkDevice.DeviceType.SMART_TV

            deviceTypeStr.contains("MediaServer") ||
                friendlyName.contains("nas") ||
                modelName.contains("nas") -> NetworkDevice.DeviceType.NAS

            deviceTypeStr.contains("InternetGatewayDevice") ||
                friendlyName.contains("router") ||
                modelName.contains("router") -> NetworkDevice.DeviceType.ROUTER

            deviceTypeStr.contains("PrintDevice") ||
                friendlyName.contains("printer") ||
                modelName.contains("printer") -> NetworkDevice.DeviceType.PRINTER

            friendlyName.contains("camera") ||
                modelName.contains("camera") -> NetworkDevice.DeviceType.CAMERA

            server.contains("android") -> NetworkDevice.DeviceType.MOBILE

            else -> NetworkDevice.DeviceType.UNKNOWN
        }
    }

    /**
     * Stop discovery
     */
    fun stopDiscovery() {
        isDiscovering = false
        discoveryChannel.close()
    }
}
