package com.zilal.networkguardian.network

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Network Tools - DNS Analysis, Ping, and Traceroute functionality
 * Provides comprehensive network diagnostic tools like Fing
 */
@Singleton
class NetworkTools @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _pingResults = MutableStateFlow<List<PingResult>>(emptyList())
    val pingResults: StateFlow<List<PingResult>> = _pingResults.asStateFlow()
    
    private val _tracerouteResults = MutableStateFlow<List<TracerouteHop>>(emptyList())
    val tracerouteResults: StateFlow<List<TracerouteHop>> = _tracerouteResults.asStateFlow()
    
    private val _dnsResults = MutableStateFlow<DNSAnalysisResult?>(null)
    val dnsResults: StateFlow<DNSAnalysisResult?> = _dnsResults.asStateFlow()
    
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()
    
    companion object {
        private const val TAG = "NetworkTools"
        private const val DEFAULT_PING_COUNT = 4
        private const val MAX_TRACEROUTE_HOPS = 30
        private const val PING_TIMEOUT = 5000
    }
    
    /**
     * Perform ping test to a host
     */
    fun performPing(
        host: String, 
        count: Int = DEFAULT_PING_COUNT,
        onResult: (PingResult) -> Unit = {}
    ) {
        if (_isRunning.value) return
        
        _isRunning.value = true
        _pingResults.value = emptyList()
        
        scope.launch {
            try {
                val results = mutableListOf<PingResult>()
                
                repeat(count) { i ->
                    val result = performSinglePing(host, i + 1)
                    results.add(result)
                    _pingResults.value = results.toList()
                    onResult(result)
                    
                    if (i < count - 1) {
                        delay(1000) // 1 second between pings
                    }
                }
                
                val summary = calculatePingSummary(results)
                analyticsManager.trackEvent(
                    eventName = "ping_completed",
                    properties = mapOf(
                        "host" to host,
                        "count" to count,
                        "avg_time" to summary.averageTime,
                        "packet_loss" to summary.packetLoss
                    )
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Ping failed", e)
                analyticsManager.trackError("ping_error", e.message ?: "Unknown error")
            } finally {
                _isRunning.value = false
            }
        }
    }
    
    /**
     * Perform single ping
     */
    private suspend fun performSinglePing(host: String, sequence: Int): PingResult {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.nanoTime()
                val address = InetAddress.getByName(host)
                val reachable = address.isReachable(PING_TIMEOUT)
                val endTime = System.nanoTime()
                
                val responseTime = if (reachable) {
                    (endTime - startTime) / 1_000_000.0 // Convert to milliseconds
                } else {
                    -1.0
                }
                
                PingResult(
                    sequence = sequence,
                    host = host,
                    ipAddress = address.hostAddress ?: "Unknown",
                    responseTime = responseTime,
                    success = reachable,
                    timestamp = System.currentTimeMillis()
                )
                
            } catch (e: Exception) {
                PingResult(
                    sequence = sequence,
                    host = host,
                    ipAddress = "Unknown",
                    responseTime = -1.0,
                    success = false,
                    timestamp = System.currentTimeMillis(),
                    error = e.message
                )
            }
        }
    }
    
    /**
     * Perform traceroute to a host
     */
    fun performTraceroute(host: String) {
        if (_isRunning.value) return
        
        _isRunning.value = true
        _tracerouteResults.value = emptyList()
        
        scope.launch {
            try {
                val results = mutableListOf<TracerouteHop>()
                
                // Try to resolve the target first
                val targetAddress = try {
                    InetAddress.getByName(host)
                } catch (e: Exception) {
                    _isRunning.value = false
                    return@launch
                }
                
                // Perform traceroute using ping with increasing TTL
                for (ttl in 1..MAX_TRACEROUTE_HOPS) {
                    val hop = performTracerouteHop(targetAddress.hostAddress!!, ttl)
                    results.add(hop)
                    _tracerouteResults.value = results.toList()
                    
                    // Stop if we reached the target
                    if (hop.ipAddress == targetAddress.hostAddress) {
                        break
                    }
                    
                    // Stop if we hit too many timeouts
                    if (results.takeLast(3).all { !it.success }) {
                        break
                    }
                    
                    delay(500) // Small delay between hops
                }
                
                analyticsManager.trackEvent(
                    eventName = "traceroute_completed",
                    properties = mapOf(
                        "host" to host,
                        "hops" to results.size,
                        "success" to results.any { it.success }
                    )
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Traceroute failed", e)
                analyticsManager.trackError("traceroute_error", e.message ?: "Unknown error")
            } finally {
                _isRunning.value = false
            }
        }
    }
    
    /**
     * Perform single traceroute hop
     */
    private suspend fun performTracerouteHop(targetIp: String, ttl: Int): TracerouteHop {
        return withContext(Dispatchers.IO) {
            try {
                // Use system ping command with TTL
                val process = Runtime.getRuntime().exec("ping -c 1 -t $ttl $targetIp")
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                val errorReader = BufferedReader(InputStreamReader(process.errorStream))
                
                val startTime = System.nanoTime()
                val exitCode = process.waitFor()
                val endTime = System.nanoTime()
                
                val responseTime = (endTime - startTime) / 1_000_000.0 // Convert to milliseconds
                
                var hopIp = "*"
                var hostname = "*"
                
                // Parse output to extract hop information
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    line?.let { l ->
                        if (l.contains("From")) {
                            // Extract IP from "From x.x.x.x" format
                            val parts = l.split(" ")
                            parts.find { it.matches(Regex("\\d+\\.\\d+\\.\\d+\\.\\d+")) }?.let {
                                hopIp = it
                                try {
                                    hostname = InetAddress.getByName(it).hostName
                                } catch (e: Exception) {
                                    hostname = it
                                }
                            }
                        }
                    }
                }
                
                reader.close()
                errorReader.close()
                
                TracerouteHop(
                    hopNumber = ttl,
                    ipAddress = hopIp,
                    hostname = hostname,
                    responseTime = if (exitCode == 0) responseTime else -1.0,
                    success = exitCode == 0,
                    timestamp = System.currentTimeMillis()
                )
                
            } catch (e: Exception) {
                TracerouteHop(
                    hopNumber = ttl,
                    ipAddress = "*",
                    hostname = "*",
                    responseTime = -1.0,
                    success = false,
                    timestamp = System.currentTimeMillis(),
                    error = e.message
                )
            }
        }
    }
    
    /**
     * Perform DNS analysis
     */
    fun performDNSAnalysis(domain: String) {
        scope.launch {
            try {
                val startTime = System.currentTimeMillis()
                
                // Resolve A records
                val aRecords = mutableListOf<String>()
                try {
                    val addresses = InetAddress.getAllByName(domain)
                    addresses.forEach { addr ->
                        if (addr is Inet4Address) {
                            aRecords.add(addr.hostAddress ?: "")
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "Failed to resolve A records for $domain")
                }
                
                // Resolve AAAA records (IPv6)
                val aaaaRecords = mutableListOf<String>()
                try {
                    val addresses = InetAddress.getAllByName(domain)
                    addresses.forEach { addr ->
                        if (addr is Inet6Address) {
                            aaaaRecords.add(addr.hostAddress ?: "")
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "Failed to resolve AAAA records for $domain")
                }
                
                // Measure DNS resolution time
                val resolutionTime = System.currentTimeMillis() - startTime
                
                // Get DNS servers
                val dnsServers = getDNSServers()
                
                val result = DNSAnalysisResult(
                    domain = domain,
                    aRecords = aRecords,
                    aaaaRecords = aaaaRecords,
                    dnsServers = dnsServers,
                    resolutionTime = resolutionTime,
                    timestamp = System.currentTimeMillis()
                )
                
                _dnsResults.value = result
                
                analyticsManager.trackEvent(
                    eventName = "dns_analysis_completed",
                    properties = mapOf(
                        "domain" to domain,
                        "resolution_time" to resolutionTime,
                        "a_records_count" to aRecords.size,
                        "aaaa_records_count" to aaaaRecords.size
                    )
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "DNS analysis failed", e)
                analyticsManager.trackError("dns_analysis_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Get DNS servers
     */
    private fun getDNSServers(): List<String> {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val network = connectivityManager.activeNetwork
            val linkProperties = connectivityManager.getLinkProperties(network)
            
            linkProperties?.dnsServers?.map { it.hostAddress ?: "Unknown" } ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Calculate ping summary statistics
     */
    private fun calculatePingSummary(results: List<PingResult>): PingSummary {
        val successfulPings = results.filter { it.success && it.responseTime > 0 }
        val packetLoss = ((results.size - successfulPings.size).toFloat() / results.size) * 100
        
        val averageTime = if (successfulPings.isNotEmpty()) {
            successfulPings.map { it.responseTime }.average()
        } else {
            0.0
        }
        
        val minTime = successfulPings.minOfOrNull { it.responseTime } ?: 0.0
        val maxTime = successfulPings.maxOfOrNull { it.responseTime } ?: 0.0
        
        return PingSummary(
            totalPings = results.size,
            successfulPings = successfulPings.size,
            packetLoss = packetLoss,
            averageTime = averageTime,
            minTime = minTime,
            maxTime = maxTime
        )
    }
    
    /**
     * Stop current operation
     */
    fun stopCurrentOperation() {
        _isRunning.value = false
    }
}

/**
 * Ping result data class
 */
data class PingResult(
    val sequence: Int,
    val host: String,
    val ipAddress: String,
    val responseTime: Double, // milliseconds, -1 if failed
    val success: Boolean,
    val timestamp: Long,
    val error: String? = null
)

/**
 * Traceroute hop data class
 */
data class TracerouteHop(
    val hopNumber: Int,
    val ipAddress: String,
    val hostname: String,
    val responseTime: Double, // milliseconds, -1 if timeout
    val success: Boolean,
    val timestamp: Long,
    val error: String? = null
)

/**
 * DNS analysis result
 */
data class DNSAnalysisResult(
    val domain: String,
    val aRecords: List<String>,
    val aaaaRecords: List<String>,
    val dnsServers: List<String>,
    val resolutionTime: Long, // milliseconds
    val timestamp: Long
)

/**
 * Ping summary statistics
 */
data class PingSummary(
    val totalPings: Int,
    val successfulPings: Int,
    val packetLoss: Float, // percentage
    val averageTime: Double,
    val minTime: Double,
    val maxTime: Double
)
