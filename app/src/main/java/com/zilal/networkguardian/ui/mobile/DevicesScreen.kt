package com.zilal.networkguardian.ui.mobile

import android.content.Context
import android.util.Log
import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.database.AppDatabase
import com.zilal.networkguardian.database.TrustedDevice
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.network.ProductionNetworkScanner
import com.zilal.networkguardian.network.ScanState
import com.zilal.networkguardian.storage.DataStorageManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Comprehensive Devices Screen with Automatic Network Scanning
 * Features:
 * - Automatic background scanning on app start
 * - Manual "Scan Network" button for immediate discovery
 * - Expandable device list with detailed information
 * - Custom device naming with local and cloud storage
 * - Real-time device status updates
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DevicesScreen() {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // Initialize components
    val productionNetworkScanner = remember {
        ProductionNetworkScanner(context, SimpleAnalyticsManager(context))
    }
    val dataStorageManager = remember {
        DataStorageManager(context)
    }
    val database = remember {
        AppDatabase.getDatabase(context)
    }
    
    // State management
    val scanState by productionNetworkScanner.scanState.collectAsStateWithLifecycle()
    val discoveredDevices by productionNetworkScanner.discoveredDevices.collectAsStateWithLifecycle()
    val scanProgress by productionNetworkScanner.scanProgress.collectAsStateWithLifecycle()
    
    var expandedDeviceId by remember { mutableStateOf<String?>(null) }
    var editingDeviceId by remember { mutableStateOf<String?>(null) }
    var customNameInput by remember { mutableStateOf("") }
    var showDeviceDetails by remember { mutableStateOf<NetworkDevice?>(null) }
    
    // Automatic scanning on screen load
    LaunchedEffect(Unit) {
        Log.i("DevicesScreen", "Starting automatic network scanning...")
        delay(1000) // Brief delay to let UI settle
        productionNetworkScanner.startRealNetworkScan()
    }
    
    // Periodic background scanning every 2 minutes
    LaunchedEffect(Unit) {
        while (true) {
            delay(120000) // 2 minutes
            if (scanState !is ScanState.Scanning) {
                Log.i("DevicesScreen", "Starting periodic background scan...")
                productionNetworkScanner.startRealNetworkScan()
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header Section
        DevicesScreenHeader(
            deviceCount = discoveredDevices.size,
            scanState = scanState,
            scanProgress = scanProgress
        )
        
        // Scan Network Button
        ScanNetworkButton(
            scanState = scanState,
            onScanClick = {
                scope.launch {
                    Log.i("DevicesScreen", "Manual network scan triggered")
                    productionNetworkScanner.startRealNetworkScan()
                }
            }
        )
        
        // Devices List
        if (discoveredDevices.isNotEmpty()) {
            DevicesList(
                devices = discoveredDevices,
                expandedDeviceId = expandedDeviceId,
                editingDeviceId = editingDeviceId,
                customNameInput = customNameInput,
                onDeviceClick = { device ->
                    expandedDeviceId = if (expandedDeviceId == device.id) null else device.id
                },
                onEditNameClick = { device ->
                    editingDeviceId = device.id
                    customNameInput = device.customName ?: device.hostname ?: ""
                },
                onSaveCustomName = { device, newName ->
                    scope.launch {
                        saveDeviceCustomName(
                            device = device,
                            customName = newName,
                            scanner = productionNetworkScanner,
                            dataStorageManager = dataStorageManager,
                            database = database
                        )
                        editingDeviceId = null
                        customNameInput = ""
                    }
                },
                onCancelEdit = {
                    editingDeviceId = null
                    customNameInput = ""
                },
                onCustomNameChange = { newName ->
                    customNameInput = newName
                },
                onShowDetails = { device ->
                    showDeviceDetails = device
                }
            )
        } else if (scanState is ScanState.Completed) {
            // No devices found
            NoDevicesFound()
        }
    }
    
    // Device Details Dialog
    showDeviceDetails?.let { device ->
        DeviceDetailsDialog(
            device = device,
            onDismiss = { showDeviceDetails = null }
        )
    }
}

@Composable
private fun DevicesScreenHeader(
    deviceCount: Int,
    scanState: ScanState,
    scanProgress: Float
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Devices,
                    contentDescription = "Devices",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "Network Devices",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "Automatic discovery and management",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Status and Progress
            when (scanState) {
                is ScanState.Scanning -> {
                    LinearProgressIndicator(
                        progress = { scanProgress },
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Scanning network... ${(scanProgress * 100).toInt()}% complete",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                    )
                    Text(
                        text = "$deviceCount devices discovered so far",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.6f)
                    )
                }
                is ScanState.Completed -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Completed",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Scan completed - $deviceCount devices found",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
                is ScanState.Error -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Error",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Scan error: ${scanState.message}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
                else -> {
                    Text(
                        text = "Ready to scan network",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
private fun ScanNetworkButton(
    scanState: ScanState,
    onScanClick: () -> Unit
) {
    Button(
        onClick = onScanClick,
        modifier = Modifier.fillMaxWidth(),
        enabled = scanState !is ScanState.Scanning,
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.secondary
        )
    ) {
        Icon(
            imageVector = if (scanState is ScanState.Scanning) Icons.Default.Refresh else Icons.Default.Search,
            contentDescription = null
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = if (scanState is ScanState.Scanning) "Scanning..." else "Scan Network",
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun NoDevicesFound() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.DeviceHub,
                contentDescription = "No devices",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No devices found",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "Make sure you're connected to a WiFi network and try scanning again",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
        }
    }
}

@Composable
private fun DevicesList(
    devices: List<NetworkDevice>,
    expandedDeviceId: String?,
    editingDeviceId: String?,
    customNameInput: String,
    onDeviceClick: (NetworkDevice) -> Unit,
    onEditNameClick: (NetworkDevice) -> Unit,
    onSaveCustomName: (NetworkDevice, String) -> Unit,
    onCancelEdit: () -> Unit,
    onCustomNameChange: (String) -> Unit,
    onShowDetails: (NetworkDevice) -> Unit
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "Discovered Devices (${devices.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
        }

        items(devices) { device ->
            DeviceCard(
                device = device,
                isExpanded = expandedDeviceId == device.id,
                isEditing = editingDeviceId == device.id,
                customNameInput = customNameInput,
                onDeviceClick = { onDeviceClick(device) },
                onEditNameClick = { onEditNameClick(device) },
                onSaveCustomName = { newName -> onSaveCustomName(device, newName) },
                onCancelEdit = onCancelEdit,
                onCustomNameChange = onCustomNameChange,
                onShowDetails = { onShowDetails(device) }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DeviceCard(
    device: NetworkDevice,
    isExpanded: Boolean,
    isEditing: Boolean,
    customNameInput: String,
    onDeviceClick: () -> Unit,
    onEditNameClick: () -> Unit,
    onSaveCustomName: (String) -> Unit,
    onCancelEdit: () -> Unit,
    onCustomNameChange: (String) -> Unit,
    onShowDetails: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onDeviceClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Device Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (device.deviceType) {
                        NetworkDevice.DeviceType.ROUTER -> Icons.Default.Router
                        NetworkDevice.DeviceType.COMPUTER -> Icons.Default.Computer
                        NetworkDevice.DeviceType.MOBILE -> Icons.Default.PhoneAndroid
                        NetworkDevice.DeviceType.PRINTER -> Icons.Default.Print
                        NetworkDevice.DeviceType.SMART_TV -> Icons.Default.Tv
                        NetworkDevice.DeviceType.SERVER -> Icons.Default.Storage
                        NetworkDevice.DeviceType.CAMERA -> Icons.Default.Camera
                        else -> Icons.Default.DeviceHub
                    },
                    contentDescription = device.deviceType?.name ?: "Unknown",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column(modifier = Modifier.weight(1f)) {
                    if (isEditing) {
                        // Custom name editing
                        OutlinedTextField(
                            value = customNameInput,
                            onValueChange = onCustomNameChange,
                            label = { Text("Custom Name") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Row {
                            TextButton(onClick = { onSaveCustomName(customNameInput) }) {
                                Text("Save")
                            }
                            TextButton(onClick = onCancelEdit) {
                                Text("Cancel")
                            }
                        }
                    } else {
                        // Device name display
                        Text(
                            text = device.customName ?: device.hostname ?: "Unknown Device",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = device.ipAddress,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        if (device.manufacturer != null) {
                            Text(
                                text = device.manufacturer!!,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                            )
                        }
                    }
                }

                // Security risk indicator
                Icon(
                    imageVector = when (device.securityRisk) {
                        NetworkDevice.SecurityRisk.HIGH, NetworkDevice.SecurityRisk.CRITICAL -> Icons.Default.Error
                        NetworkDevice.SecurityRisk.MEDIUM -> Icons.Default.Warning
                        else -> Icons.Default.CheckCircle
                    },
                    contentDescription = device.securityRisk?.name ?: "Unknown",
                    tint = when (device.securityRisk) {
                        NetworkDevice.SecurityRisk.HIGH, NetworkDevice.SecurityRisk.CRITICAL -> MaterialTheme.colorScheme.error
                        NetworkDevice.SecurityRisk.MEDIUM -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.primary
                    },
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Icon(
                    imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "Collapse" else "Expand",
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            // Expanded content
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically() + fadeIn(),
                exit = shrinkVertically() + fadeOut()
            ) {
                Column(
                    modifier = Modifier.padding(top = 16.dp)
                ) {
                    Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                    Spacer(modifier = Modifier.height(12.dp))

                    // Device details summary
                    DeviceDetailsSummary(device = device)

                    Spacer(modifier = Modifier.height(12.dp))

                    // Action buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = onEditNameClick,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Edit Name")
                        }

                        Button(
                            onClick = onShowDetails,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Details")
                        }
                    }
                }
            }
        }
    }
}

/**
 * Save custom device name to both local and cloud storage
 */
private suspend fun saveDeviceCustomName(
    device: NetworkDevice,
    customName: String,
    scanner: ProductionNetworkScanner,
    dataStorageManager: DataStorageManager,
    database: AppDatabase
) {
    try {
        // Save to scanner (immediate UI update)
        scanner.saveDeviceName(device.ipAddress, customName)

        // Save to local database
        device.macAddress?.let { macAddress ->
            val trustedDevice = TrustedDevice(
                macAddress = macAddress,
                ipAddress = device.ipAddress,
                hostname = device.hostname,
                manufacturer = device.manufacturer,
                deviceType = device.deviceType?.name,
                firstSeen = device.lastSeen,
                lastSeen = System.currentTimeMillis(),
                nickname = customName
            )
            database.trustedDeviceDao().insert(trustedDevice)
        }

        // Save to cloud storage (Supabase)
        val updatedDevices = listOf(device.copy(customName = customName))
        dataStorageManager.saveNetworkDevices(updatedDevices)

        Log.i("DevicesScreen", "Custom name '$customName' saved for device ${device.ipAddress}")
    } catch (e: Exception) {
        Log.e("DevicesScreen", "Failed to save custom device name", e)
    }
}

@Composable
private fun DeviceDetailsSummary(device: NetworkDevice) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Basic info
        if (device.macAddress != null) {
            DetailRow(label = "MAC Address", value = device.macAddress!!)
        }
        if (device.deviceType != null) {
            DetailRow(label = "Device Type", value = device.deviceType!!.name)
        }
        if (device.operatingSystem != null) {
            DetailRow(label = "Operating System", value = device.operatingSystem!!)
        }

        // Network info
        if (device.openPorts.isNotEmpty()) {
            DetailRow(
                label = "Open Ports",
                value = "${device.openPorts.size} ports (${device.openPorts.take(3).joinToString(", ")}${if (device.openPorts.size > 3) "..." else ""})"
            )
        }

        if (device.services.isNotEmpty()) {
            DetailRow(label = "Services", value = "${device.services.size} running")
        }

        // Security info
        DetailRow(
            label = "Security Risk",
            value = device.securityRisk?.name ?: "Unknown",
            valueColor = when (device.securityRisk) {
                NetworkDevice.SecurityRisk.HIGH, NetworkDevice.SecurityRisk.CRITICAL -> MaterialTheme.colorScheme.error
                NetworkDevice.SecurityRisk.MEDIUM -> MaterialTheme.colorScheme.secondary
                else -> MaterialTheme.colorScheme.primary
            }
        )

        // Last seen
        val lastSeenText = if (device.lastSeen > 0) {
            val timeDiff = System.currentTimeMillis() - device.lastSeen
            when {
                timeDiff < 60000 -> "Just now"
                timeDiff < 3600000 -> "${timeDiff / 60000} minutes ago"
                timeDiff < 86400000 -> "${timeDiff / 3600000} hours ago"
                else -> "${timeDiff / 86400000} days ago"
            }
        } else {
            "Unknown"
        }
        DetailRow(label = "Last Seen", value = lastSeenText)
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String,
    valueColor: Color = MaterialTheme.colorScheme.onSurface
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = valueColor,
            modifier = Modifier.weight(1f),
            textAlign = androidx.compose.ui.text.style.TextAlign.End
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DeviceDetailsDialog(
    device: NetworkDevice,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (device.deviceType) {
                        NetworkDevice.DeviceType.ROUTER -> Icons.Default.Router
                        NetworkDevice.DeviceType.COMPUTER -> Icons.Default.Computer
                        NetworkDevice.DeviceType.MOBILE -> Icons.Default.PhoneAndroid
                        NetworkDevice.DeviceType.PRINTER -> Icons.Default.Print
                        NetworkDevice.DeviceType.SMART_TV -> Icons.Default.Tv
                        NetworkDevice.DeviceType.SERVER -> Icons.Default.Storage
                        NetworkDevice.DeviceType.CAMERA -> Icons.Default.Camera
                        else -> Icons.Default.DeviceHub
                    },
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = device.customName ?: device.hostname ?: "Unknown Device",
                    style = MaterialTheme.typography.titleLarge
                )
            }
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    DeviceDetailsSection(
                        title = "Network Information",
                        details = buildList {
                            add("IP Address" to device.ipAddress)
                            device.macAddress?.let { add("MAC Address" to it) }
                            device.hostname?.let { add("Hostname" to it) }
                        }
                    )
                }

                item {
                    DeviceDetailsSection(
                        title = "Device Information",
                        details = buildList {
                            device.deviceType?.let { add("Type" to it.name) }
                            device.manufacturer?.let { add("Manufacturer" to it) }
                            device.operatingSystem?.let { add("OS" to it) }
                        }
                    )
                }

                if (device.openPorts.isNotEmpty()) {
                    item {
                        DeviceDetailsSection(
                            title = "Open Ports (${device.openPorts.size})",
                            details = device.openPorts.chunked(5).map { chunk ->
                                "Ports" to chunk.joinToString(", ")
                            }
                        )
                    }
                }

                if (device.services.isNotEmpty()) {
                    item {
                        DeviceDetailsSection(
                            title = "Running Services (${device.services.size})",
                            details = device.services.map { (port, service) ->
                                "Port $port" to service
                            }
                        )
                    }
                }

                item {
                    DeviceDetailsSection(
                        title = "Security Assessment",
                        details = buildList {
                            add("Risk Level" to (device.securityRisk?.name ?: "Unknown"))
                            if (device.vulnerabilities.isNotEmpty()) {
                                add("Vulnerabilities" to "${device.vulnerabilities.size} found")
                            }
                            add("Status" to if (device.isOnline) "Online" else "Offline")
                        }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

@Composable
private fun DeviceDetailsSection(
    title: String,
    details: List<Pair<String, String>>
) {
    if (details.isNotEmpty()) {
        Column {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            details.forEach { (label, value) ->
                DetailRow(label = label, value = value)
            }
        }
    }
}
