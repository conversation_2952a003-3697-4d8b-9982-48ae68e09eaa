package com.zilal.networkguardian.ui.enhanced

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.viewmodel.NetworkScanViewModel
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.ScanResult

/**
 * Production-ready network scanner that provides comprehensive scanning capabilities
 * Matches the web application's RealNetworkScanner functionality
 */
@Composable
fun ProductionNetworkScanner() {
    val networkScanViewModel: NetworkScanViewModel = viewModel()
    val isScanning by networkScanViewModel.isScanning.collectAsState()
    val scanResults by networkScanViewModel.scanResults.collectAsState()
    val recentScans by networkScanViewModel.recentScans.collectAsState()

    var selectedScanType by remember { mutableStateOf("discovery") }
    var targetRange by remember { mutableStateOf("***********/24") }
    var showAdvancedOptions by remember { mutableStateOf(false) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Scanner Header
        item {
            ScannerHeader()
        }

        // Quick Scan Actions
        item {
            QuickScanSection(
                selectedScanType = selectedScanType,
                onScanTypeSelected = { scanType ->
                    selectedScanType = scanType
                    when (scanType) {
                        "discovery" -> networkScanViewModel.startDeviceDiscovery()
                        "vulnerability" -> networkScanViewModel.startVulnerabilityScan()
                        "port" -> networkScanViewModel.startPortScan()
                        "security" -> networkScanViewModel.startSecurityAudit()
                    }
                },
                isScanning = isScanning
            )
        }

        // Advanced Scanner Controls
        item {
            AdvancedScannerControls(
                scanType = selectedScanType,
                targetRange = targetRange,
                onTargetRangeChange = { targetRange = it },
                showAdvancedOptions = showAdvancedOptions,
                onToggleAdvancedOptions = { showAdvancedOptions = !showAdvancedOptions },
                onStartScan = {
                    when (selectedScanType) {
                        "discovery" -> networkScanViewModel.startDeviceDiscovery()
                        "vulnerability" -> networkScanViewModel.startVulnerabilityScan()
                        "port" -> networkScanViewModel.startPortScan()
                        "security" -> networkScanViewModel.startSecurityAudit()
                    }
                },
                isScanning = isScanning
            )
        }

        // Real-time Scan Progress
        if (isScanning) {
            item {
                ScanProgressCard(scanType = selectedScanType)
            }
        }

        // Current Scan Results
        if (scanResults != null) {
            item {
                CurrentScanResultsCard(scanResults = scanResults!!)
            }
        }

        // Threat Intelligence Tools
        item {
            ThreatIntelligenceTools()
        }

        // Recent Scans History
        if (recentScans.isNotEmpty()) {
            item {
                RecentScansHistory(recentScans = recentScans)
            }
        }
    }
}

@Composable
fun ScannerHeader() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1E293B)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Card(
                modifier = Modifier.size(60.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF3B82F6)
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Radar,
                        contentDescription = "Scanner",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column {
                Text(
                    text = "Network Scanner",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = "Advanced network discovery and security analysis",
                    fontSize = 14.sp,
                    color = Color(0xFF94A3B8)
                )
                Text(
                    text = "Real-time scanning • CVE detection • Port analysis",
                    fontSize = 12.sp,
                    color = Color(0xFF64748B)
                )
            }
        }
    }
}

@Composable
fun QuickScanSection(
    selectedScanType: String,
    onScanTypeSelected: (String) -> Unit,
    isScanning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Quick Scan Actions",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    QuickScanCard(
                        icon = Icons.Default.Search,
                        title = "Device Discovery",
                        description = "Find all network devices",
                        scanType = "discovery",
                        isActive = isScanning && selectedScanType == "discovery",
                        isSelected = selectedScanType == "discovery",
                        onClick = { onScanTypeSelected("discovery") },
                        color = Color(0xFF3B82F6)
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Security,
                        title = "Vulnerability Scan",
                        description = "Check for CVE vulnerabilities",
                        scanType = "vulnerability",
                        isActive = isScanning && selectedScanType == "vulnerability",
                        isSelected = selectedScanType == "vulnerability",
                        onClick = { onScanTypeSelected("vulnerability") },
                        color = Color(0xFFEF4444)
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Cable,
                        title = "Port Scan",
                        description = "Discover open ports",
                        scanType = "port",
                        isActive = isScanning && selectedScanType == "port",
                        isSelected = selectedScanType == "port",
                        onClick = { onScanTypeSelected("port") },
                        color = Color(0xFF10B981)
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Lock,
                        title = "Security Audit",
                        description = "Comprehensive security check",
                        scanType = "security",
                        isActive = isScanning && selectedScanType == "security",
                        isSelected = selectedScanType == "security",
                        onClick = { onScanTypeSelected("security") },
                        color = Color(0xFF8B5CF6)
                    )
                }
            }
        }
    }
}

@Composable
fun QuickScanCard(
    icon: ImageVector,
    title: String,
    description: String,
    scanType: String,
    isActive: Boolean,
    isSelected: Boolean,
    onClick: () -> Unit,
    color: Color
) {
    Card(
        modifier = Modifier
            .width(160.dp)
            .height(120.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isActive -> color.copy(alpha = 0.2f)
                isSelected -> color.copy(alpha = 0.1f)
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(32.dp),
                tint = if (isActive || isSelected) color else MaterialTheme.colorScheme.onSurfaceVariant
            )

            Column {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                if (isActive) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(12.dp),
                            strokeWidth = 2.dp,
                            color = color
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "Scanning...",
                            fontSize = 10.sp,
                            color = color
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedScannerControls(
    scanType: String,
    targetRange: String,
    onTargetRangeChange: (String) -> Unit,
    showAdvancedOptions: Boolean,
    onToggleAdvancedOptions: () -> Unit,
    onStartScan: () -> Unit,
    isScanning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Advanced Scanner Controls",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                TextButton(onClick = onToggleAdvancedOptions) {
                    Text(if (showAdvancedOptions) "Hide Options" else "Show Options")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Target Range Input
            OutlinedTextField(
                value = targetRange,
                onValueChange = onTargetRangeChange,
                label = { Text("Target Range") },
                placeholder = { Text("***********/24") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Router,
                        contentDescription = "Network"
                    )
                }
            )

            if (showAdvancedOptions) {
                Spacer(modifier = Modifier.height(16.dp))

                // Advanced Options
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Advanced Options",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        // Scan Speed
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Scan Speed:")
                            Row {
                                FilterChip(
                                    onClick = { },
                                    label = { Text("Fast") },
                                    selected = true
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                FilterChip(
                                    onClick = { },
                                    label = { Text("Thorough") },
                                    selected = false
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // Port Range
                        OutlinedTextField(
                            value = "1-1000",
                            onValueChange = { },
                            label = { Text("Port Range") },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Start Scan Button
            Button(
                onClick = onStartScan,
                enabled = !isScanning,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF3B82F6)
                )
            ) {
                if (isScanning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Scanning...")
                } else {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "Start scan",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Start ${scanType.replaceFirstChar { it.uppercase() }} Scan")
                }
            }
        }
    }
}

@Composable
fun ScanProgressCard(scanType: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF3B82F6).copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = Color(0xFF3B82F6)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "Scanning in Progress",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF3B82F6)
                    )
                    Text(
                        text = "Running ${scanType.replaceFirstChar { it.uppercase() }} scan...",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Progress indicators
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ProgressIndicator(
                    label = "Discovery",
                    progress = 0.8f,
                    isActive = true
                )
                ProgressIndicator(
                    label = "Analysis",
                    progress = 0.3f,
                    isActive = true
                )
                ProgressIndicator(
                    label = "Reporting",
                    progress = 0.0f,
                    isActive = false
                )
            }
        }
    }
}

@Composable
fun ProgressIndicator(
    label: String,
    progress: Float,
    isActive: Boolean
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator(
            progress = progress,
            modifier = Modifier.size(40.dp),
            color = if (isActive) Color(0xFF3B82F6) else Color(0xFF94A3B8),
            trackColor = Color(0xFF94A3B8).copy(alpha = 0.3f)
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = label,
            fontSize = 12.sp,
            color = if (isActive) Color(0xFF3B82F6) else Color(0xFF94A3B8)
        )
        Text(
            text = "${(progress * 100).toInt()}%",
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

@Composable
fun CurrentScanResultsCard(scanResults: ScanResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Scan Results",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Results Summary
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ResultMetric(
                    icon = Icons.Default.Devices,
                    label = "Devices Found",
                    value = "${scanResults.devices.size}",
                    color = Color(0xFF10B981)
                )
                ResultMetric(
                    icon = Icons.Default.Security,
                    label = "Vulnerabilities",
                    value = "${scanResults.vulnerabilities.size}",
                    color = if (scanResults.vulnerabilities.isNotEmpty()) Color(0xFFEF4444) else Color(0xFF10B981)
                )
                ResultMetric(
                    icon = Icons.Default.Speed,
                    label = "Scan Time",
                    value = "${((scanResults.endTime ?: System.currentTimeMillis()) - scanResults.startTime) / 1000}s",
                    color = Color(0xFF3B82F6)
                )
            }

            if (scanResults.devices.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Discovered Devices",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(8.dp))

                scanResults.devices.take(3).forEach { device ->
                    DeviceResultItem(device = device)
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (scanResults.devices.size > 3) {
                    TextButton(
                        onClick = { /* TODO: Show all devices */ }
                    ) {
                        Text("View all ${scanResults.devices.size} devices")
                    }
                }
            }
        }
    }
}

@Composable
fun ResultMetric(
    icon: ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(24.dp),
            tint = color
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun DeviceResultItem(device: NetworkDevice) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (device.deviceType) {
                    NetworkDevice.DeviceType.ROUTER -> Icons.Default.Router
                    NetworkDevice.DeviceType.COMPUTER -> Icons.Default.Computer
                    NetworkDevice.DeviceType.MOBILE -> Icons.Default.PhoneAndroid
                    else -> Icons.Default.DeviceUnknown
                },
                contentDescription = "Device type",
                modifier = Modifier.size(24.dp),
                tint = if (device.isOnline) Color(0xFF10B981) else Color(0xFF6B7280)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.hostname ?: device.ipAddress,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "${device.ipAddress} • ${device.macAddress ?: "Unknown MAC"}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }

            Card(
                colors = CardDefaults.cardColors(
                    containerColor = if (device.isOnline) Color(0xFF10B981) else Color(0xFF6B7280)
                )
            ) {
                Text(
                    text = if (device.isOnline) "Online" else "Offline",
                    fontSize = 10.sp,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
fun ThreatIntelligenceTools() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Threat Intelligence & Analysis",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    ThreatCheckTool(
                        title = "IP Reputation",
                        placeholder = "*************",
                        icon = Icons.Default.Computer,
                        color = Color(0xFF3B82F6)
                    )
                }
                item {
                    ThreatCheckTool(
                        title = "Domain Check",
                        placeholder = "example.com",
                        icon = Icons.Default.Language,
                        color = Color(0xFF10B981)
                    )
                }
                item {
                    ThreatCheckTool(
                        title = "CVE Lookup",
                        placeholder = "CVE-2024-0001",
                        icon = Icons.Default.BugReport,
                        color = Color(0xFFEF4444)
                    )
                }
            }
        }
    }
}

@Composable
fun ThreatCheckTool(
    title: String,
    placeholder: String,
    icon: ImageVector,
    color: Color
) {
    var inputValue by remember { mutableStateOf("") }
    var isChecking by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.width(200.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(20.dp),
                    tint = color
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = color
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            OutlinedTextField(
                value = inputValue,
                onValueChange = { inputValue = it },
                placeholder = { Text(placeholder, fontSize = 12.sp) },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(8.dp))

            Button(
                onClick = {
                    isChecking = true
                    // TODO: Implement threat intelligence check
                },
                enabled = inputValue.isNotBlank() && !isChecking,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = color
                )
            ) {
                if (isChecking) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(14.dp),
                        strokeWidth = 2.dp,
                        color = Color.White
                    )
                } else {
                    Text("Check", fontSize = 12.sp)
                }
            }
        }
    }
}

@Composable
fun RecentScansHistory(recentScans: List<ScanResult>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Recent Scans",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                TextButton(onClick = { /* TODO: View all scans */ }) {
                    Text("View All")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            recentScans.take(3).forEach { scan ->
                RecentScanItem(scan = scan)
                if (scan != recentScans.take(3).last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

@Composable
fun RecentScanItem(scan: ScanResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (scan.scanType) {
                        ScanResult.ScanType.INTERNAL_NETWORK -> Icons.Default.Search
                        ScanResult.ScanType.VULNERABILITY_SCAN -> Icons.Default.Security
                        ScanResult.ScanType.PORT_SCAN -> Icons.Default.Cable
                        else -> Icons.Default.Radar
                    },
                    contentDescription = "Scan type",
                    modifier = Modifier.size(20.dp),
                    tint = when (scan.scanStatus) {
                        ScanResult.ScanStatus.COMPLETED -> Color(0xFF10B981)
                        ScanResult.ScanStatus.IN_PROGRESS -> Color(0xFF3B82F6)
                        ScanResult.ScanStatus.FAILED -> Color(0xFFEF4444)
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column {
                    Text(
                        text = "${scan.scanType.name.lowercase().replaceFirstChar { it.uppercase() }} Scan",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "Started: ${java.text.SimpleDateFormat("MMM dd, HH:mm").format(java.util.Date(scan.startTime))}",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = when (scan.scanStatus) {
                        ScanResult.ScanStatus.COMPLETED -> "Completed"
                        ScanResult.ScanStatus.IN_PROGRESS -> "Running"
                        ScanResult.ScanStatus.FAILED -> "Failed"
                        else -> "Unknown"
                    },
                    fontSize = 12.sp,
                    color = when (scan.scanStatus) {
                        ScanResult.ScanStatus.COMPLETED -> Color(0xFF10B981)
                        ScanResult.ScanStatus.IN_PROGRESS -> Color(0xFF3B82F6)
                        ScanResult.ScanStatus.FAILED -> Color(0xFFEF4444)
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                if (scan.scanStatus == ScanResult.ScanStatus.COMPLETED) {
                    Text(
                        text = "${scan.devices.size} devices, ${scan.vulnerabilities.size} issues",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}