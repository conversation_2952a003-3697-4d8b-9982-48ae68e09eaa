package com.zilal.networkguardian.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Enterprise-grade professional color scheme for Network Guardian
 * Sophisticated, clean aesthetic matching advanced network management tools
 */

// Professional Primary Colors
private val ProfessionalBlue = Color(0xFF1565C0)
private val ProfessionalBlueVariant = Color(0xFF0D47A1)
private val ProfessionalLightBlue = Color(0xFF42A5F5)

// Professional Secondary Colors
private val ProfessionalTeal = Color(0xFF00695C)
private val ProfessionalTealVariant = Color(0xFF004D40)
private val ProfessionalLightTeal = Color(0xFF26A69A)

// Professional Neutral Colors
private val ProfessionalGray = Color(0xFF37474F)
private val ProfessionalLightGray = Color(0xFF78909C)
private val ProfessionalDarkGray = Color(0xFF263238)

// Professional Surface Colors
private val ProfessionalSurface = Color(0xFFF8F9FA)
private val ProfessionalSurfaceVariant = Color(0xFFE8EAF6)
private val ProfessionalBackground = Color(0xFFFFFFFF)

// Professional Dark Theme Colors
private val ProfessionalDarkSurface = Color(0xFF1A1A1A)
private val ProfessionalDarkSurfaceVariant = Color(0xFF2D2D2D)
private val ProfessionalDarkBackground = Color(0xFF121212)

// Status Colors
private val SuccessGreen = Color(0xFF2E7D32)
private val WarningOrange = Color(0xFFEF6C00)
private val ErrorRed = Color(0xFFD32F2F)
private val InfoBlue = Color(0xFF1976D2)

/**
 * Professional Light Color Scheme
 */
private val ProfessionalLightColorScheme = lightColorScheme(
    primary = ProfessionalBlue,
    onPrimary = Color.White,
    primaryContainer = ProfessionalSurfaceVariant,
    onPrimaryContainer = ProfessionalDarkGray,
    
    secondary = ProfessionalTeal,
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFE0F2F1),
    onSecondaryContainer = ProfessionalTealVariant,
    
    tertiary = ProfessionalLightBlue,
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFE3F2FD),
    onTertiaryContainer = ProfessionalBlueVariant,
    
    error = ErrorRed,
    onError = Color.White,
    errorContainer = Color(0xFFFFEBEE),
    onErrorContainer = ErrorRed,
    
    background = ProfessionalBackground,
    onBackground = ProfessionalDarkGray,
    
    surface = ProfessionalSurface,
    onSurface = ProfessionalDarkGray,
    surfaceVariant = ProfessionalSurfaceVariant,
    onSurfaceVariant = ProfessionalGray,
    
    outline = ProfessionalLightGray,
    outlineVariant = Color(0xFFCFD8DC),
    
    scrim = Color.Black.copy(alpha = 0.32f),
    inverseSurface = ProfessionalDarkGray,
    inverseOnSurface = Color.White,
    inversePrimary = ProfessionalLightBlue
)

/**
 * Professional Dark Color Scheme
 */
private val ProfessionalDarkColorScheme = darkColorScheme(
    primary = ProfessionalLightBlue,
    onPrimary = ProfessionalDarkGray,
    primaryContainer = ProfessionalBlueVariant,
    onPrimaryContainer = Color.White,
    
    secondary = ProfessionalLightTeal,
    onSecondary = ProfessionalDarkGray,
    secondaryContainer = ProfessionalTealVariant,
    onSecondaryContainer = Color.White,
    
    tertiary = Color(0xFF90CAF9),
    onTertiary = ProfessionalDarkGray,
    tertiaryContainer = ProfessionalBlueVariant,
    onTertiaryContainer = Color.White,
    
    error = Color(0xFFEF5350),
    onError = Color.White,
    errorContainer = Color(0xFFB71C1C),
    onErrorContainer = Color.White,
    
    background = ProfessionalDarkBackground,
    onBackground = Color.White,
    
    surface = ProfessionalDarkSurface,
    onSurface = Color.White,
    surfaceVariant = ProfessionalDarkSurfaceVariant,
    onSurfaceVariant = Color(0xFFB0BEC5),
    
    outline = Color(0xFF607D8B),
    outlineVariant = Color(0xFF455A64),
    
    scrim = Color.Black.copy(alpha = 0.32f),
    inverseSurface = ProfessionalSurface,
    inverseOnSurface = ProfessionalDarkGray,
    inversePrimary = ProfessionalBlue
)

/**
 * Professional Enterprise Theme
 */
@Composable
fun EnterpriseNetworkGuardianTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> ProfessionalDarkColorScheme
        else -> ProfessionalLightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = ProfessionalTypography,
        shapes = ProfessionalShapes,
        content = content
    )
}

/**
 * Professional Typography System
 */
val ProfessionalTypography = Typography(
    displayLarge = Typography().displayLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Light,
        letterSpacing = (-0.25).sp
    ),
    displayMedium = Typography().displayMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Light,
        letterSpacing = 0.sp
    ),
    displaySmall = Typography().displaySmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
        letterSpacing = 0.sp
    ),
    headlineLarge = Typography().headlineLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.SemiBold,
        letterSpacing = 0.sp
    ),
    headlineMedium = Typography().headlineMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.SemiBold,
        letterSpacing = 0.sp
    ),
    headlineSmall = Typography().headlineSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.SemiBold,
        letterSpacing = 0.sp
    ),
    titleLarge = Typography().titleLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.SemiBold,
        letterSpacing = 0.sp
    ),
    titleMedium = Typography().titleMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
        letterSpacing = 0.15.sp
    ),
    titleSmall = Typography().titleSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
        letterSpacing = 0.1.sp
    ),
    bodyLarge = Typography().bodyLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
        letterSpacing = 0.5.sp
    ),
    bodyMedium = Typography().bodyMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
        letterSpacing = 0.25.sp
    ),
    bodySmall = Typography().bodySmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
        letterSpacing = 0.4.sp
    ),
    labelLarge = Typography().labelLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
        letterSpacing = 0.1.sp
    ),
    labelMedium = Typography().labelMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
        letterSpacing = 0.5.sp
    ),
    labelSmall = Typography().labelSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default,
        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
        letterSpacing = 0.5.sp
    )
)

/**
 * Professional Shape System
 */
val ProfessionalShapes = Shapes(
    extraSmall = androidx.compose.foundation.shape.RoundedCornerShape(4.dp),
    small = androidx.compose.foundation.shape.RoundedCornerShape(8.dp),
    medium = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
    large = androidx.compose.foundation.shape.RoundedCornerShape(16.dp),
    extraLarge = androidx.compose.foundation.shape.RoundedCornerShape(24.dp)
)

/**
 * Professional Status Colors
 */
object ProfessionalColors {
    val Success = SuccessGreen
    val Warning = WarningOrange
    val Error = ErrorRed
    val Info = InfoBlue
    
    val NetworkSecure = SuccessGreen
    val NetworkWarning = WarningOrange
    val NetworkVulnerable = ErrorRed
    val NetworkUnknown = ProfessionalLightGray
    
    val HighPriority = ErrorRed
    val MediumPriority = WarningOrange
    val LowPriority = InfoBlue
    val NoPriority = ProfessionalLightGray
}
