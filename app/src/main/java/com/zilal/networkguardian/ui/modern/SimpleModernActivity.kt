package com.zilal.networkguardian.ui.modern

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.auth.repository.AuthState
import com.zilal.networkguardian.auth.ui.AuthActivity
import com.zilal.networkguardian.auth.viewmodel.AuthViewModel
import com.zilal.networkguardian.ui.theme.ZilalTheme
import com.zilal.networkguardian.viewmodel.NetworkScanViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Simple modern activity with clean, production-ready UI and authentication
 */
@AndroidEntryPoint
class SimpleModernActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ZilalTheme {
                AuthenticatedApp()
            }
        }
    }

    @Composable
    private fun AuthenticatedApp() {
        val authViewModel: AuthViewModel = viewModel()
        val authState by authViewModel.authState.collectAsState()

        // Check authentication state
        LaunchedEffect(authState) {
            when (authState) {
                is AuthState.Unauthenticated -> {
                    // Redirect to auth activity
                    navigateToAuth()
                }
                is AuthState.Loading -> {
                    // Show loading
                }
                is AuthState.Authenticated -> {
                    // Show main app
                }
                is AuthState.Error -> {
                    // Handle error - might need to redirect to auth
                    navigateToAuth()
                }
            }
        }

        when (authState) {
            is AuthState.Authenticated -> {
                SimpleModernApp(authViewModel = authViewModel)
            }
            is AuthState.Loading -> {
                LoadingScreen()
            }
            else -> {
                // This shouldn't happen as we redirect to auth, but just in case
                LoadingScreen()
            }
        }
    }

    private fun navigateToAuth() {
        val intent = Intent(this, AuthActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleModernApp(authViewModel: AuthViewModel) {
    var currentView by remember { mutableStateOf("dashboard") }
    var showDrawer by remember { mutableStateOf(false) }
    
    val drawerState = rememberDrawerState(DrawerValue.Closed)
    
    // Launch effect to control drawer
    LaunchedEffect(showDrawer) {
        if (showDrawer) {
            drawerState.open()
        } else {
            drawerState.close()
        }
    }
    
    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModernDrawerContent(
                currentView = currentView,
                onViewChange = { view ->
                    currentView = view
                    showDrawer = false
                },
                authViewModel = authViewModel
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFF8FAFC),
                            Color(0xFFF1F5F9),
                            Color(0xFFE2E8F0)
                        )
                    )
                )
        ) {
            // Modern Top Bar
            ModernTopBar(
                currentView = currentView,
                onMenuClick = { showDrawer = true },
                authViewModel = authViewModel
            )
            
            // Main Content
            when (currentView) {
                "dashboard" -> FullModernDashboard()
                "scanner" -> FullNetworkScanner()
                "devices" -> SimpleDeviceManager()
                "security" -> SimpleSecurityDashboard()
                "settings" -> SimpleSettings()
                else -> FullModernDashboard()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModernTopBar(
    currentView: String,
    onMenuClick: () -> Unit,
    authViewModel: AuthViewModel
) {
    val currentUser by authViewModel.currentUser.collectAsState()
    TopAppBar(
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Modern Logo
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(
                            Brush.linearGradient(
                                colors = listOf(
                                    Color(0xFF3B82F6),
                                    Color(0xFF8B5CF6)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "🛡️",
                        fontSize = 20.sp
                    )
                }
                
                Column {
                    Text(
                        text = "SecureNet Pro",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1E293B)
                    )
                    Text(
                        text = "Welcome, ${currentUser?.firstName ?: currentUser?.username ?: "User"}",
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
        },
        navigationIcon = {
            IconButton(onClick = onMenuClick) {
                Icon(
                    imageVector = Icons.Default.Menu,
                    contentDescription = "Menu"
                )
            }
        },
        actions = {
            IconButton(onClick = { /* TODO: Notifications */ }) {
                Badge {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = "Notifications"
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.White.copy(alpha = 0.9f)
        )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModernDrawerContent(
    currentView: String,
    onViewChange: (String) -> Unit,
    authViewModel: AuthViewModel
) {
    val currentUser by authViewModel.currentUser.collectAsState()
    ModalDrawerSheet(
        modifier = Modifier.width(300.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF1E293B)
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .clip(RoundedCornerShape(20.dp))
                            .background(
                                Brush.linearGradient(
                                    colors = listOf(
                                        Color(0xFF3B82F6),
                                        Color(0xFF8B5CF6)
                                    )
                                )
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Profile",
                            tint = Color.White,
                            modifier = Modifier.size(30.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = authViewModel.getUserDisplayName(),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        text = currentUser?.email ?: "<EMAIL>",
                        fontSize = 12.sp,
                        color = Color(0xFF94A3B8)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Navigation Items
            val menuItems = listOf(
                "dashboard" to Icons.Default.Dashboard,
                "scanner" to Icons.Default.Search,
                "devices" to Icons.Default.Devices,
                "security" to Icons.Default.Security,
                "settings" to Icons.Default.Settings
            )
            
            menuItems.forEach { (route, icon) ->
                NavigationDrawerItem(
                    icon = { Icon(icon, contentDescription = route) },
                    label = { Text(route.replaceFirstChar { it.uppercase() }) },
                    selected = currentView == route,
                    onClick = { onViewChange(route) },
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Logout Button
            NavigationDrawerItem(
                icon = { Icon(Icons.Default.Logout, contentDescription = "Logout") },
                label = { Text("Logout") },
                selected = false,
                onClick = { authViewModel.logout() },
                modifier = Modifier.padding(vertical = 4.dp),
                colors = NavigationDrawerItemDefaults.colors(
                    unselectedContainerColor = Color(0xFFEF4444).copy(alpha = 0.1f),
                    unselectedIconColor = Color(0xFFEF4444),
                    unselectedTextColor = Color(0xFFEF4444)
                )
            )
        }
    }
}

@Composable
fun SimpleNetworkScanner() {
    val networkScanViewModel: NetworkScanViewModel = viewModel()
    val isScanning by networkScanViewModel.isScanning.collectAsState()
    val scanResults by networkScanViewModel.scanResults.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Network Scanner",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = { networkScanViewModel.startQuickScan() },
                        enabled = !isScanning,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF3B82F6)
                        )
                    ) {
                        if (isScanning) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Scanning...")
                        } else {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = "Scan",
                                modifier = Modifier.size(18.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Start Network Scan")
                        }
                    }
                    
                    if (scanResults != null) {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Found ${scanResults!!.devices.size} devices",
                            fontSize = 16.sp,
                            color = Color(0xFF10B981)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SimpleDeviceManager() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Device Manager",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Manage connected devices on your network",
                        fontSize = 14.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
        }
    }
}

@Composable
fun SimpleSecurityDashboard() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Security Dashboard",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Monitor security threats and vulnerabilities",
                        fontSize = 14.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
        }
    }
}

@Composable
fun SimpleSettings() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Settings",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Configure app preferences and security settings",
                        fontSize = 14.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1E293B),
                        Color(0xFF334155),
                        Color(0xFF475569)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // Logo
            Card(
                modifier = Modifier.size(100.dp),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White.copy(alpha = 0.1f)
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "🛡️",
                        fontSize = 48.sp
                    )
                }
            }

            // Loading indicator
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                strokeWidth = 4.dp,
                color = Color.White
            )

            Text(
                text = "SecureNet Pro",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )

            Text(
                text = "Loading your secure network dashboard...",
                fontSize = 14.sp,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
    }
}
