package com.zilal.networkguardian.ui.enhanced

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.viewmodel.NetworkScanViewModel
import com.zilal.networkguardian.model.ScanResult

@Composable
fun EnhancedNetworkScanner() {
    val networkScanViewModel: NetworkScanViewModel = viewModel()
    val isScanning by networkScanViewModel.isScanning.collectAsState()
    val scanResults by networkScanViewModel.scanResults.collectAsState()
    val recentScans by networkScanViewModel.recentScans.collectAsState()
    
    var selectedScanType by remember { mutableStateOf("discovery") }
    var targetRange by remember { mutableStateOf("192.168.1.0/24") }
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Quick Scan Actions
        item {
            QuickScanActionsSection(
                onScanTypeSelected = { scanType ->
                    selectedScanType = scanType
                    when (scanType) {
                        "discovery" -> networkScanViewModel.startDeviceDiscovery()
                        "vulnerability" -> networkScanViewModel.startVulnerabilityScan()
                        "port" -> networkScanViewModel.startPortScan()
                        "security" -> networkScanViewModel.startSecurityAudit()
                    }
                },
                isScanning = isScanning,
                selectedScanType = selectedScanType
            )
        }
        
        // Advanced Scan Controls
        item {
            AdvancedScanControlsSection(
                scanType = selectedScanType,
                targetRange = targetRange,
                onTargetRangeChange = { targetRange = it },
                onStartScan = {
                    when (selectedScanType) {
                        "discovery" -> networkScanViewModel.startDeviceDiscovery()
                        "vulnerability" -> networkScanViewModel.startVulnerabilityScan()
                        "port" -> networkScanViewModel.startPortScan()
                        "security" -> networkScanViewModel.startSecurityAudit()
                    }
                },
                isScanning = isScanning
            )
        }
        
        // Threat Intelligence Tools
        item {
            ThreatIntelligenceSection()
        }
        
        // Recent Scans
        if (recentScans.isNotEmpty()) {
            item {
                RecentScansSection(recentScans = recentScans)
            }
        }
        
        // Current Scan Results
        if (scanResults != null) {
            item {
                CurrentScanResultsSection(scanResults = scanResults!!)
            }
        }
    }
}

@Composable
fun QuickScanActionsSection(
    onScanTypeSelected: (String) -> Unit,
    isScanning: Boolean,
    selectedScanType: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Quick Scan Actions",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    QuickScanCard(
                        icon = Icons.Default.Search,
                        title = "Device Discovery",
                        description = "Find all devices",
                        scanType = "discovery",
                        isActive = isScanning && selectedScanType == "discovery",
                        onClick = { onScanTypeSelected("discovery") }
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Security,
                        title = "Vulnerability Scan",
                        description = "Check for CVEs",
                        scanType = "vulnerability",
                        isActive = isScanning && selectedScanType == "vulnerability",
                        onClick = { onScanTypeSelected("vulnerability") }
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Cable,
                        title = "Port Scan",
                        description = "Check open ports",
                        scanType = "port",
                        isActive = isScanning && selectedScanType == "port",
                        onClick = { onScanTypeSelected("port") }
                    )
                }
                item {
                    QuickScanCard(
                        icon = Icons.Default.Lock,
                        title = "Security Audit",
                        description = "Full security check",
                        scanType = "security",
                        isActive = isScanning && selectedScanType == "security",
                        onClick = { onScanTypeSelected("security") }
                    )
                }
            }
        }
    }
}

@Composable
fun QuickScanCard(
    icon: ImageVector,
    title: String,
    description: String,
    scanType: String,
    isActive: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .width(160.dp)
            .height(120.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isActive) {
                Color(0xFF3B82F6).copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(32.dp),
                tint = if (isActive) Color(0xFF3B82F6) else MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Column {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                if (isActive) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(12.dp),
                            strokeWidth = 2.dp,
                            color = Color(0xFF3B82F6)
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "Running...",
                            fontSize = 10.sp,
                            color = Color(0xFF3B82F6)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun AdvancedScanControlsSection(
    scanType: String,
    targetRange: String,
    onTargetRangeChange: (String) -> Unit,
    onStartScan: () -> Unit,
    isScanning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Advanced Network Scanner",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Scan Type Dropdown
            var expanded by remember { mutableStateOf(false) }
            
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = when (scanType) {
                        "discovery" -> "Device Discovery"
                        "vulnerability" -> "Vulnerability Scan"
                        "port" -> "Port Scan"
                        "security" -> "Security Audit"
                        else -> "Device Discovery"
                    },
                    onValueChange = {},
                    readOnly = true,
                    label = { Text("Scan Type") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    listOf(
                        "discovery" to "Device Discovery",
                        "vulnerability" to "Vulnerability Scan",
                        "port" to "Port Scan",
                        "security" to "Security Audit"
                    ).forEach { (type, label) ->
                        DropdownMenuItem(
                            text = { Text(label) },
                            onClick = {
                                expanded = false
                                // Update scan type
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Target Range Input (for discovery and port scans)
            if (scanType == "discovery" || scanType == "port") {
                OutlinedTextField(
                    value = targetRange,
                    onValueChange = onTargetRangeChange,
                    label = { Text("Target Range") },
                    placeholder = { Text("192.168.1.0/24") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Start Scan Button
            Button(
                onClick = onStartScan,
                enabled = !isScanning,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF3B82F6)
                )
            ) {
                if (isScanning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Scanning...")
                } else {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "Start scan",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Start Scan")
                }
            }
            
            // Scan Description
            Spacer(modifier = Modifier.height(12.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = when (scanType) {
                            "discovery" -> "Device Discovery Scan"
                            "vulnerability" -> "Vulnerability Assessment"
                            "port" -> "Port Scanning"
                            "security" -> "Security Audit"
                            else -> "Network Scan"
                        },
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = when (scanType) {
                            "discovery" -> "Discovers all devices on the network using ARP scanning, ping sweeps, and device fingerprinting."
                            "vulnerability" -> "Scans for known vulnerabilities using CVE database and security advisories."
                            "port" -> "Performs comprehensive port scanning to identify open services and potential security risks."
                            "security" -> "Conducts a full security audit including weak passwords, misconfigurations, and security best practices."
                            else -> "Performs network scanning to discover devices and assess security."
                        },
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}

@Composable
fun ThreatIntelligenceSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Threat Intelligence & CVE Lookup",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    ThreatCheckCard(
                        title = "IP Check",
                        placeholder = "*************",
                        icon = Icons.Default.Computer
                    )
                }
                item {
                    ThreatCheckCard(
                        title = "Domain Check",
                        placeholder = "example.com",
                        icon = Icons.Default.Language
                    )
                }
                item {
                    ThreatCheckCard(
                        title = "CVE Lookup",
                        placeholder = "CVE-2024-0001",
                        icon = Icons.Default.BugReport
                    )
                }
            }
        }
    }
}

@Composable
fun ThreatCheckCard(
    title: String,
    placeholder: String,
    icon: ImageVector
) {
    var inputValue by remember { mutableStateOf("") }
    var isChecking by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.width(200.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            OutlinedTextField(
                value = inputValue,
                onValueChange = { inputValue = it },
                placeholder = { Text(placeholder, fontSize = 12.sp) },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Button(
                onClick = {
                    isChecking = true
                    // TODO: Implement threat check
                },
                enabled = inputValue.isNotBlank() && !isChecking,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFEF4444)
                )
            ) {
                if (isChecking) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(14.dp),
                        strokeWidth = 2.dp,
                        color = Color.White
                    )
                } else {
                    Text("Check", fontSize = 12.sp)
                }
            }
        }
    }
}

@Composable
fun RecentScansSection(recentScans: List<ScanResult>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Recent Scans",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            recentScans.take(3).forEach { scan ->
                RecentScanItem(scan = scan)
                if (scan != recentScans.take(3).last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

@Composable
fun RecentScanItem(scan: ScanResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "${scan.scanType.name.lowercase().replaceFirstChar { it.uppercase() }} Scan",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "Started: ${java.text.SimpleDateFormat("MMM dd, HH:mm").format(java.util.Date(scan.startTime))}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = when (scan.scanStatus) {
                        ScanResult.ScanStatus.COMPLETED -> "Completed"
                        ScanResult.ScanStatus.RUNNING -> "Running"
                        ScanResult.ScanStatus.FAILED -> "Failed"
                        else -> "Unknown"
                    },
                    fontSize = 12.sp,
                    color = when (scan.scanStatus) {
                        ScanResult.ScanStatus.COMPLETED -> Color(0xFF10B981)
                        ScanResult.ScanStatus.RUNNING -> Color(0xFF3B82F6)
                        ScanResult.ScanStatus.FAILED -> Color(0xFFEF4444)
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                if (scan.scanStatus == ScanResult.ScanStatus.COMPLETED) {
                    Text(
                        text = "${scan.devices.size} devices, ${scan.vulnerabilities.size} issues",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun CurrentScanResultsSection(scanResults: ScanResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Current Scan Results",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ResultStat(
                    icon = Icons.Default.Devices,
                    label = "Devices",
                    value = "${scanResults.devices.size}",
                    color = Color(0xFF10B981)
                )
                ResultStat(
                    icon = Icons.Default.Security,
                    label = "Vulnerabilities",
                    value = "${scanResults.vulnerabilities.size}",
                    color = if (scanResults.vulnerabilities.isNotEmpty()) Color(0xFFEF4444) else Color(0xFF10B981)
                )
                ResultStat(
                    icon = Icons.Default.Speed,
                    label = "Duration",
                    value = "${(scanResults.endTime - scanResults.startTime) / 1000}s",
                    color = Color(0xFF3B82F6)
                )
            }
        }
    }
}

@Composable
fun ResultStat(
    icon: ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(24.dp),
            tint = color
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}
