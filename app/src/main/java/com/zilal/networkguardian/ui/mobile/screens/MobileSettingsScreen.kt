package com.zilal.networkguardian.ui.mobile.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.auth.viewmodel.AuthViewModel
import com.zilal.networkguardian.ui.mobile.theme.premiumColor

/**
 * Mobile settings screen with premium features and account management
 */
@Composable
fun MobileSettingsScreen(
    authViewModel: AuthViewModel,
    modifier: Modifier = Modifier
) {
    val currentUser by authViewModel.currentUser.collectAsStateWithLifecycle()
    var showLogoutDialog by remember { mutableStateOf(false) }
    var showServerInfo by remember { mutableStateOf(false) }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Header
            MobileSettingsHeader(
                userName = currentUser?.firstName ?: "User",
                userEmail = currentUser?.email ?: "<EMAIL>"
            )
        }
        
        item {
            // Premium Status
            MobilePremiumStatus()
        }
        
        items(getSettingsSections()) { section ->
            MobileSettingsSection(
                section = section,
                onItemClick = { item ->
                    when (item.id) {
                        "logout" -> showLogoutDialog = true
                        "server_info" -> showServerInfo = true
                        // Handle other settings
                    }
                }
            )
        }
        
        item {
            // App Info
            MobileAppInfo()
        }
        
        // Bottom padding
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
    
    // Logout Confirmation Dialog
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = { Text("Sign Out") },
            text = { Text("Are you sure you want to sign out?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLogoutDialog = false
                        authViewModel.logout()
                    }
                ) {
                    Text("Sign Out")
                }
            },
            dismissButton = {
                TextButton(onClick = { showLogoutDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
    
    // Server Info Dialog
    if (showServerInfo) {
        AlertDialog(
            onDismissRequest = { showServerInfo = false },
            title = { Text("Server Information") },
            text = { 
                Column {
                    Text("Local Development Server")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("URL: http://192.168.10.194:3001")
                    Text("Status: Connected")
                    Text("Features: All APIs integrated")
                }
            },
            confirmButton = {
                TextButton(onClick = { showServerInfo = false }) {
                    Text("OK")
                }
            }
        )
    }
}

@Composable
private fun MobileSettingsHeader(
    userName: String,
    userEmail: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Avatar
            Card(
                modifier = Modifier.size(56.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = userName.firstOrNull()?.uppercase() ?: "U",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // User Info
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = userName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = userEmail,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "Premium",
                        tint = premiumColor(),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Premium Member",
                        fontSize = 12.sp,
                        color = premiumColor(),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // Edit Button
            IconButton(onClick = { /* TODO: Edit profile */ }) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "Edit Profile"
                )
            }
        }
    }
}

@Composable
private fun MobilePremiumStatus() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = premiumColor().copy(alpha = 0.1f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Diamond,
                contentDescription = "Premium",
                tint = premiumColor(),
                modifier = Modifier.size(32.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "Premium Plan",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = premiumColor()
                )
                Text(
                    text = "All features unlocked • No limits",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            Text(
                text = "$29.99/mo",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = premiumColor()
            )
        }
    }
}

@Composable
private fun MobileSettingsSection(
    section: SettingsSection,
    onItemClick: (SettingsItem) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = section.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            section.items.forEach { item ->
                MobileSettingsItem(
                    item = item,
                    onClick = { onItemClick(item) }
                )
            }
        }
    }
}

@Composable
private fun MobileSettingsItem(
    item: SettingsItem,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = if (item.isDestructive) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                fontSize = 14.sp,
                color = if (item.isDestructive) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface
            )
            if (item.subtitle.isNotEmpty()) {
                Text(
                    text = item.subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
        if (item.showArrow) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Navigate",
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Composable
private fun MobileAppInfo() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Security,
                contentDescription = "App Icon",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Network Guardian",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "Version 1.0.0 (Premium)",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Premium Network Security Platform",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
            )
        }
    }
}

// Data classes
data class SettingsSection(
    val title: String,
    val items: List<SettingsItem>
)

data class SettingsItem(
    val id: String,
    val title: String,
    val subtitle: String = "",
    val icon: ImageVector,
    val showArrow: Boolean = true,
    val isDestructive: Boolean = false
)

// Settings data
private fun getSettingsSections(): List<SettingsSection> {
    return listOf(
        SettingsSection(
            title = "Network",
            items = listOf(
                SettingsItem("scan_settings", "Scan Settings", "Configure automatic scanning", Icons.Default.Radar),
                SettingsItem("network_config", "Network Configuration", "WiFi and connection settings", Icons.Default.Wifi),
                SettingsItem("device_management", "Device Management", "Manage trusted devices", Icons.Default.Devices),
                SettingsItem("security_policies", "Security Policies", "Configure security rules", Icons.Default.Security)
            )
        ),
        SettingsSection(
            title = "Analytics",
            items = listOf(
                SettingsItem("analytics_settings", "Analytics Settings", "Data collection preferences", Icons.Default.Analytics),
                SettingsItem("ai_insights", "AI Insights", "Machine learning preferences", Icons.Default.Psychology),
                SettingsItem("export_data", "Export Data", "Download your network data", Icons.Default.Download),
                SettingsItem("data_retention", "Data Retention", "How long to keep scan data", Icons.Default.Storage)
            )
        ),
        SettingsSection(
            title = "Notifications",
            items = listOf(
                SettingsItem("push_notifications", "Push Notifications", "Security alerts and updates", Icons.Default.Notifications),
                SettingsItem("email_alerts", "Email Alerts", "Critical security notifications", Icons.Default.Email),
                SettingsItem("alert_frequency", "Alert Frequency", "How often to receive alerts", Icons.Default.Schedule)
            )
        ),
        SettingsSection(
            title = "Privacy & Security",
            items = listOf(
                SettingsItem("privacy_settings", "Privacy Settings", "Data sharing preferences", Icons.Default.PrivacyTip),
                SettingsItem("encryption", "Encryption", "Data encryption settings", Icons.Default.Lock),
                SettingsItem("two_factor", "Two-Factor Auth", "Enable 2FA for extra security", Icons.Default.Security),
                SettingsItem("session_management", "Active Sessions", "Manage logged in devices", Icons.Default.Devices)
            )
        ),
        SettingsSection(
            title = "Support",
            items = listOf(
                SettingsItem("help_center", "Help Center", "FAQs and documentation", Icons.Default.Help),
                SettingsItem("contact_support", "Contact Support", "Get help from our team", Icons.Default.Support),
                SettingsItem("feedback", "Send Feedback", "Help us improve the app", Icons.Default.Feedback),
                SettingsItem("server_info", "Server Info", "Local development server", Icons.Default.Info)
            )
        ),
        SettingsSection(
            title = "Account",
            items = listOf(
                SettingsItem("change_password", "Change Password", "Update your password", Icons.Default.Key),
                SettingsItem("subscription", "Subscription", "Manage your premium plan", Icons.Default.Star),
                SettingsItem("logout", "Sign Out", "Sign out of your account", Icons.Default.Logout, showArrow = false, isDestructive = true)
            )
        )
    )
}
