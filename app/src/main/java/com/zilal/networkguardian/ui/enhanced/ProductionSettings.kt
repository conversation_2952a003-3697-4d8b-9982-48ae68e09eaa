package com.zilal.networkguardian.ui.enhanced

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.viewmodel.SettingsViewModel

/**
 * Production-ready settings screen with user authentication and profile management
 */
@Composable
fun ProductionSettings() {
    val settingsViewModel: SettingsViewModel = viewModel()
    val isDarkTheme by settingsViewModel.isDarkTheme.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // User Profile Section
        item {
            UserProfileSection()
        }
        
        // Security Settings
        item {
            SecuritySettingsSection()
        }
        
        // Scan Settings
        item {
            ScanSettingsSection()
        }
        
        // Notification Settings
        item {
            NotificationSettingsSection()
        }
        
        // App Settings
        item {
            AppSettingsSection(
                isDarkTheme = isDarkTheme,
                onThemeToggle = { settingsViewModel.toggleTheme() }
            )
        }
        
        // Data & Privacy
        item {
            DataPrivacySection()
        }
        
        // About & Support
        item {
            AboutSupportSection()
        }
    }
}

@Composable
fun UserProfileSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "User Profile",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Profile Avatar
                Card(
                    modifier = Modifier.size(60.dp),
                    shape = RoundedCornerShape(30.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF3B82F6)
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Profile",
                            tint = Color.White,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Security Admin",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "<EMAIL>",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "Premium Account",
                        fontSize = 12.sp,
                        color = Color(0xFF10B981)
                    )
                }
                
                IconButton(onClick = { /* TODO: Edit profile */ }) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit profile"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    onClick = { /* TODO: Sign out */ },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFEF4444)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Logout,
                        contentDescription = "Sign out",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Sign Out")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: Sync data */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Sync,
                        contentDescription = "Sync",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Sync")
                }
            }
        }
    }
}

@Composable
fun SecuritySettingsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Security Settings",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = Icons.Default.Lock,
                title = "App Lock",
                subtitle = "Require authentication to open app",
                trailing = {
                    var enabled by remember { mutableStateOf(false) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.Fingerprint,
                title = "Biometric Authentication",
                subtitle = "Use fingerprint or face unlock",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.VpnKey,
                title = "Two-Factor Authentication",
                subtitle = "Add extra security to your account",
                onClick = { /* TODO: Setup 2FA */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Security,
                title = "Security Audit",
                subtitle = "Review security settings and recommendations",
                onClick = { /* TODO: Security audit */ }
            )
        }
    }
}

@Composable
fun ScanSettingsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Scan Settings",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = Icons.Default.Schedule,
                title = "Auto Scan Interval",
                subtitle = "Automatically scan network every hour",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.Speed,
                title = "Scan Speed",
                subtitle = "Balance between speed and thoroughness",
                onClick = { /* TODO: Scan speed settings */ }
            )
            
            SettingsItem(
                icon = Icons.Default.NetworkCheck,
                title = "Background Monitoring",
                subtitle = "Monitor network changes in background",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.CloudSync,
                title = "Cloud Sync",
                subtitle = "Sync scan results to cloud storage",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
        }
    }
}

@Composable
fun NotificationSettingsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Notifications",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = Icons.Default.Notifications,
                title = "Push Notifications",
                subtitle = "Receive alerts and updates",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.Warning,
                title = "Security Alerts",
                subtitle = "Immediate alerts for security threats",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.DeviceUnknown,
                title = "New Device Alerts",
                subtitle = "Notify when new devices join network",
                trailing = {
                    var enabled by remember { mutableStateOf(true) }
                    Switch(
                        checked = enabled,
                        onCheckedChange = { enabled = it }
                    )
                }
            )
        }
    }
}

@Composable
fun AppSettingsSection(
    isDarkTheme: Boolean,
    onThemeToggle: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "App Settings",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = if (isDarkTheme) Icons.Default.DarkMode else Icons.Default.LightMode,
                title = "Theme",
                subtitle = if (isDarkTheme) "Dark mode enabled" else "Light mode enabled",
                trailing = {
                    Switch(
                        checked = isDarkTheme,
                        onCheckedChange = { onThemeToggle() }
                    )
                }
            )
            
            SettingsItem(
                icon = Icons.Default.Language,
                title = "Language",
                subtitle = "English (US)",
                onClick = { /* TODO: Language settings */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Storage,
                title = "Storage",
                subtitle = "Manage app data and cache",
                onClick = { /* TODO: Storage settings */ }
            )
        }
    }
}

@Composable
fun DataPrivacySection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Data & Privacy",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = Icons.Default.PrivacyTip,
                title = "Privacy Policy",
                subtitle = "Read our privacy policy",
                onClick = { /* TODO: Privacy policy */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Description,
                title = "Terms of Service",
                subtitle = "View terms and conditions",
                onClick = { /* TODO: Terms of service */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Delete,
                title = "Clear Data",
                subtitle = "Remove all app data and settings",
                onClick = { /* TODO: Clear data */ }
            )
        }
    }
}

@Composable
fun AboutSupportSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "About & Support",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingsItem(
                icon = Icons.Default.Info,
                title = "About Zilal",
                subtitle = "Version 1.0.0 (Production)",
                onClick = { /* TODO: About */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Help,
                title = "Help & Support",
                subtitle = "Get help and contact support",
                onClick = { /* TODO: Help */ }
            )
            
            SettingsItem(
                icon = Icons.Default.BugReport,
                title = "Report Bug",
                subtitle = "Report issues and feedback",
                onClick = { /* TODO: Bug report */ }
            )
            
            SettingsItem(
                icon = Icons.Default.Update,
                title = "Check for Updates",
                subtitle = "Check for app updates",
                onClick = { /* TODO: Check updates */ }
            )
        }
    }
}

@Composable
fun SettingsItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    trailing: @Composable (() -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else {
                    Modifier
                }
            ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
            
            if (trailing != null) {
                trailing()
            } else if (onClick != null) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "Navigate",
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                )
            }
        }
    }
    
    Spacer(modifier = Modifier.height(8.dp))
}
