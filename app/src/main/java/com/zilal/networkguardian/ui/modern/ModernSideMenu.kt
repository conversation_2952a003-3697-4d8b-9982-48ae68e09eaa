package com.zilal.networkguardian.ui.modern

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Modern side navigation menu with glass morphism and advanced features
 */
@Composable
fun ModernSideMenu(
    currentView: String,
    onViewChange: (String) -> Unit,
    onDismiss: () -> Unit,
    isDarkTheme: Boolean
) {
    // Glass morphism background
    val menuBackground = if (isDarkTheme) {
        Color(0xFF1E293B).copy(alpha = 0.95f)
    } else {
        Color.White.copy(alpha = 0.95f)
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.3f))
            .clickable { onDismiss() }
    ) {
        Card(
            modifier = Modifier
                .fillMaxHeight()
                .width(320.dp)
                .padding(16.dp)
                .clickable(enabled = false) { }, // Prevent dismissing when clicking menu
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = menuBackground
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 16.dp)
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Header Section
                item {
                    ModernMenuHeader(isDarkTheme = isDarkTheme)
                    Spacer(modifier = Modifier.height(24.dp))
                }

                // Main Navigation
                item {
                    Text(
                        text = "MAIN",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = if (isDarkTheme) Color(0xFF94A3B8) else Color(0xFF64748B),
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }

                items(mainMenuItems) { item ->
                    ModernMenuItem(
                        item = item,
                        isSelected = currentView == item.route,
                        onClick = { onViewChange(item.route) },
                        isDarkTheme = isDarkTheme
                    )
                }

                // Security Section
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "SECURITY",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = if (isDarkTheme) Color(0xFF94A3B8) else Color(0xFF64748B),
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }

                items(securityMenuItems) { item ->
                    ModernMenuItem(
                        item = item,
                        isSelected = currentView == item.route,
                        onClick = { onViewChange(item.route) },
                        isDarkTheme = isDarkTheme
                    )
                }

                // Tools Section
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "TOOLS",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = if (isDarkTheme) Color(0xFF94A3B8) else Color(0xFF64748B),
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }

                items(toolsMenuItems) { item ->
                    ModernMenuItem(
                        item = item,
                        isSelected = currentView == item.route,
                        onClick = { onViewChange(item.route) },
                        isDarkTheme = isDarkTheme
                    )
                }

                // Bottom Section
                item {
                    Spacer(modifier = Modifier.height(32.dp))
                    ModernMenuFooter(isDarkTheme = isDarkTheme)
                }
            }
        }
    }
}

@Composable
fun ModernMenuHeader(isDarkTheme: Boolean) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Profile Avatar with Gradient
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(
                    Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF3B82F6),
                            Color(0xFF8B5CF6),
                            Color(0xFFEC4899)
                        )
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Profile",
                tint = Color.White,
                modifier = Modifier.size(40.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Security Admin",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = if (isDarkTheme) Color.White else Color(0xFF1E293B)
        )

        Text(
            text = "<EMAIL>",
            fontSize = 14.sp,
            color = if (isDarkTheme) Color(0xFF94A3B8) else Color(0xFF64748B)
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Status Badge
        Card(
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF10B981).copy(alpha = 0.1f)
            )
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFF10B981))
                )
                Text(
                    text = "Online",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF10B981)
                )
            }
        }
    }
}

@Composable
fun ModernMenuItem(
    item: MenuItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    isDarkTheme: Boolean
) {
    val backgroundColor = when {
        isSelected && isDarkTheme -> Color(0xFF3B82F6).copy(alpha = 0.2f)
        isSelected && !isDarkTheme -> Color(0xFF3B82F6).copy(alpha = 0.1f)
        !isSelected && isDarkTheme -> Color.Transparent
        else -> Color.Transparent
    }

    val contentColor = when {
        isSelected -> Color(0xFF3B82F6)
        isDarkTheme -> Color.White
        else -> Color(0xFF1E293B)
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = if (isSelected) CardDefaults.cardElevation(defaultElevation = 4.dp) else CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Icon with background
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        if (isSelected) {
                            Color(0xFF3B82F6).copy(alpha = 0.2f)
                        } else {
                            if (isDarkTheme) Color(0xFF374151) else Color(0xFFF3F4F6)
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = item.icon,
                    contentDescription = item.title,
                    tint = contentColor,
                    modifier = Modifier.size(20.dp)
                )
            }

            // Title and subtitle
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = item.title,
                    fontSize = 16.sp,
                    fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                    color = contentColor
                )
                if (item.subtitle.isNotEmpty()) {
                    Text(
                        text = item.subtitle,
                        fontSize = 12.sp,
                        color = contentColor.copy(alpha = 0.7f)
                    )
                }
            }

            // Badge or indicator
            if (item.badge.isNotEmpty()) {
                Card(
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFEF4444)
                    )
                ) {
                    Text(
                        text = item.badge,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Arrow for selected item
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "Selected",
                    tint = contentColor,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
fun ModernMenuFooter(isDarkTheme: Boolean) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Quick Actions
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            QuickActionButton(
                icon = Icons.Default.Settings,
                onClick = { /* TODO: Settings */ },
                isDarkTheme = isDarkTheme
            )
            QuickActionButton(
                icon = Icons.Default.Help,
                onClick = { /* TODO: Help */ },
                isDarkTheme = isDarkTheme
            )
            QuickActionButton(
                icon = Icons.Default.Logout,
                onClick = { /* TODO: Logout */ },
                isDarkTheme = isDarkTheme
            )
        }

        // Version Info
        Text(
            text = "SecureNet v1.0.0",
            fontSize = 12.sp,
            color = if (isDarkTheme) Color(0xFF64748B) else Color(0xFF94A3B8)
        )
    }
}

@Composable
fun QuickActionButton(
    icon: ImageVector,
    onClick: () -> Unit,
    isDarkTheme: Boolean
) {
    IconButton(
        onClick = onClick,
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(
                if (isDarkTheme) Color(0xFF374151) else Color(0xFFF3F4F6)
            )
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = if (isDarkTheme) Color.White else Color(0xFF1F2937),
            modifier = Modifier.size(20.dp)
        )
    }
}

// Data classes for menu items
data class MenuItem(
    val route: String,
    val title: String,
    val subtitle: String = "",
    val icon: ImageVector,
    val badge: String = ""
)

// Menu items data
val mainMenuItems = listOf(
    MenuItem("dashboard", "Dashboard", "Network overview", Icons.Default.Dashboard),
    MenuItem("scanner", "Network Scanner", "Discover & analyze", Icons.Default.Search),
    MenuItem("devices", "Device Manager", "Connected devices", Icons.Default.Devices),
)

val securityMenuItems = listOf(
    MenuItem("security", "Security Dashboard", "Threats & vulnerabilities", Icons.Default.Security, "3"),
    MenuItem("threat", "Threat Intelligence", "Real-time analysis", Icons.Default.Warning),
    MenuItem("firewall", "Firewall Rules", "Access control", Icons.Default.Shield),
)

val toolsMenuItems = listOf(
    MenuItem("admin", "Admin Dashboard", "System management", Icons.Default.AdminPanelSettings),
    MenuItem("reports", "Reports", "Analytics & insights", Icons.Default.Assessment),
    MenuItem("settings", "Settings", "App configuration", Icons.Default.Settings),
)
