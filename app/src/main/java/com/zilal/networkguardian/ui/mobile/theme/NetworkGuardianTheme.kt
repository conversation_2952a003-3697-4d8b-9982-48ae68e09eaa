package com.zilal.networkguardian.ui.mobile.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat

/**
 * Mobile-optimized theme for Network Guardian
 * Premium dark theme with modern Material Design 3
 */

// Premium Color Palette
private val PrimaryColor = Color(0xFF00BCD4) // Cyan - Tech/Security feel
private val SecondaryColor = Color(0xFF4CAF50) // Green - Success/Safe
private val TertiaryColor = Color(0xFFFF9800) // Orange - Warning/Alert
private val ErrorColor = Color(0xFFF44336) // Red - Danger/Error

// Dark Theme Colors
private val DarkColorScheme = darkColorScheme(
    primary = PrimaryColor,
    onPrimary = Color.White,
    primaryContainer = PrimaryColor.copy(alpha = 0.2f),
    onPrimaryContainer = PrimaryColor,
    
    secondary = SecondaryColor,
    onSecondary = Color.White,
    secondaryContainer = SecondaryColor.copy(alpha = 0.2f),
    onSecondaryContainer = SecondaryColor,
    
    tertiary = TertiaryColor,
    onTertiary = Color.White,
    tertiaryContainer = TertiaryColor.copy(alpha = 0.2f),
    onTertiaryContainer = TertiaryColor,
    
    error = ErrorColor,
    onError = Color.White,
    errorContainer = ErrorColor.copy(alpha = 0.2f),
    onErrorContainer = ErrorColor,
    
    background = Color(0xFF0A0A0A), // Very dark background
    onBackground = Color(0xFFE0E0E0), // Light text
    
    surface = Color(0xFF1A1A1A), // Dark surface
    onSurface = Color(0xFFE0E0E0),
    surfaceVariant = Color(0xFF2A2A2A),
    onSurfaceVariant = Color(0xFFB0B0B0),
    
    outline = Color(0xFF404040),
    outlineVariant = Color(0xFF303030),
    
    scrim = Color.Black.copy(alpha = 0.5f)
)

// Light Theme Colors (for users who prefer light mode)
private val LightColorScheme = lightColorScheme(
    primary = PrimaryColor,
    onPrimary = Color.White,
    primaryContainer = PrimaryColor.copy(alpha = 0.1f),
    onPrimaryContainer = PrimaryColor,
    
    secondary = SecondaryColor,
    onSecondary = Color.White,
    secondaryContainer = SecondaryColor.copy(alpha = 0.1f),
    onSecondaryContainer = SecondaryColor,
    
    tertiary = TertiaryColor,
    onTertiary = Color.White,
    tertiaryContainer = TertiaryColor.copy(alpha = 0.1f),
    onTertiaryContainer = TertiaryColor,
    
    error = ErrorColor,
    onError = Color.White,
    errorContainer = ErrorColor.copy(alpha = 0.1f),
    onErrorContainer = ErrorColor,
    
    background = Color(0xFFFAFAFA), // Light background
    onBackground = Color(0xFF1A1A1A), // Dark text
    
    surface = Color.White, // Light surface
    onSurface = Color(0xFF1A1A1A),
    surfaceVariant = Color(0xFFF5F5F5),
    onSurfaceVariant = Color(0xFF606060),
    
    outline = Color(0xFFE0E0E0),
    outlineVariant = Color(0xFFF0F0F0),
    
    scrim = Color.Black.copy(alpha = 0.3f)
)

@Composable
fun NetworkGuardianTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // Disabled for consistent branding
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = androidx.compose.ui.platform.LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.background.toArgb()
            window.navigationBarColor = colorScheme.background.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = NetworkGuardianTypography,
        shapes = NetworkGuardianShapes,
        content = content
    )
}

// Custom Typography for premium feel
val NetworkGuardianTypography = Typography(
    displayLarge = Typography().displayLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    displayMedium = Typography().displayMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    displaySmall = Typography().displaySmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    headlineLarge = Typography().headlineLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    headlineMedium = Typography().headlineMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    headlineSmall = Typography().headlineSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    titleLarge = Typography().titleLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    titleMedium = Typography().titleMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    titleSmall = Typography().titleSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    bodyLarge = Typography().bodyLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    bodyMedium = Typography().bodyMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    bodySmall = Typography().bodySmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    labelLarge = Typography().labelLarge.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    labelMedium = Typography().labelMedium.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    ),
    labelSmall = Typography().labelSmall.copy(
        fontFamily = androidx.compose.ui.text.font.FontFamily.Default
    )
)

// Custom Shapes for modern feel
val NetworkGuardianShapes = Shapes(
    extraSmall = androidx.compose.foundation.shape.RoundedCornerShape(4.dp),
    small = androidx.compose.foundation.shape.RoundedCornerShape(8.dp),
    medium = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
    large = androidx.compose.foundation.shape.RoundedCornerShape(16.dp),
    extraLarge = androidx.compose.foundation.shape.RoundedCornerShape(24.dp)
)

// Extension functions for custom colors
@Composable
fun successColor(): Color = Color(0xFF4CAF50)

@Composable
fun warningColor(): Color = Color(0xFFFF9800)

@Composable
fun infoColor(): Color = Color(0xFF2196F3)

@Composable
fun premiumColor(): Color = Color(0xFFFFD700) // Gold for premium features
