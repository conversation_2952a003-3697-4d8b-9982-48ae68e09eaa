package com.zilal.networkguardian.ui.enhanced

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.viewmodel.NetworkScanViewModel
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.ScanResult

@Composable
fun EnhancedDashboard() {
    val networkScanViewModel: NetworkScanViewModel = viewModel()
    val scanResults by networkScanViewModel.scanResults.collectAsState()
    val isScanning by networkScanViewModel.isScanning.collectAsState()
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Network Stats Cards
        item {
            NetworkStatsSection(scanResults = scanResults)
        }
        
        // Quick Actions
        item {
            QuickActionsSection(
                onStartScan = { networkScanViewModel.startQuickScan() },
                isScanning = isScanning
            )
        }
        
        // Recent Devices
        item {
            RecentDevicesSection(devices = scanResults?.devices ?: emptyList())
        }
        
        // Security Alerts
        item {
            SecurityAlertsSection(vulnerabilities = scanResults?.vulnerabilities ?: emptyList())
        }
    }
}

@Composable
fun NetworkStatsSection(scanResults: ScanResult?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Network Overview",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    StatCard(
                        icon = Icons.Default.Devices,
                        title = "Devices",
                        value = "${scanResults?.devices?.size ?: 0}",
                        subtitle = "Connected",
                        color = Color(0xFF10B981)
                    )
                }
                item {
                    StatCard(
                        icon = Icons.Default.Security,
                        title = "Vulnerabilities",
                        value = "${scanResults?.vulnerabilities?.size ?: 0}",
                        subtitle = "Found",
                        color = if ((scanResults?.vulnerabilities?.size ?: 0) > 0) Color(0xFFEF4444) else Color(0xFF10B981)
                    )
                }
                item {
                    StatCard(
                        icon = Icons.Default.NetworkCheck,
                        title = "Network",
                        value = if (scanResults != null) "Secure" else "Unknown",
                        subtitle = "Status",
                        color = Color(0xFF3B82F6)
                    )
                }
                item {
                    StatCard(
                        icon = Icons.Default.Speed,
                        title = "Performance",
                        value = "Good",
                        subtitle = "Rating",
                        color = Color(0xFF8B5CF6)
                    )
                }
            }
        }
    }
}

@Composable
fun StatCard(
    icon: ImageVector,
    title: String,
    value: String,
    subtitle: String,
    color: Color
) {
    Card(
        modifier = Modifier
            .width(120.dp)
            .height(100.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Column {
                Text(
                    text = value,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
                Text(
                    text = subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun QuickActionsSection(
    onStartScan: () -> Unit,
    isScanning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Quick Actions",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    QuickActionButton(
                        icon = Icons.Default.Search,
                        title = "Device Discovery",
                        subtitle = "Find devices",
                        onClick = onStartScan,
                        isLoading = isScanning,
                        color = Color(0xFF3B82F6)
                    )
                }
                item {
                    QuickActionButton(
                        icon = Icons.Default.Security,
                        title = "Security Scan",
                        subtitle = "Check vulnerabilities",
                        onClick = { /* TODO */ },
                        color = Color(0xFFEF4444)
                    )
                }
                item {
                    QuickActionButton(
                        icon = Icons.Default.Speed,
                        title = "Performance",
                        subtitle = "Test speed",
                        onClick = { /* TODO */ },
                        color = Color(0xFF10B981)
                    )
                }
                item {
                    QuickActionButton(
                        icon = Icons.Default.Settings,
                        title = "Configure",
                        subtitle = "Network settings",
                        onClick = { /* TODO */ },
                        color = Color(0xFF8B5CF6)
                    )
                }
            }
        }
    }
}

@Composable
fun QuickActionButton(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    isLoading: Boolean = false,
    color: Color
) {
    Card(
        modifier = Modifier
            .width(140.dp)
            .height(80.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = color
                )
            } else {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = color,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = title,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = color
                    )
                    Text(
                        text = subtitle,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun RecentDevicesSection(devices: List<NetworkDevice>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Recent Devices",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                TextButton(onClick = { /* TODO: Navigate to devices */ }) {
                    Text("View All")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (devices.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No devices found. Start a scan to discover devices.",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            } else {
                devices.take(3).forEach { device ->
                    DeviceListItem(device = device)
                    if (device != devices.take(3).last()) {
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                    }
                }
            }
        }
    }
}

@Composable
fun DeviceListItem(device: NetworkDevice) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = when (device.deviceType) {
                NetworkDevice.DeviceType.ROUTER -> Icons.Default.Router
                NetworkDevice.DeviceType.COMPUTER -> Icons.Default.Computer
                NetworkDevice.DeviceType.PHONE -> Icons.Default.Phone
                else -> Icons.Default.DeviceUnknown
            },
            contentDescription = "Device type",
            modifier = Modifier.size(24.dp),
            tint = if (device.isOnline) Color(0xFF10B981) else Color(0xFF6B7280)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = device.hostname ?: device.ipAddress,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = device.ipAddress,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        Text(
            text = if (device.isOnline) "Online" else "Offline",
            fontSize = 12.sp,
            color = if (device.isOnline) Color(0xFF10B981) else Color(0xFF6B7280)
        )
    }
}

@Composable
fun SecurityAlertsSection(vulnerabilities: List<com.zilal.networkguardian.model.Vulnerability>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Security Alerts",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                TextButton(onClick = { /* TODO: Navigate to security */ }) {
                    Text("View All")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (vulnerabilities.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "No alerts",
                            tint = Color(0xFF10B981),
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "No security alerts. Your network looks secure!",
                            color = Color(0xFF10B981)
                        )
                    }
                }
            } else {
                vulnerabilities.take(3).forEach { vulnerability ->
                    SecurityAlertItem(vulnerability = vulnerability)
                    if (vulnerability != vulnerabilities.take(3).last()) {
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                    }
                }
            }
        }
    }
}

@Composable
fun SecurityAlertItem(vulnerability: com.zilal.networkguardian.model.Vulnerability) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = when (vulnerability.severity) {
                com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Icons.Default.Error
                com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Icons.Default.Warning
                else -> Icons.Default.Info
            },
            contentDescription = "Severity",
            modifier = Modifier.size(20.dp),
            tint = when (vulnerability.severity) {
                com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Color(0xFFEF4444)
                com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Color(0xFFF59E0B)
                else -> Color(0xFF3B82F6)
            }
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = vulnerability.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = vulnerability.description,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                maxLines = 1
            )
        }
        
        Text(
            text = vulnerability.severity.name,
            fontSize = 12.sp,
            color = when (vulnerability.severity) {
                com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Color(0xFFEF4444)
                com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Color(0xFFF59E0B)
                else -> Color(0xFF3B82F6)
            }
        )
    }
}
