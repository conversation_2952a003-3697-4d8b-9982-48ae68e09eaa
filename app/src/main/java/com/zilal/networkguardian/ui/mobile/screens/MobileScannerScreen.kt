package com.zilal.networkguardian.ui.mobile.screens

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.network.model.NetworkDevice
import com.zilal.networkguardian.network.viewmodel.NetworkScanViewModel
import kotlinx.coroutines.delay

/**
 * Mobile-optimized network scanner screen with real scanning capabilities
 */
@Composable
fun MobileScannerScreen(
    modifier: Modifier = Modifier,
    viewModel: NetworkScanViewModel = hiltViewModel()
) {
    val scanState by viewModel.scanState.collectAsStateWithLifecycle()
    val discoveredDevices by viewModel.discoveredDevices.collectAsStateWithLifecycle()
    val scanProgress by viewModel.scanProgress.collectAsStateWithLifecycle()
    
    var selectedScanType by remember { mutableStateOf(ScanType.NETWORK_DISCOVERY) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        MobileScannerHeader()
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Scan Type Selection
        MobileScanTypeSelector(
            selectedType = selectedScanType,
            onTypeSelected = { selectedScanType = it }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Scan Control
        MobileScanControl(
            scanState = scanState,
            scanProgress = scanProgress,
            onStartScan = { viewModel.startScan(selectedScanType.name) },
            onStopScan = { viewModel.stopScan() }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Scan Results
        MobileScanResults(
            devices = discoveredDevices,
            scanState = scanState,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun MobileScannerHeader() {
    Column {
        Text(
            text = "Network Scanner",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = "Discover and analyze network devices",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun MobileScanTypeSelector(
    selectedType: ScanType,
    onTypeSelected: (ScanType) -> Unit
) {
    Column {
        Text(
            text = "Scan Type",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(ScanType.values()) { scanType ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedType == scanType) {
                            MaterialTheme.colorScheme.primaryContainer
                        } else {
                            MaterialTheme.colorScheme.surface
                        }
                    ),
                    onClick = { onTypeSelected(scanType) }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = scanType.icon,
                            contentDescription = scanType.displayName,
                            tint = if (selectedType == scanType) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            },
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = scanType.displayName,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = if (selectedType == scanType) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.onSurface
                                }
                            )
                            Text(
                                text = scanType.description,
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                        if (selectedType == scanType) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = "Selected",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileScanControl(
    scanState: com.zilal.networkguardian.network.model.ScanState,
    scanProgress: Float,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Scan Status
            AnimatedContent(
                targetState = scanState,
                transitionSpec = {
                    fadeIn() togetherWith fadeOut()
                },
                label = "scan_status"
            ) { state ->
                when (state) {
                    is com.zilal.networkguardian.network.model.ScanState.Idle -> {
                        Text(
                            text = "Ready to scan",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    is com.zilal.networkguardian.network.model.ScanState.Scanning -> {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = "Scanning network...",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            LinearProgressIndicator(
                                progress = { scanProgress },
                                modifier = Modifier.fillMaxWidth()
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "${(scanProgress * 100).toInt()}% complete",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            )
                        }
                    }
                    is com.zilal.networkguardian.network.model.ScanState.Completed -> {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = "Completed",
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "Scan completed",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "${state.devicesFound} devices found",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            )
                        }
                    }
                    is com.zilal.networkguardian.network.model.ScanState.Error -> {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = "Error",
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "Scan failed",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = state.message,
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Scan Button
            Button(
                onClick = {
                    when (scanState) {
                        is com.zilal.networkguardian.network.model.ScanState.Scanning -> onStopScan()
                        else -> onStartScan()
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = when (scanState) {
                        is com.zilal.networkguardian.network.model.ScanState.Scanning -> Icons.Default.Stop
                        else -> Icons.Default.PlayArrow
                    },
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when (scanState) {
                        is com.zilal.networkguardian.network.model.ScanState.Scanning -> "Stop Scan"
                        else -> "Start Scan"
                    },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun MobileScanResults(
    devices: List<NetworkDevice>,
    scanState: com.zilal.networkguardian.network.model.ScanState,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Discovered Devices",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "${devices.size} devices",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (devices.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.DeviceHub,
                        contentDescription = "No devices",
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (scanState is com.zilal.networkguardian.network.model.ScanState.Scanning) {
                            "Scanning for devices..."
                        } else {
                            "No devices found"
                        },
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                    if (scanState !is com.zilal.networkguardian.network.model.ScanState.Scanning) {
                        Text(
                            text = "Start a scan to discover network devices",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                        )
                    }
                }
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(devices) { device ->
                    MobileDeviceCard(device = device)
                }
            }
        }
    }
}

@Composable
private fun MobileDeviceCard(device: NetworkDevice) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Device Icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = when (device.deviceType.lowercase()) {
                        "router" -> Icons.Default.Router
                        "phone", "mobile" -> Icons.Default.PhoneAndroid
                        "computer", "laptop" -> Icons.Default.Computer
                        "tablet" -> Icons.Default.Tablet
                        "tv" -> Icons.Default.Tv
                        "printer" -> Icons.Default.Print
                        else -> Icons.Default.DeviceHub
                    },
                    contentDescription = device.deviceType,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Device Info
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.deviceName ?: "Unknown Device",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = device.ipAddress,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                if (device.macAddress.isNotEmpty()) {
                    Text(
                        text = device.macAddress,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
            
            // Security Status
            Icon(
                imageVector = when (device.securityRisk.lowercase()) {
                    "high" -> Icons.Default.Error
                    "medium" -> Icons.Default.Warning
                    else -> Icons.Default.CheckCircle
                },
                contentDescription = device.securityRisk,
                tint = when (device.securityRisk.lowercase()) {
                    "high" -> MaterialTheme.colorScheme.error
                    "medium" -> Color(0xFFFF9800)
                    else -> Color(0xFF4CAF50)
                },
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

// Scan type enum
enum class ScanType(
    val displayName: String,
    val description: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    NETWORK_DISCOVERY(
        "Network Discovery",
        "Find all devices on your network",
        Icons.Default.Radar
    ),
    VULNERABILITY_SCAN(
        "Vulnerability Scan",
        "Check for security vulnerabilities",
        Icons.Default.Security
    ),
    PORT_SCAN(
        "Port Scan",
        "Scan for open ports on devices",
        Icons.Default.Router
    ),
    DEVICE_AUDIT(
        "Device Audit",
        "Detailed analysis of device security",
        Icons.Default.Analytics
    )
}
