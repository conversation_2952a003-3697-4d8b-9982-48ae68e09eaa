package com.zilal.networkguardian.ui.mobile.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.auth.viewmodel.AuthViewModel
import kotlinx.coroutines.delay

/**
 * Mobile-optimized dashboard screen with all premium features
 */
@Composable
fun MobileDashboardScreen(
    authViewModel: AuthViewModel,
    modifier: Modifier = Modifier
) {
    val currentUser by authViewModel.currentUser.collectAsStateWithLifecycle()
    var networkStats by remember { mutableStateOf(NetworkStats()) }
    var securityAlerts by remember { mutableStateOf(listOf<SecurityAlert>()) }
    var recentScans by remember { mutableStateOf(listOf<ScanResult>()) }
    
    // Simulate real-time data updates
    LaunchedEffect(Unit) {
        while (true) {
            networkStats = generateNetworkStats()
            securityAlerts = generateSecurityAlerts()
            recentScans = generateRecentScans()
            delay(5000) // Update every 5 seconds
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Welcome Header
            MobileWelcomeHeader(currentUser?.firstName ?: "User")
        }
        
        item {
            // Quick Stats Cards
            MobileQuickStats(networkStats)
        }
        
        item {
            // Security Status
            MobileSecurityStatus(networkStats.securityScore)
        }
        
        item {
            // Quick Actions
            MobileQuickActions()
        }
        
        item {
            // Security Alerts
            if (securityAlerts.isNotEmpty()) {
                MobileSecurityAlerts(securityAlerts)
            }
        }
        
        item {
            // Recent Scans
            MobileRecentScans(recentScans)
        }
        
        item {
            // AI Insights
            MobileAIInsights()
        }
        
        // Bottom padding for navigation bar
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
private fun MobileWelcomeHeader(userName: String) {
    Column {
        Text(
            text = "Welcome back,",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
        )
        Text(
            text = userName,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = "Your network is being monitored",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
private fun MobileQuickStats(stats: NetworkStats) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(
            listOf(
                StatCard("Devices", "${stats.devicesFound}", Icons.Default.Devices, MaterialTheme.colorScheme.primary),
                StatCard("Threats", "${stats.threatsDetected}", Icons.Default.Warning, if (stats.threatsDetected > 0) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary),
                StatCard("Scans", "${stats.totalScans}", Icons.Default.Radar, MaterialTheme.colorScheme.secondary),
                StatCard("Uptime", "${stats.uptimeHours}h", Icons.Default.Timer, MaterialTheme.colorScheme.tertiary)
            )
        ) { stat ->
            Card(
                modifier = Modifier
                    .width(100.dp)
                    .height(100.dp),
                colors = CardDefaults.cardColors(
                    containerColor = stat.color.copy(alpha = 0.1f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = stat.icon,
                        contentDescription = stat.title,
                        tint = stat.color,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = stat.value,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = stat.color
                    )
                    Text(
                        text = stat.title,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileSecurityStatus(securityScore: Int) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                securityScore >= 80 -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                securityScore >= 60 -> Color(0xFFFF9800).copy(alpha = 0.1f)
                else -> Color(0xFFF44336).copy(alpha = 0.1f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Security,
                contentDescription = "Security",
                tint = when {
                    securityScore >= 80 -> Color(0xFF4CAF50)
                    securityScore >= 60 -> Color(0xFFFF9800)
                    else -> Color(0xFFF44336)
                },
                modifier = Modifier.size(32.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "Security Score",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
                )
                Text(
                    text = "$securityScore/100",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = when {
                        securityScore >= 80 -> "Excellent Security"
                        securityScore >= 60 -> "Good Security"
                        else -> "Needs Attention"
                    },
                    fontSize = 12.sp,
                    color = when {
                        securityScore >= 80 -> Color(0xFF4CAF50)
                        securityScore >= 60 -> Color(0xFFFF9800)
                        else -> Color(0xFFF44336)
                    }
                )
            }
            CircularProgressIndicator(
                progress = { securityScore / 100f },
                modifier = Modifier.size(48.dp),
                color = when {
                    securityScore >= 80 -> Color(0xFF4CAF50)
                    securityScore >= 60 -> Color(0xFFFF9800)
                    else -> Color(0xFFF44336)
                },
                strokeWidth = 4.dp
            )
        }
    }
}

@Composable
private fun MobileQuickActions() {
    Column {
        Text(
            text = "Quick Actions",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(
                listOf(
                    QuickAction("Scan Network", Icons.Default.Radar, MaterialTheme.colorScheme.primary),
                    QuickAction("View Devices", Icons.Default.Devices, MaterialTheme.colorScheme.secondary),
                    QuickAction("Security Check", Icons.Default.Security, MaterialTheme.colorScheme.tertiary),
                    QuickAction("AI Analysis", Icons.Default.Psychology, MaterialTheme.colorScheme.error)
                )
            ) { action ->
                Card(
                    modifier = Modifier
                        .width(120.dp)
                        .height(80.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = action.color.copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = action.icon,
                            contentDescription = action.title,
                            tint = action.color,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = action.title,
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onBackground,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileSecurityAlerts(alerts: List<SecurityAlert>) {
    Column {
        Text(
            text = "Security Alerts",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        alerts.take(3).forEach { alert ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = when (alert.severity) {
                        "high" -> MaterialTheme.colorScheme.errorContainer
                        "medium" -> Color(0xFFFF9800).copy(alpha = 0.1f)
                        else -> MaterialTheme.colorScheme.surfaceVariant
                    }
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = when (alert.severity) {
                            "high" -> Icons.Default.Error
                            "medium" -> Icons.Default.Warning
                            else -> Icons.Default.Info
                        },
                        contentDescription = alert.severity,
                        tint = when (alert.severity) {
                            "high" -> MaterialTheme.colorScheme.error
                            "medium" -> Color(0xFFFF9800)
                            else -> MaterialTheme.colorScheme.primary
                        },
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = alert.title,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = alert.description,
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileRecentScans(scans: List<ScanResult>) {
    Column {
        Text(
            text = "Recent Scans",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        scans.take(3).forEach { scan ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Radar,
                        contentDescription = "Scan",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = scan.type,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "${scan.devicesFound} devices found",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
                        )
                    }
                    Text(
                        text = scan.time,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileAIInsights() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = "AI",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI Insights",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Your network shows normal activity patterns. Consider enabling automatic scanning for better security coverage.",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
            )
        }
    }
}

// Data classes
data class NetworkStats(
    val devicesFound: Int = (8..25).random(),
    val threatsDetected: Int = (0..3).random(),
    val totalScans: Int = (15..50).random(),
    val uptimeHours: Int = (12..72).random(),
    val securityScore: Int = (65..95).random()
)

data class StatCard(
    val title: String,
    val value: String,
    val icon: ImageVector,
    val color: Color
)

data class QuickAction(
    val title: String,
    val icon: ImageVector,
    val color: Color
)

data class SecurityAlert(
    val title: String,
    val description: String,
    val severity: String
)

data class ScanResult(
    val type: String,
    val devicesFound: Int,
    val time: String
)

// Sample data generators
private fun generateSecurityAlerts(): List<SecurityAlert> {
    val alerts = listOf(
        SecurityAlert("Unknown Device", "New device detected on network", "medium"),
        SecurityAlert("Weak Password", "Router using default password", "high"),
        SecurityAlert("Open Port", "Port 22 is open on device", "low"),
        SecurityAlert("Outdated Firmware", "Device needs security update", "medium")
    )
    return alerts.shuffled().take((0..2).random())
}

private fun generateRecentScans(): List<ScanResult> {
    val types = listOf("Network Discovery", "Vulnerability Scan", "Device Audit", "Security Check")
    return types.map { type ->
        ScanResult(
            type = type,
            devicesFound = (5..20).random(),
            time = "${(1..60).random()}m ago"
        )
    }
}
