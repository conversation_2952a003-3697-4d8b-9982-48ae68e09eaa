package com.zilal.networkguardian.ui.enhanced

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.viewmodel.NetworkScanViewModel

/**
 * Production-ready dashboard that provides comprehensive network security overview
 * Matches the web application's design and functionality
 */
@Composable
fun ProductionDashboard() {
    val networkScanViewModel: NetworkScanViewModel = viewModel()
    val scanResults by networkScanViewModel.scanResults.collectAsState()
    val isScanning by networkScanViewModel.isScanning.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Welcome Header
        item {
            WelcomeHeader()
        }
        
        // Network Status Overview
        item {
            NetworkStatusCard(scanResults = scanResults)
        }
        
        // Quick Actions
        item {
            QuickActionsGrid(
                onStartScan = { networkScanViewModel.startQuickScan() },
                isScanning = isScanning
            )
        }
        
        // Security Metrics
        item {
            SecurityMetricsCard(scanResults = scanResults)
        }
        
        // Recent Activity
        item {
            RecentActivityCard(scanResults = scanResults)
        }
        
        // Network Health
        item {
            NetworkHealthCard()
        }
    }
}

@Composable
fun WelcomeHeader() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1E293B)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Zilal Logo
            Card(
                modifier = Modifier.size(60.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF3B82F6)
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "🛡️",
                        fontSize = 28.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column {
                Text(
                    text = "Welcome to Zilal",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = "Your network security guardian",
                    fontSize = 14.sp,
                    color = Color(0xFF94A3B8)
                )
                Text(
                    text = "Real-time protection • AI-powered analysis",
                    fontSize = 12.sp,
                    color = Color(0xFF64748B)
                )
            }
        }
    }
}

@Composable
fun NetworkStatusCard(scanResults: com.zilal.networkguardian.model.ScanResult?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Network Status",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                // Status Indicator
                Card(
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = if (scanResults?.vulnerabilities?.isEmpty() != false) 
                            Color(0xFF10B981) else Color(0xFFEF4444)
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = if (scanResults?.vulnerabilities?.isEmpty() != false) 
                                Icons.Default.CheckCircle else Icons.Default.Warning,
                            contentDescription = "Status",
                            modifier = Modifier.size(16.dp),
                            tint = Color.White
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = if (scanResults?.vulnerabilities?.isEmpty() != false) "Secure" else "At Risk",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Network Stats
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    NetworkStatItem(
                        icon = Icons.Default.Devices,
                        label = "Connected Devices",
                        value = "${scanResults?.devices?.size ?: 0}",
                        color = Color(0xFF3B82F6)
                    )
                }
                item {
                    NetworkStatItem(
                        icon = Icons.Default.Security,
                        label = "Security Issues",
                        value = "${scanResults?.vulnerabilities?.size ?: 0}",
                        color = if ((scanResults?.vulnerabilities?.size ?: 0) > 0) Color(0xFFEF4444) else Color(0xFF10B981)
                    )
                }
                item {
                    NetworkStatItem(
                        icon = Icons.Default.Speed,
                        label = "Network Speed",
                        value = "Good",
                        color = Color(0xFF10B981)
                    )
                }
                item {
                    NetworkStatItem(
                        icon = Icons.Default.Router,
                        label = "Router Status",
                        value = "Online",
                        color = Color(0xFF10B981)
                    )
                }
            }
        }
    }
}

@Composable
fun NetworkStatItem(
    icon: ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Card(
        modifier = Modifier
            .width(120.dp)
            .height(90.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Column {
                Text(
                    text = value,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
                Text(
                    text = label,
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 2
                )
            }
        }
    }
}

@Composable
fun QuickActionsGrid(
    onStartScan: () -> Unit,
    isScanning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Quick Actions",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                QuickActionButton(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.Search,
                    title = "Device Scan",
                    subtitle = "Find devices",
                    onClick = onStartScan,
                    isLoading = isScanning,
                    color = Color(0xFF3B82F6)
                )
                
                QuickActionButton(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.Security,
                    title = "Security Check",
                    subtitle = "Vulnerability scan",
                    onClick = { /* TODO */ },
                    color = Color(0xFFEF4444)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                QuickActionButton(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.Speed,
                    title = "Speed Test",
                    subtitle = "Check performance",
                    onClick = { /* TODO */ },
                    color = Color(0xFF10B981)
                )
                
                QuickActionButton(
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.Settings,
                    title = "Configure",
                    subtitle = "Network settings",
                    onClick = { /* TODO */ },
                    color = Color(0xFF8B5CF6)
                )
            }
        }
    }
}

@Composable
fun QuickActionButton(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    isLoading: Boolean = false,
    color: Color
) {
    Card(
        modifier = modifier.height(80.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = color
                )
            } else {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = color,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = title,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = color
                    )
                    Text(
                        text = subtitle,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun SecurityMetricsCard(scanResults: com.zilal.networkguardian.model.ScanResult?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Security Metrics",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (scanResults?.vulnerabilities?.isEmpty() != false) {
                // No vulnerabilities found
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF10B981).copy(alpha = 0.1f)
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Secure",
                            tint = Color(0xFF10B981),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "Network is Secure",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF10B981)
                            )
                            Text(
                                text = "No security vulnerabilities detected",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
            } else {
                // Show vulnerabilities
                scanResults.vulnerabilities.take(3).forEach { vulnerability ->
                    SecurityIssueItem(vulnerability = vulnerability)
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                if (scanResults.vulnerabilities.size > 3) {
                    TextButton(
                        onClick = { /* TODO: Navigate to full security report */ }
                    ) {
                        Text("View all ${scanResults.vulnerabilities.size} issues")
                    }
                }
            }
        }
    }
}

@Composable
fun SecurityIssueItem(vulnerability: com.zilal.networkguardian.model.Vulnerability) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (vulnerability.severity) {
                com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Color(0xFFEF4444).copy(alpha = 0.1f)
                com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Color(0xFFF59E0B).copy(alpha = 0.1f)
                else -> Color(0xFF3B82F6).copy(alpha = 0.1f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (vulnerability.severity) {
                    com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Icons.Default.Error
                    com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Icons.Default.Warning
                    else -> Icons.Default.Info
                },
                contentDescription = "Severity",
                modifier = Modifier.size(20.dp),
                tint = when (vulnerability.severity) {
                    com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Color(0xFFEF4444)
                    com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Color(0xFFF59E0B)
                    else -> Color(0xFF3B82F6)
                }
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = vulnerability.name,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = vulnerability.description,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                    maxLines = 1
                )
            }
            
            Text(
                text = vulnerability.severity.name,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = when (vulnerability.severity) {
                    com.zilal.networkguardian.model.Vulnerability.Severity.HIGH -> Color(0xFFEF4444)
                    com.zilal.networkguardian.model.Vulnerability.Severity.MEDIUM -> Color(0xFFF59E0B)
                    else -> Color(0xFF3B82F6)
                }
            )
        }
    }
}

@Composable
fun RecentActivityCard(scanResults: com.zilal.networkguardian.model.ScanResult?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Recent Activity",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                TextButton(onClick = { /* TODO: View all activity */ }) {
                    Text("View All")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (scanResults != null) {
                ActivityItem(
                    icon = Icons.Default.Search,
                    title = "Network Scan Completed",
                    subtitle = "Found ${scanResults.devices.size} devices",
                    time = "Just now",
                    color = Color(0xFF3B82F6)
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No recent activity. Start a scan to see results.",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun ActivityItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    time: String,
    color: Color
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Card(
            modifier = Modifier.size(40.dp),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = color.copy(alpha = 0.1f)
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = color,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        Text(
            text = time,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        )
    }
}

@Composable
fun NetworkHealthCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Network Health Score",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Health Score Circle
                Card(
                    modifier = Modifier.size(80.dp),
                    shape = RoundedCornerShape(40.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF10B981).copy(alpha = 0.1f)
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "95",
                                fontSize = 24.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF10B981)
                            )
                            Text(
                                text = "Score",
                                fontSize = 10.sp,
                                color = Color(0xFF10B981)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column {
                    Text(
                        text = "Excellent",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF10B981)
                    )
                    Text(
                        text = "Your network is well protected",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "Last updated: Just now",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}
