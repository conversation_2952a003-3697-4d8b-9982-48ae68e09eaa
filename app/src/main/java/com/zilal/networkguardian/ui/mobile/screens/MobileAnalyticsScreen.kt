package com.zilal.networkguardian.ui.mobile.screens

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import kotlin.math.sin
import kotlin.random.Random

/**
 * Mobile analytics screen with real-time network monitoring
 */
@Composable
fun MobileAnalyticsScreen(
    modifier: Modifier = Modifier
) {
    var selectedTimeRange by remember { mutableStateOf(TimeRange.HOUR) }
    var networkData by remember { mutableStateOf(generateNetworkData()) }
    var securityEvents by remember { mutableStateOf(generateSecurityEvents()) }
    var performanceMetrics by remember { mutableStateOf(generatePerformanceMetrics()) }
    
    // Simulate real-time data updates
    LaunchedEffect(selectedTimeRange) {
        while (true) {
            networkData = generateNetworkData()
            securityEvents = generateSecurityEvents()
            performanceMetrics = generatePerformanceMetrics()
            delay(3000) // Update every 3 seconds
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Header
            MobileAnalyticsHeader()
        }
        
        item {
            // Time Range Selector
            MobileTimeRangeSelector(
                selectedRange = selectedTimeRange,
                onRangeSelected = { selectedTimeRange = it }
            )
        }
        
        item {
            // Real-time Network Chart
            MobileNetworkChart(
                data = networkData,
                timeRange = selectedTimeRange
            )
        }
        
        item {
            // Performance Metrics
            MobilePerformanceMetrics(performanceMetrics)
        }
        
        item {
            // Security Events
            MobileSecurityEvents(securityEvents)
        }
        
        item {
            // Network Usage Breakdown
            MobileNetworkBreakdown()
        }
        
        item {
            // AI Insights
            MobileAnalyticsInsights()
        }
        
        // Bottom padding
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
private fun MobileAnalyticsHeader() {
    Column {
        Text(
            text = "Network Analytics",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = "Real-time network monitoring and insights",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun MobileTimeRangeSelector(
    selectedRange: TimeRange,
    onRangeSelected: (TimeRange) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(TimeRange.values()) { range ->
            FilterChip(
                onClick = { onRangeSelected(range) },
                label = { Text(range.displayName) },
                selected = selectedRange == range
            )
        }
    }
}

@Composable
private fun MobileNetworkChart(
    data: List<Float>,
    timeRange: TimeRange
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Network Traffic",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${data.lastOrNull()?.toInt() ?: 0} Mbps",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Chart
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp)
            ) {
                drawNetworkChart(data, MaterialTheme.colorScheme.primary)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Chart Legend
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Now - ${timeRange.duration}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
                Text(
                    text = "Now",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

@Composable
private fun MobilePerformanceMetrics(metrics: PerformanceMetrics) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Performance Metrics",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(
                    listOf(
                        MetricCard("Latency", "${metrics.latency}ms", Icons.Default.Speed, 
                                 if (metrics.latency < 50) Color(0xFF4CAF50) else Color(0xFFFF9800)),
                        MetricCard("Packet Loss", "${metrics.packetLoss}%", Icons.Default.Error,
                                 if (metrics.packetLoss < 1) Color(0xFF4CAF50) else Color(0xFFF44336)),
                        MetricCard("Bandwidth", "${metrics.bandwidth} Mbps", Icons.Default.NetworkCheck,
                                 MaterialTheme.colorScheme.primary),
                        MetricCard("Uptime", "${metrics.uptime}%", Icons.Default.Timer,
                                 if (metrics.uptime > 99) Color(0xFF4CAF50) else Color(0xFFFF9800))
                    )
                ) { metric ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.width(80.dp)
                    ) {
                        Icon(
                            imageVector = metric.icon,
                            contentDescription = metric.title,
                            tint = metric.color,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = metric.value,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = metric.color
                        )
                        Text(
                            text = metric.title,
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileSecurityEvents(events: List<SecurityEvent>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Security Events",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${events.size} events",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (events.isEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = "Secure",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "No security events detected",
                        fontSize = 14.sp,
                        color = Color(0xFF4CAF50)
                    )
                }
            } else {
                events.take(3).forEach { event ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = when (event.severity) {
                                "high" -> Icons.Default.Error
                                "medium" -> Icons.Default.Warning
                                else -> Icons.Default.Info
                            },
                            contentDescription = event.severity,
                            tint = when (event.severity) {
                                "high" -> MaterialTheme.colorScheme.error
                                "medium" -> Color(0xFFFF9800)
                                else -> MaterialTheme.colorScheme.primary
                            },
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = event.title,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = event.timestamp,
                                fontSize = 10.sp,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileNetworkBreakdown() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Device Types",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            val deviceTypes = listOf(
                DeviceTypeData("Computers", 8, Color(0xFF2196F3)),
                DeviceTypeData("Mobile", 12, Color(0xFF4CAF50)),
                DeviceTypeData("IoT", 6, Color(0xFFFF9800)),
                DeviceTypeData("Network", 3, Color(0xFF9C27B0))
            )
            
            deviceTypes.forEach { deviceType ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(deviceType.color, CircleShape)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = deviceType.name,
                        fontSize = 14.sp,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = "${deviceType.count}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = deviceType.color
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileAnalyticsInsights() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = "AI",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI Insights",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Network traffic is 15% higher than usual. Peak usage detected between 8-10 PM. Consider upgrading bandwidth for optimal performance.",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
            )
        }
    }
}

// Helper function to draw network chart
private fun DrawScope.drawNetworkChart(data: List<Float>, color: Color) {
    if (data.size < 2) return
    
    val maxValue = data.maxOrNull() ?: 1f
    val minValue = data.minOrNull() ?: 0f
    val range = maxValue - minValue
    
    val path = Path()
    val stepX = size.width / (data.size - 1)
    
    data.forEachIndexed { index, value ->
        val x = index * stepX
        val y = size.height - ((value - minValue) / range) * size.height
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = 3.dp.toPx())
    )
    
    // Draw data points
    data.forEachIndexed { index, value ->
        val x = index * stepX
        val y = size.height - ((value - minValue) / range) * size.height
        
        drawCircle(
            color = color,
            radius = 4.dp.toPx(),
            center = Offset(x, y)
        )
    }
}

// Data classes
enum class TimeRange(val displayName: String, val duration: String) {
    HOUR("1H", "1 hour"),
    DAY("24H", "24 hours"),
    WEEK("7D", "7 days"),
    MONTH("30D", "30 days")
}

data class PerformanceMetrics(
    val latency: Int = (10..100).random(),
    val packetLoss: Float = (0f..2f).random(),
    val bandwidth: Int = (50..200).random(),
    val uptime: Float = (98f..100f).random()
)

data class MetricCard(
    val title: String,
    val value: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val color: Color
)

data class SecurityEvent(
    val title: String,
    val severity: String,
    val timestamp: String
)

data class DeviceTypeData(
    val name: String,
    val count: Int,
    val color: Color
)

// Sample data generators
private fun generateNetworkData(): List<Float> {
    return (0..20).map { 
        50f + sin(it * 0.3) * 30f + Random.nextFloat() * 20f
    }
}

private fun generateSecurityEvents(): List<SecurityEvent> {
    val events = listOf(
        SecurityEvent("Port scan detected", "medium", "2 min ago"),
        SecurityEvent("Unknown device connected", "low", "5 min ago"),
        SecurityEvent("Suspicious traffic pattern", "high", "12 min ago")
    )
    return events.shuffled().take(Random.nextInt(0, 3))
}

private fun generatePerformanceMetrics(): PerformanceMetrics {
    return PerformanceMetrics()
}
