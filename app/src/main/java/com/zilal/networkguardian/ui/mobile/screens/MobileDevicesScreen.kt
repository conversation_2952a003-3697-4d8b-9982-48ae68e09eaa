package com.zilal.networkguardian.ui.mobile.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.network.model.NetworkDevice
import com.zilal.networkguardian.network.viewmodel.NetworkScanViewModel

/**
 * Mobile devices screen showing all discovered network devices
 */
@Composable
fun MobileDevicesScreen(
    modifier: Modifier = Modifier,
    viewModel: NetworkScanViewModel = hiltViewModel()
) {
    val discoveredDevices by viewModel.discoveredDevices.collectAsStateWithLifecycle()
    var selectedFilter by remember { mutableStateOf(DeviceFilter.ALL) }
    var showDeviceDetails by remember { mutableStateOf<NetworkDevice?>(null) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        MobileDevicesHeader(deviceCount = discoveredDevices.size)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Filter Chips
        MobileDeviceFilters(
            selectedFilter = selectedFilter,
            onFilterSelected = { selectedFilter = it },
            devices = discoveredDevices
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Device List
        val filteredDevices = when (selectedFilter) {
            DeviceFilter.ALL -> discoveredDevices
            DeviceFilter.TRUSTED -> discoveredDevices.filter { it.isTrusted }
            DeviceFilter.UNKNOWN -> discoveredDevices.filter { !it.isKnownDevice }
            DeviceFilter.HIGH_RISK -> discoveredDevices.filter { it.securityRisk.lowercase() == "high" }
            DeviceFilter.ROUTERS -> discoveredDevices.filter { it.deviceType.lowercase() == "router" }
            DeviceFilter.COMPUTERS -> discoveredDevices.filter { 
                it.deviceType.lowercase() in listOf("computer", "laptop", "pc") 
            }
            DeviceFilter.MOBILE -> discoveredDevices.filter { 
                it.deviceType.lowercase() in listOf("phone", "mobile", "tablet") 
            }
        }
        
        MobileDeviceList(
            devices = filteredDevices,
            onDeviceClick = { showDeviceDetails = it },
            modifier = Modifier.weight(1f)
        )
    }
    
    // Device Details Bottom Sheet
    showDeviceDetails?.let { device ->
        MobileDeviceDetailsSheet(
            device = device,
            onDismiss = { showDeviceDetails = null }
        )
    }
}

@Composable
private fun MobileDevicesHeader(deviceCount: Int) {
    Column {
        Text(
            text = "Network Devices",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = "$deviceCount devices discovered",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun MobileDeviceFilters(
    selectedFilter: DeviceFilter,
    onFilterSelected: (DeviceFilter) -> Unit,
    devices: List<NetworkDevice>
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(DeviceFilter.values()) { filter ->
            val count = when (filter) {
                DeviceFilter.ALL -> devices.size
                DeviceFilter.TRUSTED -> devices.count { it.isTrusted }
                DeviceFilter.UNKNOWN -> devices.count { !it.isKnownDevice }
                DeviceFilter.HIGH_RISK -> devices.count { it.securityRisk.lowercase() == "high" }
                DeviceFilter.ROUTERS -> devices.count { it.deviceType.lowercase() == "router" }
                DeviceFilter.COMPUTERS -> devices.count { 
                    it.deviceType.lowercase() in listOf("computer", "laptop", "pc") 
                }
                DeviceFilter.MOBILE -> devices.count { 
                    it.deviceType.lowercase() in listOf("phone", "mobile", "tablet") 
                }
            }
            
            FilterChip(
                onClick = { onFilterSelected(filter) },
                label = { 
                    Text("${filter.displayName} ($count)")
                },
                selected = selectedFilter == filter,
                leadingIcon = {
                    Icon(
                        imageVector = filter.icon,
                        contentDescription = filter.displayName,
                        modifier = Modifier.size(16.dp)
                    )
                },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun MobileDeviceList(
    devices: List<NetworkDevice>,
    onDeviceClick: (NetworkDevice) -> Unit,
    modifier: Modifier = Modifier
) {
    if (devices.isEmpty()) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.DeviceHub,
                    contentDescription = "No devices",
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "No devices match the filter",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
        }
    } else {
        LazyColumn(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(devices) { device ->
                MobileDeviceCard(
                    device = device,
                    onClick = { onDeviceClick(device) }
                )
            }
        }
    }
}

@Composable
private fun MobileDeviceCard(
    device: NetworkDevice,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Device Icon
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = when (device.deviceType.lowercase()) {
                        "router" -> Icons.Default.Router
                        "phone", "mobile" -> Icons.Default.PhoneAndroid
                        "computer", "laptop", "pc" -> Icons.Default.Computer
                        "tablet" -> Icons.Default.Tablet
                        "tv" -> Icons.Default.Tv
                        "printer" -> Icons.Default.Print
                        "camera" -> Icons.Default.Camera
                        "speaker" -> Icons.Default.Speaker
                        else -> Icons.Default.DeviceHub
                    },
                    contentDescription = device.deviceType,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(28.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Device Info
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.deviceName ?: "Unknown Device",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = device.ipAddress,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = device.deviceType.replaceFirstChar { it.uppercase() },
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                    if (device.manufacturer.isNotEmpty()) {
                        Text(
                            text = " • ${device.manufacturer}",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                        )
                    }
                }
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                // Security Status
                Icon(
                    imageVector = when (device.securityRisk.lowercase()) {
                        "high" -> Icons.Default.Error
                        "medium" -> Icons.Default.Warning
                        else -> Icons.Default.CheckCircle
                    },
                    contentDescription = device.securityRisk,
                    tint = when (device.securityRisk.lowercase()) {
                        "high" -> MaterialTheme.colorScheme.error
                        "medium" -> Color(0xFFFF9800)
                        else -> Color(0xFF4CAF50)
                    },
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // Trust Status
                if (device.isTrusted) {
                    Icon(
                        imageVector = Icons.Default.Verified,
                        contentDescription = "Trusted",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(16.dp)
                    )
                } else if (!device.isKnownDevice) {
                    Icon(
                        imageVector = Icons.Default.Help,
                        contentDescription = "Unknown",
                        tint = Color(0xFFFF9800),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MobileDeviceDetailsSheet(
    device: NetworkDevice,
    onDismiss: () -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState()
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (device.deviceType.lowercase()) {
                        "router" -> Icons.Default.Router
                        "phone", "mobile" -> Icons.Default.PhoneAndroid
                        "computer", "laptop", "pc" -> Icons.Default.Computer
                        "tablet" -> Icons.Default.Tablet
                        "tv" -> Icons.Default.Tv
                        "printer" -> Icons.Default.Print
                        else -> Icons.Default.DeviceHub
                    },
                    contentDescription = device.deviceType,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )
                Spacer(modifier = Modifier.width(16.dp))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = device.deviceName ?: "Unknown Device",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = device.deviceType.replaceFirstChar { it.uppercase() },
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Device Details
            DeviceDetailItem("IP Address", device.ipAddress)
            if (device.macAddress.isNotEmpty()) {
                DeviceDetailItem("MAC Address", device.macAddress)
            }
            if (device.manufacturer.isNotEmpty()) {
                DeviceDetailItem("Manufacturer", device.manufacturer)
            }
            DeviceDetailItem("Security Risk", device.securityRisk.replaceFirstChar { it.uppercase() })
            DeviceDetailItem("Known Device", if (device.isKnownDevice) "Yes" else "No")
            DeviceDetailItem("Trusted", if (device.isTrusted) "Yes" else "No")
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { /* TODO: Mark as trusted */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Verified, contentDescription = null, modifier = Modifier.size(16.dp))
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Trust")
                }
                
                Button(
                    onClick = { /* TODO: Scan device */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Radar, contentDescription = null, modifier = Modifier.size(16.dp))
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Scan")
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
private fun DeviceDetailItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

// Device filter enum
enum class DeviceFilter(
    val displayName: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    ALL("All Devices", Icons.Default.DeviceHub),
    TRUSTED("Trusted", Icons.Default.Verified),
    UNKNOWN("Unknown", Icons.Default.Help),
    HIGH_RISK("High Risk", Icons.Default.Error),
    ROUTERS("Routers", Icons.Default.Router),
    COMPUTERS("Computers", Icons.Default.Computer),
    MOBILE("Mobile", Icons.Default.PhoneAndroid)
}
