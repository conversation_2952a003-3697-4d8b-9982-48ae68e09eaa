package com.zilal.networkguardian.ui.mobile

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.zilal.networkguardian.auth.repository.AuthState
import com.zilal.networkguardian.auth.viewmodel.AuthViewModel
import com.zilal.networkguardian.ui.mobile.theme.NetworkGuardianTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Mobile-optimized authentication activity
 * Designed for perfect mobile experience without scrolling
 */
@AndroidEntryPoint
class MobileAuthActivity : ComponentActivity() {
    
    private val authViewModel: AuthViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        setContent {
            NetworkGuardianTheme {
                MobileAuthContainer()
            }
        }
    }
    
    @Composable
    private fun MobileAuthContainer() {
        val authState by authViewModel.authState.collectAsStateWithLifecycle()

        // Navigate to main app when authenticated
        LaunchedEffect(authState) {
            if (authState is AuthState.Authenticated) {
                startActivity(Intent(this@MobileAuthActivity, MobileMainActivity::class.java))
                finish()
            }
        }

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.systemBars)
            ) {
                // Simple mobile auth screen
                SimpleMobileAuthScreen(
                    authViewModel = authViewModel,
                    modifier = Modifier.fillMaxSize()
                )

                // Temp Access Button (for testing)
                TempAccessButton(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(16.dp)
                )
            }
        }
    }
    
    @Composable
    private fun TempAccessButton(modifier: Modifier = Modifier) {
        var showDialog by remember { mutableStateOf(false) }
        
        FloatingActionButton(
            onClick = { showDialog = true },
            modifier = modifier.size(48.dp),
            containerColor = MaterialTheme.colorScheme.secondary
        ) {
            Text("T", color = MaterialTheme.colorScheme.onSecondary)
        }
        
        if (showDialog) {
            AlertDialog(
                onDismissRequest = { showDialog = false },
                title = { Text("Temp Access") },
                text = { Text("Skip authentication for testing?") },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showDialog = false
                            // Skip to main app
                            startActivity(Intent(this@MobileAuthActivity, MobileMainActivity::class.java))
                            finish()
                        }
                    ) {
                        Text("Yes, Skip")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showDialog = false }) {
                        Text("Cancel")
                    }
                }
            )
        }
    }

    @Composable
    private fun SimpleMobileAuthScreen(
        authViewModel: AuthViewModel,
        modifier: Modifier = Modifier
    ) {
        var email by remember { mutableStateOf("<EMAIL>") }
        var password by remember { mutableStateOf("testpass123") }
        val authState by authViewModel.authState.collectAsStateWithLifecycle()

        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Logo
            Card(
                modifier = Modifier.size(80.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = "Network Guardian",
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Network Guardian",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = "Premium Network Security",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Email Field
            OutlinedTextField(
                value = email,
                onValueChange = { email = it },
                label = { Text("Email") },
                leadingIcon = { Icon(Icons.Default.Email, contentDescription = null) },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Password Field
            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("Password") },
                leadingIcon = { Icon(Icons.Default.Lock, contentDescription = null) },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Login Button
            Button(
                onClick = { authViewModel.login(email, password) },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                enabled = authState !is AuthState.Loading
            ) {
                if (authState is AuthState.Loading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text("Sign In", fontSize = 16.sp, fontWeight = FontWeight.Medium)
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Error Message
            val currentAuthState = authState
            if (currentAuthState is AuthState.Error) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = currentAuthState.message,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.padding(12.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            Text(
                text = "Secure • Private • Intelligent",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f)
            )
        }
    }
}
