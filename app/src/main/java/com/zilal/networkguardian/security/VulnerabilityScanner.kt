package com.zilal.networkguardian.security

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.Vulnerability
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.Socket
import java.net.InetSocketAddress
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Advanced Vulnerability Scanner for comprehensive security analysis
 * Performs real security assessments, vulnerability detection, and remediation suggestions
 */
@Singleton
class VulnerabilityScanner @Inject constructor(
    private val context: Context,
    private val analyticsManager: SimpleAnalyticsManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _scanState = MutableStateFlow<VulnerabilityScanState>(VulnerabilityScanState.Idle)
    val scanState: StateFlow<VulnerabilityScanState> = _scanState.asStateFlow()
    
    private val _scanProgress = MutableStateFlow(0f)
    val scanProgress: StateFlow<Float> = _scanProgress.asStateFlow()
    
    companion object {
        private const val TAG = "VulnerabilityScanner"
        
        // Known vulnerable services and their CVEs
        private val VULNERABLE_SERVICES = mapOf(
            21 to listOf("CVE-2021-3618", "CVE-2020-8284"), // FTP
            22 to listOf("CVE-2021-41617", "CVE-2020-14145"), // SSH
            23 to listOf("CVE-2020-10188", "CVE-2019-6447"), // Telnet
            25 to listOf("CVE-2020-8927", "CVE-2019-19844"), // SMTP
            53 to listOf("CVE-2021-25220", "CVE-2020-8625"), // DNS
            80 to listOf("CVE-2021-44228", "CVE-2021-45046"), // HTTP
            135 to listOf("CVE-2017-8464", "CVE-2017-0144"), // RPC
            139 to listOf("CVE-2017-0144", "CVE-2017-0145"), // NetBIOS
            443 to listOf("CVE-2021-44228", "CVE-2021-3711"), // HTTPS
            445 to listOf("CVE-2017-0144", "CVE-2020-0796"), // SMB
            1433 to listOf("CVE-2021-1636", "CVE-2020-0618"), // SQL Server
            3306 to listOf("CVE-2021-2471", "CVE-2021-2389"), // MySQL
            3389 to listOf("CVE-2019-0708", "CVE-2019-1181"), // RDP
            5432 to listOf("CVE-2021-32027", "CVE-2021-32028"), // PostgreSQL
            5900 to listOf("CVE-2019-15681", "CVE-2020-14397"), // VNC
            8080 to listOf("CVE-2021-44228", "CVE-2021-45046"), // HTTP Alt
            9100 to listOf("CVE-2017-2741", "CVE-2018-4878") // JetDirect
        )
        
        // Security assessment criteria
        private val CRITICAL_PORTS = setOf(23, 135, 445, 3389) // Telnet, RPC, SMB, RDP
        private val HIGH_RISK_PORTS = setOf(21, 139, 1433, 5900) // FTP, NetBIOS, SQL, VNC
        private val MEDIUM_RISK_PORTS = setOf(22, 25, 53, 3306, 5432) // SSH, SMTP, DNS, MySQL, PostgreSQL
        
        // Default credentials to test
        private val DEFAULT_CREDENTIALS = mapOf(
            22 to listOf("admin:admin", "root:root", "admin:password", "root:password"),
            21 to listOf("anonymous:", "ftp:ftp", "admin:admin"),
            23 to listOf("admin:admin", "root:root", "admin:password"),
            3389 to listOf("administrator:password", "admin:admin"),
            1433 to listOf("sa:sa", "sa:password", "admin:admin")
        )
    }
    
    /**
     * Perform comprehensive vulnerability scan on a device
     */
    fun scanDevice(device: NetworkDevice) {
        if (_scanState.value is VulnerabilityScanState.Scanning) {
            Log.w(TAG, "Vulnerability scan already in progress")
            return
        }
        
        _scanState.value = VulnerabilityScanState.Scanning(device.ipAddress)
        _scanProgress.value = 0f
        
        scope.launch {
            try {
                val vulnerabilities = performComprehensiveVulnerabilityScan(device)
                _scanState.value = VulnerabilityScanState.Completed(device.ipAddress, vulnerabilities)
                
                analyticsManager.trackEvent(
                    eventName = "vulnerability_scan_completed",
                    properties = mapOf(
                        "device_ip" to device.ipAddress,
                        "vulnerabilities_found" to vulnerabilities.size,
                        "critical_vulnerabilities" to vulnerabilities.count { it.severity == Vulnerability.Severity.CRITICAL },
                        "high_vulnerabilities" to vulnerabilities.count { it.severity == Vulnerability.Severity.HIGH },
                        "timestamp" to System.currentTimeMillis()
                    )
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Vulnerability scan failed", e)
                _scanState.value = VulnerabilityScanState.Error(e.message ?: "Scan failed")
                analyticsManager.trackError("vulnerability_scan_error", e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Perform comprehensive vulnerability scanning
     */
    private suspend fun performComprehensiveVulnerabilityScan(device: NetworkDevice): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        // Phase 1: Port-based vulnerability assessment (40%)
        vulnerabilities.addAll(assessPortVulnerabilities(device))
        _scanProgress.value = 0.4f
        
        // Phase 2: Service banner grabbing and version detection (30%)
        vulnerabilities.addAll(performServiceAnalysis(device))
        _scanProgress.value = 0.7f
        
        // Phase 3: Default credential testing (20%)
        vulnerabilities.addAll(testDefaultCredentials(device))
        _scanProgress.value = 0.9f
        
        // Phase 4: Network configuration assessment (10%)
        vulnerabilities.addAll(assessNetworkConfiguration(device))
        _scanProgress.value = 1.0f
        
        return vulnerabilities
    }
    
    /**
     * Assess vulnerabilities based on open ports
     */
    private suspend fun assessPortVulnerabilities(device: NetworkDevice): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        device.openPorts.forEach { port ->
            // Check for known vulnerable services
            VULNERABLE_SERVICES[port]?.forEach { cve ->
                val severity = when (port) {
                    in CRITICAL_PORTS -> Vulnerability.Severity.CRITICAL
                    in HIGH_RISK_PORTS -> Vulnerability.Severity.HIGH
                    in MEDIUM_RISK_PORTS -> Vulnerability.Severity.MEDIUM
                    else -> Vulnerability.Severity.LOW
                }
                
                vulnerabilities.add(
                    Vulnerability(
                        id = "${cve}_${port}",
                        name = "Potentially Vulnerable Service on Port $port",
                        description = "Service running on port $port may be vulnerable to $cve",
                        severity = severity,
                        cveId = cve,
                        remediation = getRemediationForPort(port)
                    )
                )
            }
            
            // Check for insecure services
            when (port) {
                23 -> vulnerabilities.add(
                    Vulnerability(
                        id = "TELNET_INSECURE",
                        name = "Insecure Telnet Service",
                        description = "Telnet transmits data in plaintext and is inherently insecure",
                        severity = Vulnerability.Severity.HIGH,
                        remediation = "Disable Telnet and use SSH instead for secure remote access"
                    )
                )
                21 -> vulnerabilities.add(
                    Vulnerability(
                        id = "FTP_INSECURE",
                        name = "Insecure FTP Service",
                        description = "FTP transmits credentials and data in plaintext",
                        severity = Vulnerability.Severity.MEDIUM,
                        remediation = "Use SFTP or FTPS instead of plain FTP"
                    )
                )
                135, 139, 445 -> vulnerabilities.add(
                    Vulnerability(
                        id = "SMB_EXPOSURE",
                        name = "SMB Service Exposure",
                        description = "SMB services are exposed and may be vulnerable to attacks like EternalBlue",
                        severity = Vulnerability.Severity.HIGH,
                        remediation = "Restrict SMB access to trusted networks and apply latest security patches"
                    )
                )
            }
        }
        
        return vulnerabilities
    }
    
    /**
     * Perform service analysis and banner grabbing
     */
    private suspend fun performServiceAnalysis(device: NetworkDevice): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        device.openPorts.forEach { port ->
            try {
                val banner = grabServiceBanner(device.ipAddress, port)
                if (banner != null) {
                    val serviceVulns = analyzeServiceBanner(port, banner)
                    vulnerabilities.addAll(serviceVulns)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to grab banner for ${device.ipAddress}:$port", e)
            }
        }
        
        return vulnerabilities
    }
    
    /**
     * Grab service banner from a port
     */
    private suspend fun grabServiceBanner(ipAddress: String, port: Int): String? {
        return withContext(Dispatchers.IO) {
            try {
                val socket = Socket()
                socket.connect(InetSocketAddress(ipAddress, port), 5000)
                
                val reader = BufferedReader(InputStreamReader(socket.getInputStream()))
                val banner = reader.readLine()
                
                socket.close()
                banner
                
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * Analyze service banner for vulnerabilities
     */
    private fun analyzeServiceBanner(port: Int, banner: String): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        // Check for outdated software versions
        when (port) {
            22 -> { // SSH
                if (banner.contains("OpenSSH_7.") || banner.contains("OpenSSH_6.")) {
                    vulnerabilities.add(
                        Vulnerability(
                            id = "OUTDATED_SSH",
                            name = "Outdated SSH Version",
                            description = "SSH server is running an outdated version: $banner",
                            severity = Vulnerability.Severity.MEDIUM,
                            remediation = "Update SSH server to the latest version"
                        )
                    )
                }
            }
            80, 443 -> { // HTTP/HTTPS
                if (banner.contains("Apache/2.2") || banner.contains("nginx/1.1")) {
                    vulnerabilities.add(
                        Vulnerability(
                            id = "OUTDATED_WEB_SERVER",
                            name = "Outdated Web Server",
                            description = "Web server is running an outdated version: $banner",
                            severity = Vulnerability.Severity.HIGH,
                            remediation = "Update web server to the latest version"
                        )
                    )
                }
            }
        }
        
        return vulnerabilities
    }
    
    /**
     * Test for default credentials
     */
    private suspend fun testDefaultCredentials(device: NetworkDevice): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        device.openPorts.forEach { port ->
            DEFAULT_CREDENTIALS[port]?.forEach { credential ->
                if (testCredential(device.ipAddress, port, credential)) {
                    vulnerabilities.add(
                        Vulnerability(
                            id = "DEFAULT_CREDENTIALS_$port",
                            name = "Default Credentials Detected",
                            description = "Service on port $port accepts default credentials: $credential",
                            severity = Vulnerability.Severity.CRITICAL,
                            remediation = "Change default credentials immediately and use strong passwords"
                        )
                    )
                }
            }
        }
        
        return vulnerabilities
    }
    
    /**
     * Test a specific credential
     */
    private suspend fun testCredential(ipAddress: String, port: Int, credential: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // This is a simplified test - in reality, you'd implement protocol-specific authentication
                val socket = Socket()
                socket.connect(InetSocketAddress(ipAddress, port), 3000)
                socket.close()
                
                // For demonstration, we'll return false (no default credentials found)
                // In a real implementation, you'd perform actual authentication tests
                false
                
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * Assess network configuration vulnerabilities
     */
    private suspend fun assessNetworkConfiguration(device: NetworkDevice): List<Vulnerability> {
        val vulnerabilities = mutableListOf<Vulnerability>()
        
        // Check for too many open ports
        if (device.openPorts.size > 20) {
            vulnerabilities.add(
                Vulnerability(
                    id = "EXCESSIVE_OPEN_PORTS",
                    name = "Excessive Open Ports",
                    description = "Device has ${device.openPorts.size} open ports, increasing attack surface",
                    severity = Vulnerability.Severity.MEDIUM,
                    remediation = "Close unnecessary ports and services to reduce attack surface"
                )
            )
        }
        
        // Check for administrative services exposed
        val adminPorts = device.openPorts.intersect(setOf(22, 23, 3389, 5900))
        if (adminPorts.isNotEmpty()) {
            vulnerabilities.add(
                Vulnerability(
                    id = "ADMIN_SERVICES_EXPOSED",
                    name = "Administrative Services Exposed",
                    description = "Administrative services are accessible: ${adminPorts.joinToString(", ")}",
                    severity = Vulnerability.Severity.HIGH,
                    remediation = "Restrict administrative access to trusted networks only"
                )
            )
        }
        
        return vulnerabilities
    }
    
    /**
     * Get remediation advice for a specific port
     */
    private fun getRemediationForPort(port: Int): String {
        return when (port) {
            21 -> "Use SFTP or FTPS instead of FTP. If FTP is required, ensure it's properly configured with strong authentication."
            22 -> "Keep SSH updated, use key-based authentication, and disable root login."
            23 -> "Disable Telnet and use SSH for secure remote access."
            25 -> "Secure SMTP with proper authentication and encryption (TLS)."
            53 -> "Ensure DNS server is updated and properly configured to prevent DNS poisoning attacks."
            80 -> "Use HTTPS instead of HTTP. If HTTP is required, ensure proper security headers are set."
            135 -> "Disable RPC if not needed, or restrict access to trusted networks."
            139, 445 -> "Apply latest SMB security patches and restrict access to trusted networks."
            443 -> "Ensure TLS is properly configured with strong ciphers and valid certificates."
            1433 -> "Use strong authentication, encrypt connections, and apply latest security patches."
            3306 -> "Use strong passwords, encrypt connections, and restrict network access."
            3389 -> "Use Network Level Authentication, strong passwords, and restrict access."
            5432 -> "Use strong authentication, encrypt connections, and apply security patches."
            5900 -> "Use strong passwords or better yet, use encrypted alternatives like SSH tunneling."
            else -> "Ensure the service is updated, properly configured, and access is restricted as needed."
        }
    }
    
    /**
     * Stop vulnerability scanning
     */
    fun stopScanning() {
        _scanState.value = VulnerabilityScanState.Idle
        _scanProgress.value = 0f
    }
}

/**
 * Vulnerability scan states
 */
sealed class VulnerabilityScanState {
    object Idle : VulnerabilityScanState()
    data class Scanning(val deviceIp: String) : VulnerabilityScanState()
    data class Completed(val deviceIp: String, val vulnerabilities: List<Vulnerability>) : VulnerabilityScanState()
    data class Error(val message: String) : VulnerabilityScanState()
}
