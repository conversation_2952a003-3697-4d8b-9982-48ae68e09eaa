package com.zilal.networkguardian.model

import java.net.InetAddress

/**
 * Represents a device discovered on the network
 */
data class NetworkDevice(
    val id: String = java.util.UUID.randomUUID().toString(),
    val ipAddress: String,
    var macAddress: String? = null,
    var hostname: String? = null,
    var manufacturer: String? = null,
    var model: String? = null,
    var deviceType: DeviceType? = null,
    var operatingSystem: String? = null,
    var notes: String? = null,
    val isOnline: Boolean = true,
    val openPorts: MutableList<Int> = mutableListOf(),
    val services: MutableMap<Int, String> = mutableMapOf(),
    var lastSeen: Long = System.currentTimeMillis(),
    val vulnerabilities: List<Vulnerability> = emptyList(),
    val tags: MutableList<String> = mutableListOf(),
    var detectionMethod: String? = null,
    var signalStrength: Int? = null,
    var firstSeen: Long = System.currentTimeMillis(),
    var isNewDevice: Boolean = false,
    var isTrusted: Boolean = false,
    var securityRisk: SecurityRisk = SecurityRisk.UNKNOWN,
    var verificationStatus: DeviceVerificationStatus = DeviceVerificationStatus.UNKNOWN,
    var isBlocked: Boolean = false,
    var verificationTime: Long? = null,
    var blockTime: Long? = null,
    var isGateway: Boolean = false,
    var customName: String? = null,
    var trafficData: NetworkTraffic? = null
) {
    /**
     * Returns true if the device has any detected vulnerabilities
     */
    fun hasVulnerabilities(): Boolean = vulnerabilities.isNotEmpty()

    /**
     * Returns the number of high severity vulnerabilities
     */
    fun highSeverityVulnerabilityCount(): Int =
        vulnerabilities.count { it.severity == Vulnerability.Severity.HIGH }

    /**
     * Returns a formatted string of the device's hostname or IP address
     */
    fun displayName(): String = hostname ?: ipAddress

    /**
     * Returns true if this device is likely the current device
     */
    fun isLocalDevice(): Boolean {
        val localAddresses = listOf("127.0.0.1", "localhost", "::1")
        return ipAddress in localAddresses ||
               hostname?.lowercase()?.contains("localhost") == true
    }

    /**
     * Returns a formatted string of the device's MAC address
     */
    fun formattedMacAddress(): String = macAddress ?: "Unknown"

    /**
     * Returns a formatted string of the device's manufacturer
     */
    fun formattedManufacturer(): String = manufacturer ?: "Unknown"

    /**
     * Returns a formatted string of the device's detection method
     */
    fun formattedDetectionMethod(): String = detectionMethod ?: "Unknown"

    /**
     * Returns a formatted string of the device's signal strength
     */
    fun formattedSignalStrength(): String {
        return when (signalStrength) {
            null -> "Unknown"
            in 0..1 -> "Poor"
            in 2..3 -> "Good"
            else -> "Excellent"
        }
    }

    /**
     * Returns a formatted string of the device's security risk
     */
    fun formattedSecurityRisk(): String {
        return when (securityRisk) {
            SecurityRisk.NONE -> "None"
            SecurityRisk.LOW -> "Low"
            SecurityRisk.MEDIUM -> "Medium"
            SecurityRisk.HIGH -> "High"
            SecurityRisk.CRITICAL -> "Critical"
            SecurityRisk.UNKNOWN -> "Unknown"
        }
    }

    /**
     * Returns a formatted string of the device's open ports
     */
    fun formattedOpenPorts(): String {
        return if (openPorts.isEmpty()) {
            "None"
        } else {
            openPorts.sorted().joinToString(", ")
        }
    }

    /**
     * Returns a formatted string of the device's services
     */
    fun formattedServices(): String {
        return if (services.isEmpty()) {
            "None"
        } else {
            services.entries.joinToString(", ") { "${it.key}: ${it.value}" }
        }
    }

    /**
     * Returns a formatted string of the device's time on network
     */
    fun formattedTimeOnNetwork(): String {
        val now = System.currentTimeMillis()
        val timeOnNetwork = now - firstSeen

        return when {
            timeOnNetwork < 60_000 -> "Just now"
            timeOnNetwork < 3_600_000 -> "${timeOnNetwork / 60_000} minutes"
            timeOnNetwork < 86_400_000 -> "${timeOnNetwork / 3_600_000} hours"
            else -> "${timeOnNetwork / 86_400_000} days"
        }
    }

    /**
     * Returns a formatted string of the device's last seen time
     */
    fun formattedLastSeen(): String {
        val now = System.currentTimeMillis()
        val timeSinceLastSeen = now - lastSeen

        return when {
            timeSinceLastSeen < 60_000 -> "Just now"
            timeSinceLastSeen < 3_600_000 -> "${timeSinceLastSeen / 60_000} minutes ago"
            timeSinceLastSeen < 86_400_000 -> "${timeSinceLastSeen / 3_600_000} hours ago"
            else -> "${timeSinceLastSeen / 86_400_000} days ago"
        }
    }

    /**
     * Calculate security risk based on open ports and vulnerabilities
     */
    fun calculateSecurityRisk(): SecurityRisk {
        // If we have vulnerabilities, use the highest severity
        if (vulnerabilities.isNotEmpty()) {
            val highestSeverity = vulnerabilities.maxByOrNull { it.severity.ordinal }?.severity
            return when (highestSeverity) {
                Vulnerability.Severity.LOW -> SecurityRisk.LOW
                Vulnerability.Severity.MEDIUM -> SecurityRisk.MEDIUM
                Vulnerability.Severity.HIGH -> SecurityRisk.HIGH
                Vulnerability.Severity.CRITICAL -> SecurityRisk.CRITICAL
                else -> SecurityRisk.UNKNOWN
            }
        }

        // Define port risk categories
        val criticalRiskPorts = setOf(23, 135, 445) // Telnet, RPC/DCOM, SMB
        val highRiskPorts = setOf(21, 139, 1433, 3389) // FTP, NetBIOS, MSSQL, RDP
        val mediumRiskPorts = setOf(22, 25, 53, 3306, 5900) // SSH, SMTP, DNS, MySQL, VNC
        val lowRiskPorts = setOf(80, 443, 8080, 8443) // HTTP, HTTPS

        // Check for open ports by risk level
        return when {
            openPorts.any { it in criticalRiskPorts } -> SecurityRisk.CRITICAL
            openPorts.any { it in highRiskPorts } -> SecurityRisk.HIGH
            openPorts.any { it in mediumRiskPorts } -> SecurityRisk.MEDIUM
            openPorts.any { it in lowRiskPorts } -> SecurityRisk.LOW
            openPorts.isNotEmpty() -> SecurityRisk.LOW
            else -> SecurityRisk.NONE
        }
    }

    /**
     * Get a list of security issues for this device
     */
    fun getSecurityIssues(): List<String> {
        val issues = mutableListOf<String>()

        // Check for known vulnerable ports
        if (23 in openPorts) {
            issues.add("Telnet port (23) is open - sends data in plaintext and is considered insecure")
        }
        if (21 in openPorts) {
            issues.add("FTP port (21) is open - may allow anonymous access or have weak authentication")
        }
        if (445 in openPorts) {
            issues.add("SMB port (445) is open - has had critical vulnerabilities like EternalBlue")
        }
        if (135 in openPorts) {
            issues.add("RPC/DCOM port (135) is open - has had critical vulnerabilities")
        }
        if (139 in openPorts) {
            issues.add("NetBIOS port (139) is open - can expose sensitive system information")
        }
        if (3389 in openPorts) {
            issues.add("RDP port (3389) is open - has had critical vulnerabilities like BlueKeep")
        }
        if (1433 in openPorts) {
            issues.add("MS SQL port (1433) is open - may have weak authentication or known vulnerabilities")
        }
        if (3306 in openPorts) {
            issues.add("MySQL port (3306) is open - may have weak authentication or known vulnerabilities")
        }
        if (5900 in openPorts) {
            issues.add("VNC port (5900) is open - may use weak encryption or no encryption")
        }

        // Add vulnerability-based issues
        vulnerabilities.forEach { vulnerability ->
            issues.add("${vulnerability.name}: ${vulnerability.description}")
        }

        return issues
    }

    /**
     * Device type enum
     */
    enum class DeviceType {
        ROUTER,
        SWITCH,
        ACCESS_POINT,
        SERVER,
        COMPUTER,  // Includes desktops and laptops
        MOBILE,    // Phones and tablets
        IOT_DEVICE,
        IOT,       // Alias for IOT_DEVICE
        CAMERA,
        PRINTER,
        MEDIA_DEVICE,
        SMART_TV,
        GAMING_CONSOLE,
        NAS,
        UNKNOWN
    }

    /**
     * Security risk level for a device
     */
    enum class SecurityRisk {
        NONE,       // No security issues detected
        LOW,        // Minor security concerns
        MEDIUM,     // Moderate security concerns
        HIGH,       // Significant security concerns
        CRITICAL,   // Critical security vulnerabilities
        UNKNOWN     // Security status unknown
    }

    /**
     * Device verification status
     */
    enum class DeviceVerificationStatus {
        VERIFIED,   // Device has been verified
        UNVERIFIED, // Device has not been verified
        SUSPICIOUS, // Device is suspicious
        BLOCKED,    // Device is blocked
        UNKNOWN,    // Verification status unknown
        PENDING     // Verification in progress
    }

    /**
     * Returns true if the device is verified
     */
    val isVerified: Boolean
        get() = verificationStatus == DeviceVerificationStatus.VERIFIED

    companion object {
        /**
         * Creates a NetworkDevice from an InetAddress
         */
        fun fromInetAddress(address: InetAddress): NetworkDevice {
            return NetworkDevice(
                ipAddress = address.hostAddress ?: "",
                hostname = address.hostName,
                isOnline = address.isReachable(1000)
            )
        }
    }
}
