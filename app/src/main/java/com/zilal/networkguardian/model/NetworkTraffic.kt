package com.zilal.networkguardian.model

/**
 * Represents network traffic data for a device
 */
data class NetworkTraffic(
    val deviceIp: String,
    val totalBytesReceived: Long = 0,
    val totalBytesSent: Long = 0,
    val packetsReceived: Long = 0,
    val packetsSent: Long = 0,
    val connections: List<NetworkConnection> = emptyList(),
    val applications: List<ApplicationTraffic> = emptyList(),
    val websites: List<WebsiteAccess> = emptyList(),
    val protocols: Map<String, Long> = emptyMap(), // Protocol -> bytes
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * Get total traffic in bytes
     */
    fun getTotalTraffic(): Long = totalBytesReceived + totalBytesSent
    
    /**
     * Get total packets
     */
    fun getTotalPackets(): Long = packetsReceived + packetsSent
    
    /**
     * Get formatted traffic size
     */
    fun getFormattedTrafficSize(): String {
        val totalBytes = getTotalTraffic()
        return when {
            totalBytes < 1024 -> "$totalBytes B"
            totalBytes < 1024 * 1024 -> "${totalBytes / 1024} KB"
            totalBytes < 1024 * 1024 * 1024 -> "${totalBytes / (1024 * 1024)} MB"
            else -> "${totalBytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * Get top protocols by traffic
     */
    fun getTopProtocols(limit: Int = 5): List<Pair<String, Long>> {
        return protocols.toList().sortedByDescending { it.second }.take(limit)
    }
    
    /**
     * Get top websites by access count
     */
    fun getTopWebsites(limit: Int = 10): List<WebsiteAccess> {
        return websites.sortedByDescending { it.accessCount }.take(limit)
    }
    
    /**
     * Get active connections
     */
    fun getActiveConnections(): List<NetworkConnection> {
        val currentTime = System.currentTimeMillis()
        return connections.filter { currentTime - it.lastActivity < 300000 } // Active in last 5 minutes
    }
}

/**
 * Represents a network connection
 */
data class NetworkConnection(
    val localPort: Int,
    val remoteIp: String,
    val remotePort: Int,
    val protocol: String, // TCP, UDP, etc.
    val state: ConnectionState,
    val bytesReceived: Long = 0,
    val bytesSent: Long = 0,
    val startTime: Long = System.currentTimeMillis(),
    val lastActivity: Long = System.currentTimeMillis(),
    val remoteHostname: String? = null,
    val applicationName: String? = null
) {
    /**
     * Get connection duration in milliseconds
     */
    fun getDuration(): Long = lastActivity - startTime
    
    /**
     * Get formatted duration
     */
    fun getFormattedDuration(): String {
        val duration = getDuration()
        return when {
            duration < 60000 -> "${duration / 1000}s"
            duration < 3600000 -> "${duration / 60000}m"
            else -> "${duration / 3600000}h"
        }
    }
    
    /**
     * Check if connection is active
     */
    fun isActive(): Boolean {
        val currentTime = System.currentTimeMillis()
        return currentTime - lastActivity < 300000 && state == ConnectionState.ESTABLISHED
    }
}

/**
 * Connection states
 */
enum class ConnectionState {
    ESTABLISHED,
    LISTEN,
    SYN_SENT,
    SYN_RECV,
    FIN_WAIT1,
    FIN_WAIT2,
    TIME_WAIT,
    CLOSE,
    CLOSE_WAIT,
    LAST_ACK,
    CLOSING,
    UNKNOWN
}

/**
 * Represents application traffic data
 */
data class ApplicationTraffic(
    val applicationName: String,
    val packageName: String? = null,
    val bytesReceived: Long = 0,
    val bytesSent: Long = 0,
    val connections: Int = 0,
    val lastActivity: Long = System.currentTimeMillis(),
    val category: ApplicationCategory = ApplicationCategory.UNKNOWN
) {
    /**
     * Get total traffic for this application
     */
    fun getTotalTraffic(): Long = bytesReceived + bytesSent
    
    /**
     * Get formatted traffic size
     */
    fun getFormattedTrafficSize(): String {
        val totalBytes = getTotalTraffic()
        return when {
            totalBytes < 1024 -> "$totalBytes B"
            totalBytes < 1024 * 1024 -> "${totalBytes / 1024} KB"
            totalBytes < 1024 * 1024 * 1024 -> "${totalBytes / (1024 * 1024)} MB"
            else -> "${totalBytes / (1024 * 1024 * 1024)} GB"
        }
    }
}

/**
 * Application categories
 */
enum class ApplicationCategory {
    BROWSER,
    SOCIAL_MEDIA,
    MESSAGING,
    STREAMING,
    GAMING,
    PRODUCTIVITY,
    SYSTEM,
    SECURITY,
    UNKNOWN
}

/**
 * Represents website access data
 */
data class WebsiteAccess(
    val domain: String,
    val url: String? = null,
    val accessCount: Int = 0,
    val bytesTransferred: Long = 0,
    val firstAccess: Long = System.currentTimeMillis(),
    val lastAccess: Long = System.currentTimeMillis(),
    val category: WebsiteCategory = WebsiteCategory.UNKNOWN,
    val isSecure: Boolean = false // HTTPS
) {
    /**
     * Get formatted bytes transferred
     */
    fun getFormattedBytesTransferred(): String {
        return when {
            bytesTransferred < 1024 -> "$bytesTransferred B"
            bytesTransferred < 1024 * 1024 -> "${bytesTransferred / 1024} KB"
            bytesTransferred < 1024 * 1024 * 1024 -> "${bytesTransferred / (1024 * 1024)} MB"
            else -> "${bytesTransferred / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * Get access frequency (accesses per hour)
     */
    fun getAccessFrequency(): Double {
        val duration = lastAccess - firstAccess
        return if (duration > 0) {
            (accessCount.toDouble() / duration) * 3600000 // Convert to per hour
        } else {
            0.0
        }
    }
}

/**
 * Website categories
 */
enum class WebsiteCategory {
    SEARCH,
    SOCIAL_MEDIA,
    NEWS,
    ENTERTAINMENT,
    SHOPPING,
    EDUCATION,
    BUSINESS,
    TECHNOLOGY,
    GAMING,
    STREAMING,
    ADULT,
    MALWARE,
    PHISHING,
    UNKNOWN
}

/**
 * Network packet information
 */
data class NetworkPacket(
    val timestamp: Long = System.currentTimeMillis(),
    val sourceIp: String,
    val destinationIp: String,
    val sourcePort: Int,
    val destinationPort: Int,
    val protocol: String,
    val size: Int,
    val flags: List<String> = emptyList(),
    val payload: ByteArray? = null
) {
    /**
     * Check if packet is incoming
     */
    fun isIncoming(deviceIp: String): Boolean = destinationIp == deviceIp
    
    /**
     * Check if packet is outgoing
     */
    fun isOutgoing(deviceIp: String): Boolean = sourceIp == deviceIp
    
    /**
     * Get packet direction
     */
    fun getDirection(deviceIp: String): PacketDirection {
        return when {
            isIncoming(deviceIp) -> PacketDirection.INCOMING
            isOutgoing(deviceIp) -> PacketDirection.OUTGOING
            else -> PacketDirection.UNKNOWN
        }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NetworkPacket

        if (timestamp != other.timestamp) return false
        if (sourceIp != other.sourceIp) return false
        if (destinationIp != other.destinationIp) return false
        if (sourcePort != other.sourcePort) return false
        if (destinationPort != other.destinationPort) return false
        if (protocol != other.protocol) return false
        if (size != other.size) return false
        if (flags != other.flags) return false
        if (payload != null) {
            if (other.payload == null) return false
            if (!payload.contentEquals(other.payload)) return false
        } else if (other.payload != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = timestamp.hashCode()
        result = 31 * result + sourceIp.hashCode()
        result = 31 * result + destinationIp.hashCode()
        result = 31 * result + sourcePort
        result = 31 * result + destinationPort
        result = 31 * result + protocol.hashCode()
        result = 31 * result + size
        result = 31 * result + flags.hashCode()
        result = 31 * result + (payload?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * Packet direction
 */
enum class PacketDirection {
    INCOMING,
    OUTGOING,
    UNKNOWN
}
