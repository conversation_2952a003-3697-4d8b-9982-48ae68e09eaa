package com.zilal.networkguardian

import android.app.Application
import android.util.Log
import androidx.work.Configuration
import com.zilal.networkguardian.worker.WorkManagerHelper
/**
 * Application class for Zilal app (legacy - not used)
 */
class ZilalApplication : Application() {

    companion object {
        private const val TAG = "ZilalApplication"
    }

    override fun onCreate() {
        super.onCreate()

        // Initialize WorkManager for background scanning
        initializeBackgroundScanning()

        Log.d(TAG, "Zilal application initialized")
    }

    /**
     * Initialize background scanning with WorkManager
     */
    private fun initializeBackgroundScanning() {
        // Schedule periodic network scans
        WorkManagerHelper.schedulePeriodicNetworkScans(
            context = this,
            scanMode = WorkManagerHelper.ScanMode.DEFAULT
        )

        Log.d(TAG, "Background scanning initialized")
    }
}
