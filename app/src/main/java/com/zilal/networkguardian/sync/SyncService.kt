package com.zilal.networkguardian.sync

import android.content.Context
import androidx.work.*
import com.zilal.networkguardian.api.ApiClient
import com.zilal.networkguardian.api.NetworkChecker
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.model.*
import com.zilal.networkguardian.auth.service.AnalyticsService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Real-time synchronization service for cloud data sync
 */
@Singleton
class SyncService @Inject constructor(
    private val context: Context,
    private val apiClient: ApiClient,
    private val authDao: AuthDao,
    private val networkChecker: <PERSON><PERSON>hecker,
    private val analyticsService: AnalyticsService
) {
    
    private val syncScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _syncStatus = MutableStateFlow(SyncStatus.IDLE)
    val syncStatus: StateFlow<SyncStatus> = _syncStatus.asStateFlow()
    
    private val _lastSyncTime = MutableStateFlow(0L)
    val lastSyncTime: StateFlow<Long> = _lastSyncTime.asStateFlow()
    
    private val _pendingUploads = MutableStateFlow(0)
    val pendingUploads: StateFlow<Int> = _pendingUploads.asStateFlow()
    
    companion object {
        private const val SYNC_WORK_NAME = "sync_work"
        private const val SYNC_INTERVAL_MINUTES = 15L
        private const val PREFS_NAME = "sync_prefs"
        private const val KEY_LAST_SYNC = "last_sync"
        private const val KEY_SYNC_ENABLED = "sync_enabled"
    }
    
    private val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    init {
        _lastSyncTime.value = prefs.getLong(KEY_LAST_SYNC, 0L)
        setupPeriodicSync()
    }
    
    /**
     * Setup periodic background sync
     */
    private fun setupPeriodicSync() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(
            SYNC_INTERVAL_MINUTES, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        
        WorkManager.getInstance(context)
            .enqueueUniquePeriodicWork(
                SYNC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                syncRequest
            )
    }
    
    /**
     * Manual sync trigger
     */
    fun syncNow() {
        syncScope.launch {
            performSync()
        }
    }
    
    /**
     * Enable/disable automatic sync
     */
    fun setSyncEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_SYNC_ENABLED, enabled).apply()
        
        if (enabled) {
            setupPeriodicSync()
        } else {
            WorkManager.getInstance(context).cancelUniqueWork(SYNC_WORK_NAME)
        }
    }
    
    /**
     * Check if sync is enabled
     */
    fun isSyncEnabled(): Boolean {
        return prefs.getBoolean(KEY_SYNC_ENABLED, true)
    }
    
    /**
     * Perform full synchronization
     */
    suspend fun performSync(): SyncResult {
        if (!networkChecker.isNetworkAvailable()) {
            return SyncResult.Error("No network connection")
        }
        
        if (!isSyncEnabled()) {
            return SyncResult.Error("Sync is disabled")
        }
        
        _syncStatus.value = SyncStatus.SYNCING
        
        try {
            // Upload local data first
            val uploadResult = uploadLocalData()
            if (uploadResult is SyncResult.Error) {
                _syncStatus.value = SyncStatus.ERROR
                return uploadResult
            }
            
            // Download remote data
            val downloadResult = downloadRemoteData()
            if (downloadResult is SyncResult.Error) {
                _syncStatus.value = SyncStatus.ERROR
                return downloadResult
            }
            
            // Update last sync time
            val currentTime = System.currentTimeMillis()
            _lastSyncTime.value = currentTime
            prefs.edit().putLong(KEY_LAST_SYNC, currentTime).apply()
            
            _syncStatus.value = SyncStatus.SUCCESS
            return SyncResult.Success("Sync completed successfully")
            
        } catch (e: Exception) {
            _syncStatus.value = SyncStatus.ERROR
            return SyncResult.Error(e.message ?: "Sync failed")
        }
    }
    
    /**
     * Upload local data to cloud
     */
    private suspend fun uploadLocalData(): SyncResult {
        try {
            val lastSync = _lastSyncTime.value
            
            // Get local data that needs to be uploaded
            val pendingAnalytics = authDao.getUserAnalytics("current_user", 1000)
                .filter { it.timestamp > lastSync }
            
            val pendingScans = authDao.getUserScanAnalytics("current_user", 100)
                .filter { it.timestamp > lastSync }
            
            val pendingDevices = authDao.getUserDeviceAnalytics("current_user")
                .filter { it.lastSeen > lastSync }
            
            if (pendingAnalytics.isEmpty() && pendingScans.isEmpty() && pendingDevices.isEmpty()) {
                return SyncResult.Success("No data to upload")
            }
            
            // Convert to API format and upload
            val uploadRequest = com.zilal.networkguardian.api.SyncUploadRequest(
                analytics = pendingAnalytics.map { convertToApiAnalytics(it) },
                scans = pendingScans.map { convertToApiScan(it) },
                devices = pendingDevices.map { convertToApiDevice(it) },
                lastSync = lastSync
            )
            
            val response = apiClient.apiService.uploadData(uploadRequest)
            
            if (response.isSuccessful) {
                _pendingUploads.value = 0
                return SyncResult.Success("Data uploaded successfully")
            } else {
                return SyncResult.Error("Upload failed: ${response.message()}")
            }
            
        } catch (e: Exception) {
            return SyncResult.Error("Upload error: ${e.message}")
        }
    }
    
    /**
     * Download remote data from cloud
     */
    private suspend fun downloadRemoteData(): SyncResult {
        try {
            val lastSync = _lastSyncTime.value
            val response = apiClient.apiService.downloadData(lastSync)
            
            if (response.isSuccessful) {
                val data = response.body()
                
                // Process downloaded data
                data?.analytics?.forEach { apiAnalytics ->
                    val localAnalytics = convertFromApiAnalytics(apiAnalytics)
                    authDao.insertAnalyticsEvent(localAnalytics)
                }
                
                data?.insights?.forEach { apiInsight ->
                    val localInsight = convertFromApiInsight(apiInsight)
                    authDao.insertAIInsight(localInsight)
                }
                
                data?.scans?.forEach { apiScan ->
                    val localScan = convertFromApiScan(apiScan)
                    authDao.insertScanAnalytics(localScan)
                }
                
                data?.devices?.forEach { apiDevice ->
                    val localDevice = convertFromApiDevice(apiDevice)
                    authDao.insertDeviceAnalytics(localDevice)
                }
                
                return SyncResult.Success("Data downloaded successfully")
            } else {
                return SyncResult.Error("Download failed: ${response.message()}")
            }
            
        } catch (e: Exception) {
            return SyncResult.Error("Download error: ${e.message}")
        }
    }
    
    // Conversion functions between local and API models
    private fun convertToApiAnalytics(analytics: UserAnalytics): com.zilal.networkguardian.api.AnalyticsEventRequest {
        return com.zilal.networkguardian.api.AnalyticsEventRequest(
            eventType = analytics.eventType.name,
            eventName = analytics.eventName,
            eventData = analytics.eventData,
            screenName = analytics.screenName,
            timestamp = analytics.timestamp,
            duration = analytics.duration,
            networkType = analytics.networkType,
            batteryLevel = analytics.batteryLevel,
            memoryUsage = analytics.memoryUsage,
            cpuUsage = analytics.cpuUsage
        )
    }
    
    private fun convertToApiScan(scan: ScanAnalytics): com.zilal.networkguardian.api.CreateScanRequest {
        return com.zilal.networkguardian.api.CreateScanRequest(
            scanType = scan.scanType,
            targetRange = scan.targetRange,
            scanId = scan.scanId
        )
    }
    
    private fun convertToApiDevice(device: DeviceAnalytics): com.zilal.networkguardian.api.AddDeviceRequest {
        return com.zilal.networkguardian.api.AddDeviceRequest(
            deviceId = device.deviceId,
            deviceName = device.deviceName,
            deviceType = device.deviceType,
            macAddress = device.macAddress,
            ipAddress = device.ipAddress,
            manufacturer = device.manufacturer,
            isKnownDevice = device.isKnownDevice
        )
    }
    
    private fun convertFromApiAnalytics(api: com.zilal.networkguardian.api.ApiAnalyticsEvent): UserAnalytics {
        return UserAnalytics(
            id = api.id,
            userId = "current_user", // This should be the actual user ID
            sessionId = "sync_session",
            eventType = AnalyticsEventType.valueOf(api.eventType),
            eventName = api.eventName,
            eventData = api.eventData,
            screenName = api.screenName,
            timestamp = System.currentTimeMillis(), // Convert from API timestamp
            duration = api.duration,
            networkType = api.networkType,
            batteryLevel = api.batteryLevel,
            memoryUsage = api.memoryUsage,
            cpuUsage = api.cpuUsage
        )
    }
    
    private fun convertFromApiInsight(api: com.zilal.networkguardian.api.ApiInsight): AIInsight {
        return AIInsight(
            id = api.id,
            userId = "current_user",
            insightType = AIInsightType.valueOf(api.insightType.uppercase()),
            title = api.title,
            description = api.description,
            recommendation = api.recommendation,
            severity = InsightSeverity.valueOf(api.severity.uppercase()),
            confidence = api.confidence,
            dataPoints = api.dataPoints,
            isRead = api.isRead,
            isActioned = api.isActioned,
            createdAt = System.currentTimeMillis(),
            expiresAt = null
        )
    }
    
    private fun convertFromApiScan(api: com.zilal.networkguardian.api.ApiScan): ScanAnalytics {
        return ScanAnalytics(
            id = api.id,
            userId = "current_user",
            scanId = api.scanId,
            scanType = api.scanType,
            targetRange = api.targetRange,
            devicesFound = api.devicesFound,
            vulnerabilitiesFound = api.vulnerabilitiesFound,
            scanDuration = api.scanDuration,
            scanSuccess = api.scanSuccess,
            errorMessage = api.errorMessage,
            networkConditions = api.networkConditions,
            deviceTypes = api.deviceTypes,
            securityScore = api.securityScore,
            timestamp = System.currentTimeMillis()
        )
    }
    
    private fun convertFromApiDevice(api: com.zilal.networkguardian.api.ApiDevice): DeviceAnalytics {
        return DeviceAnalytics(
            id = api.id,
            userId = "current_user",
            deviceId = api.deviceId,
            deviceName = api.deviceName,
            deviceType = api.deviceType,
            macAddress = api.macAddress,
            ipAddress = api.ipAddress,
            manufacturer = api.manufacturer,
            firstSeen = System.currentTimeMillis(),
            lastSeen = System.currentTimeMillis(),
            totalConnections = api.totalConnections,
            securityRisk = SecurityRiskLevel.valueOf(api.securityRisk.uppercase()),
            isKnownDevice = api.isKnownDevice,
            isTrusted = api.isTrusted,
            notes = api.notes
        )
    }
}

/**
 * Sync status enum
 */
enum class SyncStatus {
    IDLE,
    SYNCING,
    SUCCESS,
    ERROR
}

/**
 * Sync result sealed class
 */
sealed class SyncResult {
    data class Success(val message: String) : SyncResult()
    data class Error(val message: String) : SyncResult()
}

/**
 * Background sync worker
 */
class SyncWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        // This would be injected in a real implementation
        // For now, we'll return success
        return try {
            // Perform sync operation
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}
