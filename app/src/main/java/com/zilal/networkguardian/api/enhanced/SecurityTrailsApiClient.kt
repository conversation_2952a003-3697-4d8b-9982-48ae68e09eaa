package com.zilal.networkguardian.api.enhanced

import android.util.Log
import com.zilal.networkguardian.api.ApiConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * Security Trails API Client
 * Provides domain intelligence, DNS history, and infrastructure analysis
 */
class SecurityTrailsApiClient {
    
    companion object {
        private const val TAG = "SecurityTrailsApiClient"
    }
    
    /**
     * Get comprehensive domain information
     */
    suspend fun getDomainInfo(domain: String): SecurityTrailsDomainInfo? {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.SECURITY_TRAILS_BASE_URL}domain/$domain"
                
                Log.d(TAG, "Requesting domain info for: $domain")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("APIKEY", ApiConfig.SECURITY_TRAILS_API_KEY)
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "SecurityTrails domain response: $response")
                    
                    parseDomainInfo(response, domain)
                } else {
                    Log.w(TAG, "SecurityTrails API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching domain info from SecurityTrails", e)
                null
            }
        }
    }
    
    /**
     * Get IP address information and associated domains
     */
    suspend fun getIPInfo(ipAddress: String): SecurityTrailsIPInfo? {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.SECURITY_TRAILS_BASE_URL}ips/nearby/$ipAddress"
                
                Log.d(TAG, "Requesting IP info for: $ipAddress")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("APIKEY", ApiConfig.SECURITY_TRAILS_API_KEY)
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "SecurityTrails IP response: $response")
                    
                    parseIPInfo(response, ipAddress)
                } else {
                    Log.w(TAG, "SecurityTrails IP API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching IP info from SecurityTrails", e)
                null
            }
        }
    }
    
    /**
     * Get DNS history for a domain
     */
    suspend fun getDNSHistory(domain: String, recordType: String = "a"): SecurityTrailsDNSHistory? {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.SECURITY_TRAILS_BASE_URL}history/$domain/$recordType"
                
                Log.d(TAG, "Requesting DNS history for: $domain ($recordType)")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("APIKEY", ApiConfig.SECURITY_TRAILS_API_KEY)
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "SecurityTrails DNS history response: $response")
                    
                    parseDNSHistory(response, domain, recordType)
                } else {
                    Log.w(TAG, "SecurityTrails DNS history API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching DNS history from SecurityTrails", e)
                null
            }
        }
    }
    
    /**
     * Get subdomains for a domain
     */
    suspend fun getSubdomains(domain: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.SECURITY_TRAILS_BASE_URL}domain/$domain/subdomains"
                
                Log.d(TAG, "Requesting subdomains for: $domain")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("APIKEY", ApiConfig.SECURITY_TRAILS_API_KEY)
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "SecurityTrails subdomains response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    val subdomainsArray = jsonResponse.optJSONArray("subdomains")
                    
                    if (subdomainsArray != null) {
                        (0 until subdomainsArray.length()).map { 
                            subdomainsArray.getString(it) + "." + domain 
                        }
                    } else {
                        emptyList()
                    }
                } else {
                    Log.w(TAG, "SecurityTrails subdomains API error: $responseCode")
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching subdomains from SecurityTrails", e)
                emptyList()
            }
        }
    }
    
    /**
     * Parse domain information response
     */
    private fun parseDomainInfo(response: String, domain: String): SecurityTrailsDomainInfo {
        val jsonResponse = JSONObject(response)
        
        return SecurityTrailsDomainInfo(
            domain = domain,
            alexa_rank = jsonResponse.optInt("alexa_rank", 0),
            hostname = jsonResponse.optString("hostname", ""),
            mail_provider = jsonResponse.optJSONArray("mail_provider")?.let { array ->
                (0 until array.length()).map { array.getString(it) }
            } ?: emptyList(),
            subdomain_count = jsonResponse.optInt("subdomain_count", 0),
            current_dns = parseDNSRecords(jsonResponse.optJSONObject("current_dns")),
            endpoint = parseEndpoint(jsonResponse.optJSONObject("endpoint")),
            whois = parseWhoisInfo(jsonResponse.optJSONObject("whois"))
        )
    }
    
    /**
     * Parse IP information response
     */
    private fun parseIPInfo(response: String, ipAddress: String): SecurityTrailsIPInfo {
        val jsonResponse = JSONObject(response)
        
        return SecurityTrailsIPInfo(
            ipAddress = ipAddress,
            nearby_ips = jsonResponse.optJSONArray("nearby_ips")?.let { array ->
                (0 until array.length()).map { array.getString(it) }
            } ?: emptyList(),
            blocks = jsonResponse.optJSONArray("blocks")?.let { array ->
                (0 until array.length()).map { 
                    val blockObj = array.getJSONObject(it)
                    IPBlock(
                        cidr = blockObj.optString("cidr", ""),
                        size = blockObj.optInt("size", 0),
                        organization = blockObj.optString("organization", "")
                    )
                }
            } ?: emptyList()
        )
    }
    
    /**
     * Parse DNS history response
     */
    private fun parseDNSHistory(response: String, domain: String, recordType: String): SecurityTrailsDNSHistory {
        val jsonResponse = JSONObject(response)
        
        return SecurityTrailsDNSHistory(
            domain = domain,
            recordType = recordType,
            records = jsonResponse.optJSONArray("records")?.let { array ->
                (0 until array.length()).map { 
                    val recordObj = array.getJSONObject(it)
                    DNSRecord(
                        first_seen = recordObj.optString("first_seen", ""),
                        last_seen = recordObj.optString("last_seen", ""),
                        values = recordObj.optJSONArray("values")?.let { valArray ->
                            (0 until valArray.length()).map { valArray.getString(it) }
                        } ?: emptyList()
                    )
                }
            } ?: emptyList()
        )
    }
    
    /**
     * Parse DNS records
     */
    private fun parseDNSRecords(dnsObj: JSONObject?): CurrentDNS? {
        if (dnsObj == null) return null
        
        return CurrentDNS(
            a = parseRecordValues(dnsObj.optJSONObject("a")),
            aaaa = parseRecordValues(dnsObj.optJSONObject("aaaa")),
            mx = parseRecordValues(dnsObj.optJSONObject("mx")),
            ns = parseRecordValues(dnsObj.optJSONObject("ns")),
            txt = parseRecordValues(dnsObj.optJSONObject("txt"))
        )
    }
    
    /**
     * Parse record values
     */
    private fun parseRecordValues(recordObj: JSONObject?): List<String> {
        if (recordObj == null) return emptyList()
        
        val valuesArray = recordObj.optJSONArray("values")
        return if (valuesArray != null) {
            (0 until valuesArray.length()).map { valuesArray.getString(it) }
        } else {
            emptyList()
        }
    }
    
    /**
     * Parse endpoint information
     */
    private fun parseEndpoint(endpointObj: JSONObject?): EndpointInfo? {
        if (endpointObj == null) return null
        
        return EndpointInfo(
            ip = endpointObj.optString("ip", ""),
            server = endpointObj.optString("server", ""),
            port = endpointObj.optInt("port", 0)
        )
    }
    
    /**
     * Parse WHOIS information
     */
    private fun parseWhoisInfo(whoisObj: JSONObject?): WhoisInfo? {
        if (whoisObj == null) return null
        
        return WhoisInfo(
            registrar = whoisObj.optString("registrar", ""),
            creationDate = whoisObj.optString("creationDate", ""),
            expirationDate = whoisObj.optString("expirationDate", ""),
            organization = whoisObj.optString("organization", "")
        )
    }
}

/**
 * Data classes for SecurityTrails responses
 */
data class SecurityTrailsDomainInfo(
    val domain: String,
    val alexa_rank: Int,
    val hostname: String,
    val mail_provider: List<String>,
    val subdomain_count: Int,
    val current_dns: CurrentDNS?,
    val endpoint: EndpointInfo?,
    val whois: WhoisInfo?
)

data class SecurityTrailsIPInfo(
    val ipAddress: String,
    val nearby_ips: List<String>,
    val blocks: List<IPBlock>
)

data class SecurityTrailsDNSHistory(
    val domain: String,
    val recordType: String,
    val records: List<DNSRecord>
)

data class CurrentDNS(
    val a: List<String>,
    val aaaa: List<String>,
    val mx: List<String>,
    val ns: List<String>,
    val txt: List<String>
)

data class EndpointInfo(
    val ip: String,
    val server: String,
    val port: Int
)

data class WhoisInfo(
    val registrar: String,
    val creationDate: String,
    val expirationDate: String,
    val organization: String
)

data class IPBlock(
    val cidr: String,
    val size: Int,
    val organization: String
)

data class DNSRecord(
    val first_seen: String,
    val last_seen: String,
    val values: List<String>
)
