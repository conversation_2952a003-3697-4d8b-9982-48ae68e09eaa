package com.zilal.networkguardian.api

import retrofit2.Response
import retrofit2.http.*

/**
 * Local API service for testing with local server
 */
interface LocalApiService {
    
    // Health check
    @GET("health")
    suspend fun healthCheck(): Response<HealthResponse>
    
    // Authentication endpoints
    @POST("api/auth/register")
    suspend fun register(@Body request: LocalRegisterRequest): Response<LocalAuthResponse>
    
    @POST("api/auth/login")
    suspend fun login(@Body request: LocalLoginRequest): Response<LocalAuthResponse>
    
    // Analytics endpoints
    @POST("api/analytics/events")
    suspend fun trackEvent(@Body event: LocalAnalyticsEvent): Response<LocalMessageResponse>
    
    @GET("api/analytics/events")
    suspend fun getAnalyticsEvents(@Query("limit") limit: Int = 100): Response<LocalAnalyticsResponse>
    
    // Scan endpoints
    @POST("api/scans")
    suspend fun createScan(@Body request: LocalCreateScanRequest): Response<LocalMessageResponse>
    
    @PUT("api/scans/{scanId}")
    suspend fun updateScan(
        @Path("scanId") scanId: String,
        @Body request: LocalUpdateScanRequest
    ): Response<LocalMessageResponse>
    
    @GET("api/scans")
    suspend fun getScans(@Query("limit") limit: Int = 50): Response<LocalScansResponse>
    
    // Device endpoints
    @POST("api/devices")
    suspend fun saveDevice(@Body request: LocalDeviceRequest): Response<LocalMessageResponse>
    
    @GET("api/devices")
    suspend fun getDevices(): Response<LocalDevicesResponse>
    
    // Insights endpoints
    @GET("api/insights")
    suspend fun getInsights(@Query("limit") limit: Int = 20): Response<LocalInsightsResponse>
    
    // Dashboard endpoint
    @GET("api/dashboard")
    suspend fun getDashboardData(): Response<LocalDashboardResponse>
}

// Data classes for local API
data class HealthResponse(
    val status: String,
    val timestamp: String,
    val uptime: Double,
    val environment: String,
    val version: String,
    val database: String,
    val features: List<String>
)

data class LocalRegisterRequest(
    val email: String,
    val username: String,
    val password: String,
    val firstName: String? = null,
    val lastName: String? = null
)

data class LocalLoginRequest(
    val emailOrUsername: String,
    val password: String
)

data class LocalAuthResponse(
    val message: String,
    val user: LocalUser,
    val token: String
)

data class LocalUser(
    val id: String,
    val email: String,
    val username: String,
    val first_name: String?,
    val last_name: String?,
    val role: String,
    val subscription_plan: String,
    val subscription_status: String,
    val created_at: String,
    val last_login_at: String?,
    val login_count: Int
)

data class LocalMessageResponse(
    val message: String,
    val eventId: String? = null,
    val scanId: String? = null,
    val deviceId: String? = null
)

data class LocalAnalyticsEvent(
    val eventType: String,
    val eventName: String,
    val eventData: Map<String, Any>? = null,
    val screenName: String? = null,
    val duration: Long? = null,
    val networkType: String? = null,
    val batteryLevel: Int? = null,
    val memoryUsage: Int? = null,
    val cpuUsage: Float? = null
)

data class LocalAnalyticsResponse(
    val events: List<LocalAnalyticsEventResponse>,
    val total: Int
)

data class LocalAnalyticsEventResponse(
    val id: String,
    val user_id: String,
    val event_type: String,
    val event_name: String,
    val event_data: String?,
    val screen_name: String?,
    val timestamp: String,
    val duration_ms: Long?,
    val network_type: String?,
    val battery_level: Int?,
    val memory_usage_mb: Int?,
    val cpu_usage_percent: Float?
)

data class LocalCreateScanRequest(
    val scanId: String,
    val scanType: String,
    val targetRange: String
)

data class LocalUpdateScanRequest(
    val devicesFound: Int,
    val vulnerabilitiesFound: Int,
    val scanDuration: Long,
    val success: Boolean,
    val errorMessage: String? = null,
    val scanResults: Map<String, Any>? = null
)

data class LocalScansResponse(
    val scans: List<LocalScan>,
    val total: Int
)

data class LocalScan(
    val id: String,
    val user_id: String,
    val scan_id: String,
    val scan_type: String,
    val target_range: String,
    val scan_status: String,
    val devices_found: Int,
    val vulnerabilities_found: Int,
    val scan_duration_ms: Long,
    val success: Boolean,
    val error_message: String?,
    val scan_results: String?,
    val started_at: String,
    val completed_at: String?
)

data class LocalDeviceRequest(
    val deviceId: String,
    val deviceName: String? = null,
    val ipAddress: String,
    val macAddress: String? = null,
    val deviceType: String = "unknown",
    val manufacturer: String? = null,
    val securityRisk: String = "low",
    val isKnownDevice: Boolean = false
)

data class LocalDevicesResponse(
    val devices: List<LocalDevice>,
    val total: Int
)

data class LocalDevice(
    val id: String,
    val user_id: String,
    val device_id: String,
    val device_name: String?,
    val ip_address: String,
    val mac_address: String?,
    val device_type: String,
    val manufacturer: String?,
    val security_risk: String,
    val is_known_device: Boolean,
    val is_trusted: Boolean,
    val first_seen: String,
    val last_seen: String,
    val total_connections: Int,
    val notes: String?
)

data class LocalInsightsResponse(
    val insights: List<LocalInsight>,
    val total: Int
)

data class LocalInsight(
    val id: String,
    val user_id: String,
    val insight_type: String,
    val title: String,
    val description: String,
    val recommendation: String?,
    val severity: String,
    val confidence_score: Float,
    val data_points: String?,
    val is_read: Boolean,
    val is_actioned: Boolean,
    val created_at: String
)

data class LocalDashboardResponse(
    val stats: LocalDashboardStats,
    val recentScans: List<LocalScan>,
    val recentDevices: List<LocalDevice>,
    val recentInsights: List<LocalInsight>,
    val timestamp: String
)

data class LocalDashboardStats(
    val totalScans: Int,
    val totalDevices: Int,
    val totalEvents: Int,
    val unreadInsights: Int
)
