package com.zilal.networkguardian.api

import com.zilal.networkguardian.auth.model.*
import retrofit2.Response
import retrofit2.http.*

/**
 * API service interface for SecureNet Pro backend
 */
interface ApiService {
    
    // Authentication endpoints
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<AuthResponse>
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<AuthResponse>
    
    @POST("auth/logout")
    suspend fun logout(@Header("x-session-id") sessionId: String): Response<MessageResponse>
    
    @GET("auth/verify")
    suspend fun verifyToken(): Response<UserResponse>
    
    // User management
    @GET("users/profile")
    suspend fun getUserProfile(): Response<UserResponse>
    
    @PUT("users/profile")
    suspend fun updateProfile(@Body request: UpdateProfileRequest): Response<UserResponse>
    
    @PUT("users/password")
    suspend fun changePassword(@Body request: ChangePasswordRequest): Response<MessageResponse>
    
    // Analytics endpoints
    @POST("analytics/events")
    suspend fun trackEvent(@Body event: AnalyticsEventRequest): Response<MessageResponse>
    
    @POST("analytics/events/batch")
    suspend fun trackEventsBatch(@Body events: List<AnalyticsEventRequest>): Response<MessageResponse>
    
    @GET("analytics/user")
    suspend fun getUserAnalytics(
        @Query("limit") limit: Int = 100,
        @Query("type") type: String? = null
    ): Response<AnalyticsResponse>
    
    @GET("analytics/insights")
    suspend fun getAIInsights(@Query("limit") limit: Int = 20): Response<InsightsResponse>
    
    @POST("analytics/insights/generate")
    suspend fun generateInsights(): Response<InsightsResponse>
    
    // Scan endpoints
    @POST("scans")
    suspend fun createScan(@Body request: CreateScanRequest): Response<ScanResponse>
    
    @PUT("scans/{scanId}")
    suspend fun updateScan(
        @Path("scanId") scanId: String,
        @Body request: UpdateScanRequest
    ): Response<ScanResponse>
    
    @GET("scans")
    suspend fun getUserScans(
        @Query("limit") limit: Int = 50,
        @Query("type") type: String? = null
    ): Response<ScansResponse>
    
    @GET("scans/{scanId}")
    suspend fun getScan(@Path("scanId") scanId: String): Response<ScanResponse>
    
    // Device endpoints
    @POST("devices")
    suspend fun addDevice(@Body request: AddDeviceRequest): Response<DeviceResponse>
    
    @PUT("devices/{deviceId}")
    suspend fun updateDevice(
        @Path("deviceId") deviceId: String,
        @Body request: UpdateDeviceRequest
    ): Response<DeviceResponse>
    
    @GET("devices")
    suspend fun getUserDevices(): Response<DevicesResponse>
    
    @DELETE("devices/{deviceId}")
    suspend fun deleteDevice(@Path("deviceId") deviceId: String): Response<MessageResponse>
    
    // Sync endpoints
    @POST("sync/upload")
    suspend fun uploadData(@Body request: SyncUploadRequest): Response<SyncResponse>
    
    @GET("sync/download")
    suspend fun downloadData(@Query("lastSync") lastSync: Long): Response<SyncDownloadResponse>
    
    @GET("sync/status")
    suspend fun getSyncStatus(): Response<SyncStatusResponse>
}

// Request/Response data classes
data class RegisterRequest(
    val email: String,
    val username: String,
    val password: String,
    val firstName: String? = null,
    val lastName: String? = null
)

data class LoginRequest(
    val emailOrUsername: String,
    val password: String
)

data class AuthResponse(
    val message: String,
    val user: ApiUser,
    val token: String,
    val sessionId: String
)

data class ApiUser(
    val id: String,
    val email: String,
    val username: String,
    val firstName: String?,
    val lastName: String?,
    val role: String,
    val subscriptionType: String,
    val createdAt: String,
    val lastLoginAt: String?,
    val loginCount: Int
)

data class UserResponse(
    val user: ApiUser
)

data class MessageResponse(
    val message: String
)

data class UpdateProfileRequest(
    val firstName: String?,
    val lastName: String?,
    val profileImageUrl: String?
)

data class ChangePasswordRequest(
    val currentPassword: String,
    val newPassword: String
)

data class AnalyticsEventRequest(
    val eventType: String,
    val eventName: String,
    val eventData: String? = null,
    val screenName: String? = null,
    val timestamp: Long = System.currentTimeMillis(),
    val duration: Long? = null,
    val networkType: String? = null,
    val batteryLevel: Int? = null,
    val memoryUsage: Long? = null,
    val cpuUsage: Float? = null
)

data class AnalyticsResponse(
    val analytics: List<ApiAnalyticsEvent>,
    val total: Int,
    val page: Int,
    val limit: Int
)

data class ApiAnalyticsEvent(
    val id: String,
    val eventType: String,
    val eventName: String,
    val eventData: String?,
    val screenName: String?,
    val timestamp: String,
    val duration: Long?,
    val networkType: String?,
    val batteryLevel: Int?,
    val memoryUsage: Long?,
    val cpuUsage: Float?
)

data class InsightsResponse(
    val insights: List<ApiInsight>,
    val total: Int,
    val unread: Int
)

data class ApiInsight(
    val id: String,
    val insightType: String,
    val title: String,
    val description: String,
    val recommendation: String?,
    val severity: String,
    val confidence: Float,
    val dataPoints: String?,
    val isRead: Boolean,
    val isActioned: Boolean,
    val createdAt: String,
    val expiresAt: String?
)

data class CreateScanRequest(
    val scanType: String,
    val targetRange: String,
    val scanId: String = java.util.UUID.randomUUID().toString()
)

data class UpdateScanRequest(
    val devicesFound: Int,
    val vulnerabilitiesFound: Int,
    val scanDuration: Long,
    val scanSuccess: Boolean,
    val errorMessage: String? = null,
    val deviceTypes: Map<String, Int>? = null,
    val securityScore: Int? = null,
    val networkConditions: String? = null
)

data class ScanResponse(
    val scan: ApiScan
)

data class ScansResponse(
    val scans: List<ApiScan>,
    val total: Int,
    val page: Int,
    val limit: Int
)

data class ApiScan(
    val id: String,
    val scanId: String,
    val scanType: String,
    val targetRange: String,
    val devicesFound: Int,
    val vulnerabilitiesFound: Int,
    val scanDuration: Long,
    val scanSuccess: Boolean,
    val errorMessage: String?,
    val deviceTypes: String?,
    val securityScore: Int?,
    val timestamp: String,
    val networkConditions: String?
)

data class AddDeviceRequest(
    val deviceId: String,
    val deviceName: String?,
    val deviceType: String,
    val macAddress: String?,
    val ipAddress: String,
    val manufacturer: String?,
    val isKnownDevice: Boolean = false
)

data class UpdateDeviceRequest(
    val deviceName: String?,
    val manufacturer: String?,
    val isKnownDevice: Boolean?,
    val isTrusted: Boolean?,
    val notes: String?
)

data class DeviceResponse(
    val device: ApiDevice
)

data class DevicesResponse(
    val devices: List<ApiDevice>,
    val total: Int
)

data class ApiDevice(
    val id: String,
    val deviceId: String,
    val deviceName: String?,
    val deviceType: String,
    val macAddress: String?,
    val ipAddress: String,
    val manufacturer: String?,
    val firstSeen: String,
    val lastSeen: String,
    val totalConnections: Int,
    val securityRisk: String,
    val isKnownDevice: Boolean,
    val isTrusted: Boolean,
    val notes: String?
)

data class SyncUploadRequest(
    val analytics: List<AnalyticsEventRequest>? = null,
    val scans: List<CreateScanRequest>? = null,
    val devices: List<AddDeviceRequest>? = null,
    val lastSync: Long
)

data class SyncResponse(
    val message: String,
    val syncId: String,
    val timestamp: Long
)

data class SyncDownloadResponse(
    val analytics: List<ApiAnalyticsEvent>? = null,
    val insights: List<ApiInsight>? = null,
    val scans: List<ApiScan>? = null,
    val devices: List<ApiDevice>? = null,
    val lastSync: Long
)

data class SyncStatusResponse(
    val lastSync: Long,
    val pendingUploads: Int,
    val syncEnabled: Boolean
)
