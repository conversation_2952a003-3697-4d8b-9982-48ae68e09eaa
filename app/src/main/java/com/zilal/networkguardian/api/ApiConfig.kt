package com.zilal.networkguardian.api

/**
 * Configuration class for API keys and endpoints
 */
object ApiConfig {
    // API Keys
    const val OTX_API_KEY = "437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0"
    const val NVD_API_KEY = "9be12dbe-1479-46f6-955d-d249e118b4c2"
    const val VIRUS_TOTAL_API_KEY = "0128653679336ca21dbe7faaf311e510ae8ad1a554ac9e0b7ea952f1fdf12e4c"
    const val GEMINI_API_KEY = "AIzaSyDlH3toLBpndiI4-gVF2w0A8-MkSipDyaI"
    const val SHODAN_API_KEY = "********************************"

    // API Endpoints
    const val OTX_BASE_URL = "https://otx.alienvault.com/api/v1/"
    const val NVD_BASE_URL = "https://services.nvd.nist.gov/rest/json/cves/2.0/"
    const val VIRUS_TOTAL_BASE_URL = "https://www.virustotal.com/api/v3/"
    const val GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/"
    const val SHODAN_BASE_URL = "https://api.shodan.io/"
}
