package com.zilal.networkguardian.api

/**
 * Enhanced API Configuration for Production-Ready Network Security
 * Includes all major security and network intelligence APIs
 */
object ApiConfig {
    // Existing API Keys
    const val OTX_API_KEY = "437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0"
    const val NVD_API_KEY = "9be12dbe-1479-46f6-955d-d249e118b4c2"
    const val VIRUS_TOTAL_API_KEY = "0128653679336ca21dbe7faaf311e510ae8ad1a554ac9e0b7ea952f1fdf12e4c"
    const val GEMINI_API_KEY = "AIzaSyDlH3toLBpndiI4-gVF2w0A8-MkSipDyaI"
    const val SHODAN_API_KEY = "********************************"

    // New Enhanced API Keys
    const val MAC_ADDRESS_IO_API_KEY = "at_eYJ1oRB178yG0WzD2l7V7bXELZP9X"
    const val MAC_VENDORS_API_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiIsImp0aSI6ImVkOGNiZjAzLWEzZWMtNGIxYS1iZTY0LWExZTE2MDcyN2FiNCJ9.eyJpc3MiOiJtYWN2ZW5kb3JzIiwiYXVkIjoibWFjdmVuZG9ycyIsImp0aSI6ImVkOGNiZjAzLWEzZWMtNGIxYS1iZTY0LWExZTE2MDcyN2FiNCIsImlhdCI6MTc0OTIyNzk5MCwiZXhwIjoyMDYzNzIzOTkwLCJzdWIiOiIxNjEwNCIsInR5cCI6ImFjY2VzcyJ9.jOH3yZNzO5gO3pnkHuaoCYU51B6jisyys-JLue6Nm1U9UoXF264Ph-KBc30UdJvOyBtmHwedKtyQLHKPC1UfGg"
    const val SECURITY_TRAILS_API_KEY = "uMYkHUNW3cU8IiXeD-_0g-xoTG1LEd0G"
    const val HYBRID_ANALYSIS_API_KEY = "0qh8iapqccaef07649wbirwzdcc4ec11mw16h25k29a2de8cmyws3xk6ec6006b8"
    const val HYBRID_ANALYSIS_SECRET = "9672517983511897a3a3b9c72a9e4d82af5545e4f2710693"

    // Existing API Endpoints
    const val OTX_BASE_URL = "https://otx.alienvault.com/api/v1/"
    const val NVD_BASE_URL = "https://services.nvd.nist.gov/rest/json/cves/2.0/"
    const val VIRUS_TOTAL_BASE_URL = "https://www.virustotal.com/api/v3/"
    const val GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/"
    const val SHODAN_BASE_URL = "https://api.shodan.io/"

    // New Enhanced API Endpoints
    const val MAC_ADDRESS_IO_BASE_URL = "https://api.macaddress.io/v1/"
    const val MAC_VENDORS_BASE_URL = "https://api.macvendors.com/v1/"
    const val SECURITY_TRAILS_BASE_URL = "https://api.securitytrails.com/v1/"
    const val HYBRID_ANALYSIS_BASE_URL = "https://www.hybrid-analysis.com/api/v2/"

    // Additional Free APIs for Enhanced Features
    const val IPAPI_BASE_URL = "http://ip-api.com/json/"
    const val IPINFO_BASE_URL = "https://ipinfo.io/"
    const val WHOIS_BASE_URL = "https://www.whoisxmlapi.com/whoisserver/WhoisService"
    const val DNS_LOOKUP_BASE_URL = "https://dns.google/resolve"
}
