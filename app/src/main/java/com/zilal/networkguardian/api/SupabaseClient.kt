package com.zilal.networkguardian.api

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * Client for interacting with Supabase
 */
class SupabaseClient(private val context: Context) {

    companion object {
        private const val SUPABASE_URL = "https://yselfyopwwtslluyreoo.supabase.co"
        private const val SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzZWxmeW9wd3d0c2xsdXlyZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxNTU4ODYsImV4cCI6MjA1NTczMTg4Nn0.uw3o_iNlD3Ytm5WI8pO-AyEEfPAwLIwbG1_15oTDpTc"
        private const val PREFS_NAME = "ZilalPrefs"
        private const val KEY_USER_TOKEN = "user_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_EMAIL = "user_email"
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    private val jsonMediaType = "application/json; charset=utf-8".toMediaType()
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * Enhanced Data Models for Production-Ready Supabase Integration
     */

    // Device Management
    @Serializable
    data class DeviceDto(
        val id: String = "",
        val userId: String = "",
        val macAddress: String,
        val originalName: String,
        val customName: String? = null,
        val deviceType: String,
        val manufacturer: String? = null,
        val isKidsDevice: Boolean = false,
        val isOnline: Boolean = true,
        val lastSeen: Long,
        val ipAddress: String? = null,
        val signalStrength: Int? = null,
        val connectionType: String? = null,
        val firstSeen: Long? = null,
        val operatingSystem: String? = null,
        val openPorts: List<Int>? = null,
        val vulnerabilities: List<VulnerabilityInfo>? = null,
        val riskScore: Double? = null
    )

    @Serializable
    data class VulnerabilityInfo(
        val cveId: String,
        val severity: String,
        val description: String,
        val solution: String
    )

    // Network Scan Results
    @Serializable
    data class NetworkScanDto(
        val id: String = "",
        val userId: String,
        val scanType: String, // "discovery", "vulnerability", "port", "security"
        val status: String, // "running", "completed", "failed"
        val startTime: Long,
        val endTime: Long? = null,
        val results: ScanResults,
        val details: String? = null
    )

    @Serializable
    data class ScanResults(
        val devicesFound: Int,
        val vulnerabilitiesFound: Int,
        val openPorts: Int,
        val securityIssues: Int
    )

    // Vulnerability Database
    @Serializable
    data class VulnerabilityDto(
        val id: String = "",
        val cveId: String,
        val severity: String,
        val score: Double,
        val description: String,
        val solution: String,
        val publishedDate: Long,
        val lastModified: Long,
        val affectedProducts: List<String>,
        val references: List<String>
    )

    // Threat Intelligence
    @Serializable
    data class ThreatIntelligenceDto(
        val id: String = "",
        val type: String, // "ip", "domain", "hash", "url"
        val value: String,
        val source: String, // "virustotal", "alienvault", "custom"
        val threatType: String,
        val confidence: Double,
        val lastChecked: Long,
        val details: ThreatDetails
    )

    @Serializable
    data class ThreatDetails(
        val malicious: Boolean,
        val reputation: Int,
        val categories: List<String>
    )

    // Network Traffic
    @Serializable
    data class NetworkTrafficDto(
        val id: String = "",
        val userId: String,
        val deviceId: String,
        val timestamp: Long,
        val sourceIp: String,
        val destinationIp: String,
        val protocol: String,
        val port: Int,
        val bytes: Long,
        val packets: Int,
        val suspicious: Boolean,
        val blocked: Boolean
    )

    // Notifications
    @Serializable
    data class NotificationDto(
        val id: String = "",
        val userId: String,
        val type: String,
        val title: String,
        val message: String,
        val deviceId: String? = null,
        val isRead: Boolean = false,
        val timestamp: Long,
        val severity: String,
        val actionRequired: Boolean? = null
    )

    // User Settings
    @Serializable
    data class UserSettingsDto(
        val id: String = "",
        val userId: String,
        val theme: String = "system",
        val notifications: NotificationSettings,
        val autoScanInterval: Int = 3600, // seconds
        val aiAnalysisEnabled: Boolean = true
    )

    @Serializable
    data class NotificationSettings(
        val newDevices: Boolean = true,
        val securityAlerts: Boolean = true,
        val unusualActivity: Boolean = true,
        val deviceBlocked: Boolean = true
    )

    // AI Analysis
    @Serializable
    data class AIAnalysisDto(
        val id: String = "",
        val userId: String,
        val deviceId: String? = null,
        val analysisType: String,
        val findings: List<AIFinding>,
        val timestamp: Long
    )

    @Serializable
    data class AIFinding(
        val type: String,
        val confidence: Double,
        val description: String,
        val recommendation: String? = null
    )

    /**
     * Sign up a new user
     * @param email The user's email
     * @param password The user's password
     * @return Result indicating success or failure
     */
    suspend fun signUp(email: String, password: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val jsonBody = JSONObject().apply {
                put("email", email)
                put("password", password)
            }.toString()

            val requestBody = jsonBody.toRequestBody(jsonMediaType)

            val request = Request.Builder()
                .url("$SUPABASE_URL/auth/v1/signup")
                .addHeader("apikey", SUPABASE_KEY)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val jsonResponse = JSONObject(responseBody)
                    val userId = jsonResponse.optJSONObject("user")?.optString("id")
                    val token = jsonResponse.optString("access_token")

                    if (userId != null && token.isNotEmpty()) {
                        // Save user info
                        prefs.edit()
                            .putString(KEY_USER_TOKEN, token)
                            .putString(KEY_USER_ID, userId)
                            .putString(KEY_USER_EMAIL, email)
                            .apply()

                        Result.success(Unit)
                    } else {
                        Result.failure(Exception("Invalid response format"))
                    }
                } else {
                    Result.failure(Exception("Empty response body"))
                }
            } else {
                val errorBody = response.body?.string()
                val errorMessage = if (errorBody != null) {
                    try {
                        JSONObject(errorBody).optString("error_description", "Sign up failed")
                    } catch (e: Exception) {
                        "Sign up failed"
                    }
                } else {
                    "Sign up failed"
                }
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sign in an existing user
     * @param email The user's email
     * @param password The user's password
     * @return Result indicating success or failure
     */
    suspend fun signIn(email: String, password: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val jsonBody = JSONObject().apply {
                put("email", email)
                put("password", password)
            }.toString()

            val requestBody = jsonBody.toRequestBody(jsonMediaType)

            val request = Request.Builder()
                .url("$SUPABASE_URL/auth/v1/token?grant_type=password")
                .addHeader("apikey", SUPABASE_KEY)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val jsonResponse = JSONObject(responseBody)
                    val userId = jsonResponse.optJSONObject("user")?.optString("id")
                    val token = jsonResponse.optString("access_token")

                    if (userId != null && token.isNotEmpty()) {
                        // Save user info
                        prefs.edit()
                            .putString(KEY_USER_TOKEN, token)
                            .putString(KEY_USER_ID, userId)
                            .putString(KEY_USER_EMAIL, email)
                            .apply()

                        Result.success(Unit)
                    } else {
                        Result.failure(Exception("Invalid response format"))
                    }
                } else {
                    Result.failure(Exception("Empty response body"))
                }
            } else {
                val errorBody = response.body?.string()
                val errorMessage = if (errorBody != null) {
                    try {
                        JSONObject(errorBody).optString("error_description", "Sign in failed")
                    } catch (e: Exception) {
                        "Sign in failed"
                    }
                } else {
                    "Sign in failed"
                }
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sign out the current user
     * @return Result indicating success or failure
     */
    suspend fun signOut(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Clear user info from preferences
            prefs.edit()
                .remove(KEY_USER_TOKEN)
                .remove(KEY_USER_ID)
                .remove(KEY_USER_EMAIL)
                .apply()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Check if a user is signed in
     * @return True if a user is signed in, false otherwise
     */
    fun isSignedIn(): Boolean {
        return prefs.getString(KEY_USER_TOKEN, null) != null
    }

    /**
     * Get the current user's ID
     * @return The user ID or null if not signed in
     */
    fun getCurrentUserId(): String? {
        return prefs.getString(KEY_USER_ID, null)
    }

    /**
     * Get the context
     * @return The context
     */
    fun getContext(): Context {
        return context
    }

    /**
     * Save a scan result to Supabase (Enhanced version)
     * @param scanResult The scan result to save
     * @return The scan ID
     */
    suspend fun saveScanResultEnhanced(scanResult: NetworkScanDto): String = withContext(Dispatchers.IO) {
        // Generate a UUID for the scan
        val scanId = UUID.randomUUID().toString()

        try {
            // Check if we have internet connection
            if (isNetworkAvailable()) {
                // Create scan result JSON
                val scanResultJson = JSONObject().apply {
                    put("id", scanId)
                    put("timestamp", scanResult.startTime)
                    put("scan_type", scanResult.scanType)
                    put("target_address", "Local Network")
                    put("device_count", 0)
                    put("vulnerability_count", 0)
                }

                // Insert scan result
                val request = Request.Builder()
                    .url("$SUPABASE_URL/rest/v1/scan_results")
                    .addHeader("apikey", SUPABASE_KEY)
                    .addHeader("Content-Type", "application/json")
                    .post(scanResultJson.toString().toRequestBody(jsonMediaType))
                    .build()

                try {
                    client.newCall(request).execute().use { response ->
                        if (!response.isSuccessful) {
                            Log.e("SupabaseClient", "Error saving scan result: ${response.code}")
                            // Save to pending uploads
                            savePendingScanResult(scanId, scanResult)
                            return@withContext scanId
                        }
                    }
                } catch (e: Exception) {
                    Log.e("SupabaseClient", "Network error saving scan result: ${e.message}")
                    // Save to pending uploads
                    savePendingScanResult(scanId, scanResult)
                    return@withContext scanId
                }

                // Insert devices (placeholder - no devices in NetworkScanDto)
                var deviceSuccess = true
                // TODO: Implement device insertion for NetworkScanDto

                // Insert vulnerabilities
                var vulnSuccess = true
                // TODO: Implement vulnerability insertion for NetworkScanDto

                // If any part failed, save to pending uploads
                if (!deviceSuccess || !vulnSuccess) {
                    // TODO: Implement pending upload for NetworkScanDto
                    // savePendingScanResult(scanId, scanResult)
                }

                Log.d("SupabaseClient", "Successfully saved scan result with ID: $scanId")
            } else {
                // No internet connection, save to pending uploads
                Log.d("SupabaseClient", "No internet connection, saving scan result locally")
                // TODO: Implement pending upload for NetworkScanDto
                // savePendingScanResult(scanId, scanResult)
            }
        } catch (e: Exception) {
            Log.e("SupabaseClient", "Error saving scan result: ${e.message}")
            // Save to pending uploads
            // TODO: Implement pending upload for NetworkScanDto
            // savePendingScanResult(scanId, scanResult)
        }

        return@withContext scanId
    }

    /**
     * Save a scan result to local storage for later upload
     * @param scanId The scan ID
     * @param scanResult The scan result to save
     */
    private fun savePendingScanResult(scanId: String, scanResult: NetworkScanDto) {
        // TODO: Implement pending upload functionality
        /*
        try {
            // Get existing pending uploads
            val pendingUploads = getPendingUploads()

            // Add the new scan result
            pendingUploads[scanId] = scanResult

            // Save the updated map
            val json = gson.toJson(pendingUploads)
            prefs.edit().putString("pending_uploads", json).apply()

            Log.d("SupabaseClient", "Saved scan result to pending uploads: $scanId")
        } catch (e: Exception) {
            Log.e("SupabaseClient", "Error saving pending scan result: ${e.message}")
        }
        */
    }

    /**
     * Get pending uploads from local storage
     * @return Map of scan ID to scan result
     */
    private fun getPendingUploads(): MutableMap<String, NetworkScanDto> {
        // TODO: Implement pending uploads functionality
        return mutableMapOf()
        /*
        val json = prefs.getString("pending_uploads", null)
        return if (json != null) {
            val type = object : TypeToken<MutableMap<String, NetworkScanDto>>() {}.type
            gson.fromJson(json, type)
        } else {
            mutableMapOf()
        }
        */
    }

    /**
     * Check if network is available
     * @return True if network is available, false otherwise
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
        val networkCapabilities = connectivityManager.activeNetwork ?: return false
        val actNw = connectivityManager.getNetworkCapabilities(networkCapabilities) ?: return false

        return when {
            actNw.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI) -> true
            actNw.hasTransport(android.net.NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            actNw.hasTransport(android.net.NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            else -> false
        }
    }

    /**
     * Sync pending uploads to Supabase
     * @return Result indicating success or failure
     */
    suspend fun syncPendingUploads(): Result<Int> = withContext(Dispatchers.IO) {
        // TODO: Implement sync functionality
        return@withContext Result.success(0)
        /*
        try {
            // Check if we have internet connection
            if (!isNetworkAvailable()) {
                return@withContext Result.failure(Exception("No internet connection"))
            }

            // Get pending uploads
            val pendingUploads = getPendingUploads()
            if (pendingUploads.isEmpty()) {
                return@withContext Result.success(0)
            }

            var successCount = 0
            val successfulIds = mutableListOf<String>()

            // Upload each pending scan result
            for ((scanId, scanResult) in pendingUploads) {
                try {
                    // Create scan result JSON
                    val scanResultJson = JSONObject().apply {
                        put("id", scanId)
                        put("timestamp", scanResult.timestamp)
                        put("scan_type", scanResult.scanType)
                        put("target_address", scanResult.targetAddress)
                        put("device_count", scanResult.devices.size)
                        put("vulnerability_count", scanResult.vulnerabilities.size)
                    }

                    // Insert scan result
                    val request = Request.Builder()
                        .url("$SUPABASE_URL/rest/v1/scan_results")
                        .addHeader("apikey", SUPABASE_KEY)
                        .addHeader("Content-Type", "application/json")
                        .post(scanResultJson.toString().toRequestBody(jsonMediaType))
                        .build()

                    var scanSuccess = false
                    try {
                        client.newCall(request).execute().use { response ->
                            scanSuccess = response.isSuccessful
                        }
                    } catch (e: Exception) {
                        Log.e("SupabaseClient", "Network error syncing scan result: ${e.message}")
                        continue
                    }

                    if (!scanSuccess) {
                        continue
                    }

                    // Insert devices
                    var deviceSuccess = true
                    for (device in scanResult.devices) {
                        val deviceJson = JSONObject().apply {
                            put("id", UUID.randomUUID().toString())
                            put("scan_id", scanId)
                            put("ip_address", device.ipAddress)
                            put("mac_address", device.macAddress ?: "")
                            put("hostname", device.hostname ?: "")
                            put("manufacturer", device.manufacturer ?: "")
                            put("device_type", device.deviceType ?: "")
                            put("is_online", device.isOnline)
                            put("last_seen", device.lastSeen)

                            // Convert open ports to JSON array
                            val portsArray = org.json.JSONArray()
                            device.openPorts?.forEach { port ->
                                portsArray.put(port)
                            }
                            put("open_ports", portsArray)
                        }

                        val deviceRequest = Request.Builder()
                            .url("$SUPABASE_URL/rest/v1/devices")
                            .addHeader("apikey", SUPABASE_KEY)
                            .addHeader("Content-Type", "application/json")
                            .post(deviceJson.toString().toRequestBody(jsonMediaType))
                            .build()

                        try {
                            client.newCall(deviceRequest).execute().use { response ->
                                if (!response.isSuccessful) {
                                    deviceSuccess = false
                                }
                            }
                        } catch (e: Exception) {
                            deviceSuccess = false
                        }
                    }

                    // Insert vulnerabilities
                    var vulnSuccess = true
                    for (vulnerability in scanResult.vulnerabilities) {
                        val vulnerabilityJson = JSONObject().apply {
                            put("id", UUID.randomUUID().toString())
                            put("scan_id", scanId)
                            put("device_id", vulnerability.deviceId)
                            put("name", vulnerability.name)
                            put("description", vulnerability.description)
                            put("severity", vulnerability.severity)
                            put("detection_timestamp", vulnerability.detectionTimestamp)
                            put("device_ip_address", vulnerability.deviceIpAddress)
                        }

                        val vulnerabilityRequest = Request.Builder()
                            .url("$SUPABASE_URL/rest/v1/vulnerabilities")
                            .addHeader("apikey", SUPABASE_KEY)
                            .addHeader("Content-Type", "application/json")
                            .post(vulnerabilityJson.toString().toRequestBody(jsonMediaType))
                            .build()

                        try {
                            client.newCall(vulnerabilityRequest).execute().use { response ->
                                if (!response.isSuccessful) {
                                    vulnSuccess = false
                                }
                            }
                        } catch (e: Exception) {
                            vulnSuccess = false
                        }
                    }

                    // If all parts succeeded, mark as successful
                    if (scanSuccess && deviceSuccess && vulnSuccess) {
                        successCount++
                        successfulIds.add(scanId)
                        Log.d("SupabaseClient", "Successfully synced scan result: $scanId")
                    }
                } catch (e: Exception) {
                    Log.e("SupabaseClient", "Error syncing scan result $scanId: ${e.message}")
                }
            }

            // Remove successful uploads from pending uploads
            if (successfulIds.isNotEmpty()) {
                val remainingUploads = pendingUploads.filterKeys { it !in successfulIds }
                val json = gson.toJson(remainingUploads)
                prefs.edit().putString("pending_uploads", json).apply()
            }

            Result.success(successCount)
        } catch (e: Exception) {
            Log.e("SupabaseClient", "Error syncing pending uploads: ${e.message}")
            Result.failure(e)
        }
        */
    }

    /**
     * Save a scan result to local storage
     * @param scanResult The scan result to save
     * @return Result indicating success or failure
     */
    suspend fun saveScanResult(scanResult: ScanResultData): Result<Unit> = withContext(Dispatchers.IO) {
        // This method saves to local storage

        // TODO: Implement Supabase integration here


        try {
            // Get existing scan results
            val scanResults = getScanResultsFromStorage()

            // Add the new scan result
            scanResults.add(0, scanResult)

            // Save the updated list
            saveScanResultsToStorage(scanResults)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get scan results from local storage
     * @return List of scan results
     */
    suspend fun getScanResults(): Result<List<ScanResultData>> = withContext(Dispatchers.IO) {
        try {
            val userId = getCurrentUserId() ?: return@withContext Result.failure(Exception("Not signed in"))

            // Get scan results from storage
            val allScanResults = getScanResultsFromStorage()

            // Filter by user ID
            val userScanResults = allScanResults.filter { it.user_id == userId }

            Result.success(userScanResults)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save a vulnerability to local storage
     * @param vulnerability The vulnerability to save
     * @return Result indicating success or failure
     */
    suspend fun saveVulnerability(vulnerability: VulnerabilityData): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Get existing vulnerabilities
            val vulnerabilities = getVulnerabilitiesFromStorage()

            // Add the new vulnerability
            vulnerabilities.add(vulnerability)

            // Save the updated list
            saveVulnerabilitiesToStorage(vulnerabilities)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get vulnerabilities for a specific scan from local storage
     * @param scanId The scan ID
     * @return List of vulnerabilities
     */
    suspend fun getVulnerabilities(scanId: String): Result<List<VulnerabilityData>> = withContext(Dispatchers.IO) {
        try {
            // Get vulnerabilities from storage
            val allVulnerabilities = getVulnerabilitiesFromStorage()

            // Filter by scan ID
            val scanVulnerabilities = allVulnerabilities.filter { it.scan_id == scanId }

            Result.success(scanVulnerabilities)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save a device to local storage
     * @param device The device to save
     * @return Result indicating success or failure
     */
    suspend fun saveDevice(device: DeviceData): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Get existing devices
            val devices = getDevicesFromStorage()

            // Add the new device
            devices.add(device)

            // Save the updated list
            saveDevicesToStorage(devices)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get devices for a specific scan from local storage
     * @param scanId The scan ID
     * @return List of devices
     */
    suspend fun getDevices(scanId: String): Result<List<DeviceData>> = withContext(Dispatchers.IO) {
        try {
            // Get devices from storage
            val allDevices = getDevicesFromStorage()

            // Filter by scan ID
            val scanDevices = allDevices.filter { it.scan_id == scanId }

            Result.success(scanDevices)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get scan results from local storage
     */
    fun getScanResultsFromStorage(): MutableList<ScanResultData> {
        val json = prefs.getString("scan_results", null)
        return if (json != null) {
            val type = object : TypeToken<MutableList<ScanResultData>>() {}.type
            gson.fromJson(json, type)
        } else {
            mutableListOf()
        }
    }

    /**
     * Save scan results to local storage
     */
    fun saveScanResultsToStorage(scanResults: List<ScanResultData>) {
        val json = gson.toJson(scanResults)
        prefs.edit().putString("scan_results", json).apply()
    }

    /**
     * Get vulnerabilities from local storage
     */
    private fun getVulnerabilitiesFromStorage(): MutableList<VulnerabilityData> {
        val json = prefs.getString("vulnerabilities", null)
        return if (json != null) {
            val type = object : TypeToken<MutableList<VulnerabilityData>>() {}.type
            gson.fromJson(json, type)
        } else {
            mutableListOf()
        }
    }

    /**
     * Save vulnerabilities to local storage
     */
    private fun saveVulnerabilitiesToStorage(vulnerabilities: List<VulnerabilityData>) {
        val json = gson.toJson(vulnerabilities)
        prefs.edit().putString("vulnerabilities", json).apply()
    }

    /**
     * Get devices from local storage
     */
    private fun getDevicesFromStorage(): MutableList<DeviceData> {
        val json = prefs.getString("devices", null)
        return if (json != null) {
            val type = object : TypeToken<MutableList<DeviceData>>() {}.type
            gson.fromJson(json, type)
        } else {
            mutableListOf()
        }
    }

    /**
     * Save devices to local storage
     */
    private fun saveDevicesToStorage(devices: List<DeviceData>) {
        val json = gson.toJson(devices)
        prefs.edit().putString("devices", json).apply()
    }
}

/**
 * Data class for scan results
 */
data class ScanResultData(
    val id: String = UUID.randomUUID().toString(),
    val user_id: String,
    val scan_type: String,
    val target_address: String,
    val start_time: Long,
    val end_time: Long? = null,
    val summary: String,
    val created_at: String = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.US)
        .format(java.util.Date())
)

/**
 * Data class for vulnerabilities
 */
data class VulnerabilityData(
    val id: String = UUID.randomUUID().toString(),
    val scan_id: String,
    val name: String,
    val description: String,
    val severity: String,
    val cve_id: String? = null,
    val remediation_steps: String? = null,
    val created_at: String = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.US)
        .format(java.util.Date())
)

/**
 * Data class for devices
 */
data class DeviceData(
    val id: String = UUID.randomUUID().toString(),
    val scan_id: String,
    val ip_address: String,
    val mac_address: String? = null,
    val hostname: String? = null,
    val manufacturer: String? = null,
    val operating_system: String? = null,
    val open_ports: List<Int> = emptyList(),
    val created_at: String = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", java.util.Locale.US)
        .format(java.util.Date())
)
