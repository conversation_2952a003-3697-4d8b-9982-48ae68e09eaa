package com.zilal.networkguardian.api.enhanced

import android.util.Log
import com.zilal.networkguardian.api.ApiConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * Hybrid Analysis API Client
 * Provides advanced malware analysis and threat intelligence
 */
class HybridAnalysisApiClient {
    
    companion object {
        private const val TAG = "HybridAnalysisApiClient"
    }
    
    /**
     * Search for malware samples by hash
     */
    suspend fun searchByHash(hash: String): HybridAnalysisResult? {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.HYBRID_ANALYSIS_BASE_URL}search/hash"
                
                Log.d(TAG, "Searching malware by hash: $hash")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("api-key", ApiConfig.HYBRID_ANALYSIS_API_KEY)
                connection.setRequestProperty("user-agent", "Falcon Sandbox")
                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
                connection.doOutput = true
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                // Send hash parameter
                val postData = "hash=$hash"
                connection.outputStream.use { it.write(postData.toByteArray()) }
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "Hybrid Analysis hash search response: $response")
                    
                    parseSearchResult(response)
                } else {
                    Log.w(TAG, "Hybrid Analysis API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error searching hash in Hybrid Analysis", e)
                null
            }
        }
    }
    
    /**
     * Search for malware samples by terms
     */
    suspend fun searchByTerms(terms: String): List<HybridAnalysisResult> {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.HYBRID_ANALYSIS_BASE_URL}search/terms"
                
                Log.d(TAG, "Searching malware by terms: $terms")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("api-key", ApiConfig.HYBRID_ANALYSIS_API_KEY)
                connection.setRequestProperty("user-agent", "Falcon Sandbox")
                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
                connection.doOutput = true
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                // Send search terms
                val postData = "terms=$terms"
                connection.outputStream.use { it.write(postData.toByteArray()) }
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "Hybrid Analysis terms search response: $response")
                    
                    parseSearchResults(response)
                } else {
                    Log.w(TAG, "Hybrid Analysis terms API error: $responseCode")
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error searching terms in Hybrid Analysis", e)
                emptyList()
            }
        }
    }
    
    /**
     * Get detailed analysis report
     */
    suspend fun getAnalysisReport(jobId: String): HybridAnalysisReport? {
        return withContext(Dispatchers.IO) {
            try {
                val url = "${ApiConfig.HYBRID_ANALYSIS_BASE_URL}report/$jobId/summary"
                
                Log.d(TAG, "Getting analysis report for job: $jobId")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("api-key", ApiConfig.HYBRID_ANALYSIS_API_KEY)
                connection.setRequestProperty("user-agent", "Falcon Sandbox")
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "Hybrid Analysis report response: $response")
                    
                    parseAnalysisReport(response, jobId)
                } else {
                    Log.w(TAG, "Hybrid Analysis report API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting analysis report from Hybrid Analysis", e)
                null
            }
        }
    }
    
    /**
     * Get threat intelligence for IP address
     */
    suspend fun getIPThreatIntelligence(ipAddress: String): IPThreatInfo? {
        return withContext(Dispatchers.IO) {
            try {
                // Search for samples related to this IP
                val searchResults = searchByTerms(ipAddress)
                
                if (searchResults.isNotEmpty()) {
                    val threatLevel = determineThreatLevel(searchResults)
                    val malwareFamilies = extractMalwareFamilies(searchResults)
                    val firstSeen = searchResults.minByOrNull { it.submit_date }?.submit_date ?: ""
                    val lastSeen = searchResults.maxByOrNull { it.submit_date }?.submit_date ?: ""
                    
                    IPThreatInfo(
                        ipAddress = ipAddress,
                        threatLevel = threatLevel,
                        malwareFamilies = malwareFamilies,
                        sampleCount = searchResults.size,
                        firstSeen = firstSeen,
                        lastSeen = lastSeen,
                        relatedSamples = searchResults.take(5) // Limit to 5 most relevant
                    )
                } else {
                    // No threats found
                    IPThreatInfo(
                        ipAddress = ipAddress,
                        threatLevel = "Clean",
                        malwareFamilies = emptyList(),
                        sampleCount = 0,
                        firstSeen = "",
                        lastSeen = "",
                        relatedSamples = emptyList()
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting IP threat intelligence", e)
                null
            }
        }
    }
    
    /**
     * Parse search result for single hash
     */
    private fun parseSearchResult(response: String): HybridAnalysisResult? {
        return try {
            val jsonArray = JSONArray(response)
            if (jsonArray.length() > 0) {
                parseHybridResult(jsonArray.getJSONObject(0))
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing search result", e)
            null
        }
    }
    
    /**
     * Parse search results for multiple samples
     */
    private fun parseSearchResults(response: String): List<HybridAnalysisResult> {
        return try {
            val jsonArray = JSONArray(response)
            (0 until jsonArray.length()).mapNotNull { 
                parseHybridResult(jsonArray.getJSONObject(it))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing search results", e)
            emptyList()
        }
    }
    
    /**
     * Parse individual hybrid analysis result
     */
    private fun parseHybridResult(jsonObj: JSONObject): HybridAnalysisResult? {
        return try {
            HybridAnalysisResult(
                job_id = jsonObj.optString("job_id", ""),
                sha256 = jsonObj.optString("sha256", ""),
                environment_id = jsonObj.optInt("environment_id", 0),
                environment_description = jsonObj.optString("environment_description", ""),
                size = jsonObj.optLong("size", 0),
                type = jsonObj.optString("type", ""),
                type_short = jsonObj.optString("type_short", ""),
                target_url = jsonObj.optString("target_url", ""),
                state = jsonObj.optString("state", ""),
                error_type = jsonObj.optString("error_type", ""),
                error_origin = jsonObj.optString("error_origin", ""),
                submit_date = jsonObj.optString("submit_date", ""),
                filename = jsonObj.optString("filename", ""),
                verdict = jsonObj.optString("verdict", ""),
                threat_score = jsonObj.optInt("threat_score", 0),
                interesting = jsonObj.optBoolean("interesting", false),
                threat_level = jsonObj.optInt("threat_level", 0),
                av_detect = jsonObj.optInt("av_detect", 0),
                vx_family = jsonObj.optString("vx_family", "")
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing hybrid result", e)
            null
        }
    }
    
    /**
     * Parse analysis report
     */
    private fun parseAnalysisReport(response: String, jobId: String): HybridAnalysisReport? {
        return try {
            val jsonObj = JSONObject(response)
            
            HybridAnalysisReport(
                job_id = jobId,
                verdict = jsonObj.optString("verdict", ""),
                threat_score = jsonObj.optInt("threat_score", 0),
                threat_level = jsonObj.optInt("threat_level", 0),
                av_detect = jsonObj.optInt("av_detect", 0),
                total_network_connections = jsonObj.optInt("total_network_connections", 0),
                total_processes = jsonObj.optInt("total_processes", 0),
                total_signatures = jsonObj.optInt("total_signatures", 0),
                extracted_files = jsonObj.optJSONArray("extracted_files")?.let { array ->
                    (0 until array.length()).map { 
                        val fileObj = array.getJSONObject(it)
                        ExtractedFile(
                            name = fileObj.optString("name", ""),
                            file_size = fileObj.optLong("file_size", 0),
                            sha256 = fileObj.optString("sha256", "")
                        )
                    }
                } ?: emptyList(),
                network_connections = jsonObj.optJSONArray("network_connections")?.let { array ->
                    (0 until array.length()).map { 
                        val connObj = array.getJSONObject(it)
                        NetworkConnection(
                            host = connObj.optString("host", ""),
                            port = connObj.optInt("port", 0),
                            protocol = connObj.optString("protocol", "")
                        )
                    }
                } ?: emptyList()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing analysis report", e)
            null
        }
    }
    
    /**
     * Determine threat level from search results
     */
    private fun determineThreatLevel(results: List<HybridAnalysisResult>): String {
        val maxThreatScore = results.maxOfOrNull { it.threat_score } ?: 0
        
        return when {
            maxThreatScore >= 80 -> "Critical"
            maxThreatScore >= 60 -> "High"
            maxThreatScore >= 40 -> "Medium"
            maxThreatScore >= 20 -> "Low"
            else -> "Clean"
        }
    }
    
    /**
     * Extract malware families from results
     */
    private fun extractMalwareFamilies(results: List<HybridAnalysisResult>): List<String> {
        return results.mapNotNull { it.vx_family }
            .filter { it.isNotEmpty() }
            .distinct()
    }
}

/**
 * Data classes for Hybrid Analysis responses
 */
data class HybridAnalysisResult(
    val job_id: String,
    val sha256: String,
    val environment_id: Int,
    val environment_description: String,
    val size: Long,
    val type: String,
    val type_short: String,
    val target_url: String,
    val state: String,
    val error_type: String,
    val error_origin: String,
    val submit_date: String,
    val filename: String,
    val verdict: String,
    val threat_score: Int,
    val interesting: Boolean,
    val threat_level: Int,
    val av_detect: Int,
    val vx_family: String
)

data class HybridAnalysisReport(
    val job_id: String,
    val verdict: String,
    val threat_score: Int,
    val threat_level: Int,
    val av_detect: Int,
    val total_network_connections: Int,
    val total_processes: Int,
    val total_signatures: Int,
    val extracted_files: List<ExtractedFile>,
    val network_connections: List<NetworkConnection>
)

data class ExtractedFile(
    val name: String,
    val file_size: Long,
    val sha256: String
)

data class NetworkConnection(
    val host: String,
    val port: Int,
    val protocol: String
)

data class IPThreatInfo(
    val ipAddress: String,
    val threatLevel: String,
    val malwareFamilies: List<String>,
    val sampleCount: Int,
    val firstSeen: String,
    val lastSeen: String,
    val relatedSamples: List<HybridAnalysisResult>
)
