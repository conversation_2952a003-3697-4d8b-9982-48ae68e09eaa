package com.zilal.networkguardian.api.enhanced

import android.util.Log
import com.zilal.networkguardian.api.ApiConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * Enhanced MAC Address API Client
 * Integrates with MacAddress.io and MacVendors.com for comprehensive device identification
 */
class MacAddressApiClient {
    
    companion object {
        private const val TAG = "MacAddressApiClient"
    }
    
    /**
     * Get comprehensive device information from MAC address using MacAddress.io
     */
    suspend fun getDeviceInfoFromMacAddressIO(macAddress: String): MacAddressInfo? {
        return withContext(Dispatchers.IO) {
            try {
                val cleanMac = macAddress.replace(":", "").replace("-", "").uppercase()
                val formattedMac = cleanMac.chunked(2).joinToString(":")
                
                val url = "${ApiConfig.MAC_ADDRESS_IO_BASE_URL}?apiKey=${ApiConfig.MAC_ADDRESS_IO_API_KEY}&output=json&search=$formattedMac"
                
                Log.d(TAG, "Requesting MAC info from MacAddress.io: $formattedMac")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "MacAddress.io response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    
                    MacAddressInfo(
                        macAddress = formattedMac,
                        vendorDetails = VendorDetails(
                            oui = jsonResponse.optString("oui", ""),
                            isPrivate = jsonResponse.optBoolean("isPrivate", false),
                            companyName = jsonResponse.optString("companyName", "Unknown"),
                            companyAddress = jsonResponse.optString("companyAddress", ""),
                            countryCode = jsonResponse.optString("countryCode", "")
                        ),
                        blockDetails = BlockDetails(
                            blockFound = jsonResponse.optBoolean("blockFound", false),
                            borderLeft = jsonResponse.optString("borderLeft", ""),
                            borderRight = jsonResponse.optString("borderRight", ""),
                            blockSize = jsonResponse.optInt("blockSize", 0),
                            assignmentBlockSize = jsonResponse.optString("assignmentBlockSize", "")
                        ),
                        macAddressDetails = MacDetails(
                            searchTerm = jsonResponse.optString("searchTerm", ""),
                            isValid = jsonResponse.optBoolean("isValid", false),
                            virtualMachine = jsonResponse.optString("virtualMachine", "Not detected"),
                            applications = jsonResponse.optJSONArray("applications")?.let { array ->
                                (0 until array.length()).map { array.getString(it) }
                            } ?: emptyList(),
                            transmissionType = jsonResponse.optString("transmissionType", ""),
                            administrationType = jsonResponse.optString("administrationType", "")
                        )
                    )
                } else {
                    Log.w(TAG, "MacAddress.io API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching MAC info from MacAddress.io", e)
                null
            }
        }
    }
    
    /**
     * Get vendor information from MAC address using MacVendors.com
     */
    suspend fun getVendorInfoFromMacVendors(macAddress: String): MacVendorInfo? {
        return withContext(Dispatchers.IO) {
            try {
                val cleanMac = macAddress.replace(":", "").replace("-", "").uppercase()
                val formattedMac = cleanMac.chunked(2).joinToString(":")
                
                val url = "${ApiConfig.MAC_VENDORS_BASE_URL}lookup/$formattedMac"
                
                Log.d(TAG, "Requesting vendor info from MacVendors.com: $formattedMac")
                
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.setRequestProperty("Authorization", "Bearer ${ApiConfig.MAC_VENDORS_API_TOKEN}")
                connection.setRequestProperty("Accept", "application/json")
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = BufferedReader(InputStreamReader(connection.inputStream)).use { it.readText() }
                    Log.d(TAG, "MacVendors.com response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    
                    MacVendorInfo(
                        macAddress = formattedMac,
                        vendorName = jsonResponse.optString("company", "Unknown"),
                        vendorAddress = jsonResponse.optString("address", ""),
                        vendorCountry = jsonResponse.optString("country", ""),
                        vendorType = jsonResponse.optString("type", ""),
                        startHex = jsonResponse.optString("startHex", ""),
                        endHex = jsonResponse.optString("endHex", "")
                    )
                } else {
                    Log.w(TAG, "MacVendors.com API error: $responseCode")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching vendor info from MacVendors.com", e)
                null
            }
        }
    }
    
    /**
     * Get comprehensive MAC address information from both APIs
     */
    suspend fun getComprehensiveMacInfo(macAddress: String): ComprehensiveMacInfo {
        val macAddressInfo = getDeviceInfoFromMacAddressIO(macAddress)
        val vendorInfo = getVendorInfoFromMacVendors(macAddress)
        
        return ComprehensiveMacInfo(
            macAddress = macAddress,
            macAddressIOInfo = macAddressInfo,
            macVendorsInfo = vendorInfo,
            enhancedDeviceType = determineDeviceType(macAddressInfo, vendorInfo),
            securityRisk = assessSecurityRisk(macAddressInfo, vendorInfo),
            confidence = calculateConfidence(macAddressInfo, vendorInfo)
        )
    }
    
    /**
     * Determine device type based on vendor information
     */
    private fun determineDeviceType(macInfo: MacAddressInfo?, vendorInfo: MacVendorInfo?): String {
        val vendorName = macInfo?.vendorDetails?.companyName ?: vendorInfo?.vendorName ?: ""
        val applications = macInfo?.macAddressDetails?.applications ?: emptyList()
        
        return when {
            vendorName.contains("Apple", ignoreCase = true) -> "Apple Device"
            vendorName.contains("Samsung", ignoreCase = true) -> "Samsung Device"
            vendorName.contains("Intel", ignoreCase = true) -> "Computer/Laptop"
            vendorName.contains("Broadcom", ignoreCase = true) -> "Network Interface"
            vendorName.contains("Qualcomm", ignoreCase = true) -> "Mobile Device"
            vendorName.contains("Cisco", ignoreCase = true) -> "Network Equipment"
            vendorName.contains("TP-Link", ignoreCase = true) -> "Router/Access Point"
            vendorName.contains("Netgear", ignoreCase = true) -> "Router/Access Point"
            vendorName.contains("D-Link", ignoreCase = true) -> "Router/Access Point"
            vendorName.contains("Linksys", ignoreCase = true) -> "Router/Access Point"
            vendorName.contains("Raspberry", ignoreCase = true) -> "Raspberry Pi"
            vendorName.contains("Amazon", ignoreCase = true) -> "Smart Device"
            vendorName.contains("Google", ignoreCase = true) -> "Smart Device"
            applications.any { it.contains("IoT", ignoreCase = true) } -> "IoT Device"
            macInfo?.macAddressDetails?.virtualMachine != "Not detected" -> "Virtual Machine"
            else -> "Unknown Device"
        }
    }
    
    /**
     * Assess security risk based on device information
     */
    private fun assessSecurityRisk(macInfo: MacAddressInfo?, vendorInfo: MacVendorInfo?): String {
        return when {
            macInfo?.vendorDetails?.isPrivate == true -> "Low" // Private/randomized MAC
            macInfo?.macAddressDetails?.virtualMachine != "Not detected" -> "Medium" // VM detected
            vendorName.contains("Unknown", ignoreCase = true) -> "High" // Unknown vendor
            macInfo?.blockDetails?.blockFound == false -> "High" // No block found
            else -> "Low"
        }
    }
    
    /**
     * Calculate confidence score for the identification
     */
    private fun calculateConfidence(macInfo: MacAddressInfo?, vendorInfo: MacVendorInfo?): Int {
        var confidence = 0
        
        if (macInfo?.blockDetails?.blockFound == true) confidence += 30
        if (macInfo?.vendorDetails?.companyName?.isNotEmpty() == true) confidence += 25
        if (vendorInfo?.vendorName?.isNotEmpty() == true) confidence += 25
        if (macInfo?.macAddressDetails?.isValid == true) confidence += 20
        
        return confidence.coerceIn(0, 100)
    }
}

/**
 * Data classes for MAC address information
 */
data class MacAddressInfo(
    val macAddress: String,
    val vendorDetails: VendorDetails,
    val blockDetails: BlockDetails,
    val macAddressDetails: MacDetails
)

data class VendorDetails(
    val oui: String,
    val isPrivate: Boolean,
    val companyName: String,
    val companyAddress: String,
    val countryCode: String
)

data class BlockDetails(
    val blockFound: Boolean,
    val borderLeft: String,
    val borderRight: String,
    val blockSize: Int,
    val assignmentBlockSize: String
)

data class MacDetails(
    val searchTerm: String,
    val isValid: Boolean,
    val virtualMachine: String,
    val applications: List<String>,
    val transmissionType: String,
    val administrationType: String
)

data class MacVendorInfo(
    val macAddress: String,
    val vendorName: String,
    val vendorAddress: String,
    val vendorCountry: String,
    val vendorType: String,
    val startHex: String,
    val endHex: String
)

data class ComprehensiveMacInfo(
    val macAddress: String,
    val macAddressIOInfo: MacAddressInfo?,
    val macVendorsInfo: MacVendorInfo?,
    val enhancedDeviceType: String,
    val securityRisk: String,
    val confidence: Int
)
