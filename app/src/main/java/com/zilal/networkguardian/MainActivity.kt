package com.zilal.networkguardian

import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import android.graphics.Color
import android.view.Gravity
import android.widget.LinearLayout.LayoutParams

class MainActivity : AppCompatActivity() {

    private lateinit var mainContainer: LinearLayout
    private lateinit var headerLayout: LinearLayout
    private lateinit var navigationLayout: LinearLayout
    private lateinit var contentLayout: FrameLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Create the enhanced UI layout
        createEnhancedUI()

        // Set initial content to Dashboard
        showDashboard()
    }

    private fun createEnhancedUI() {
        // Main container - vertical layout
        mainContainer = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            setBackgroundColor(Color.parseColor("#F5F5F5"))
        }

        // Header with app title and notification bell
        createHeader()

        // Content area
        contentLayout = FrameLayout(this).apply {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, 0, 1f)
            setBackgroundColor(Color.WHITE)
        }

        // Bottom navigation (5 tabs, screen-fit)
        createBottomNavigation()

        // Add all components to main container
        mainContainer.addView(headerLayout)
        mainContainer.addView(contentLayout)
        mainContainer.addView(navigationLayout)

        setContentView(mainContainer)
    }

    private fun createHeader() {
        headerLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            setBackgroundColor(Color.parseColor("#1976D2"))
            setPadding(24, 48, 24, 24)
            gravity = Gravity.CENTER_VERTICAL
        }

        // App title
        val titleText = TextView(this).apply {
            text = "🔒 ZILAL NETWORK SECURITY"
            textSize = 18f
            setTextColor(Color.WHITE)
            layoutParams = LayoutParams(0, LayoutParams.WRAP_CONTENT, 1f)
            gravity = Gravity.CENTER_VERTICAL
        }

        // Notification bell
        val notificationBell = TextView(this).apply {
            text = "🔔"
            textSize = 24f
            layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            setPadding(16, 0, 0, 0)
            setOnClickListener { showNotifications() }
        }

        headerLayout.addView(titleText)
        headerLayout.addView(notificationBell)
    }

    private fun createBottomNavigation() {
        navigationLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            setBackgroundColor(Color.parseColor("#1976D2"))
            setPadding(0, 16, 0, 16)
        }

        // 5 navigation tabs - screen-fit design
        val tabs = listOf(
            "📊\nDashboard" to { showDashboard() },
            "📱\nDevices" to { showDevices() },
            "📈\nAnalytics" to { showAnalytics() },
            "🔧\nTools" to { showTools() },
            "⚙️\nSettings" to { showSettings() }
        )

        tabs.forEach { (label, action) ->
            val tabButton = TextView(this).apply {
                text = label
                textSize = 12f
                setTextColor(Color.WHITE)
                gravity = Gravity.CENTER
                layoutParams = LayoutParams(0, LayoutParams.WRAP_CONTENT, 1f)
                setPadding(8, 12, 8, 12)
                setOnClickListener { action() }
                background = ContextCompat.getDrawable(context, android.R.drawable.btn_default)
            }
            navigationLayout.addView(tabButton)
        }
    }

    private fun showDashboard() {
        contentLayout.removeAllViews()

        val scrollView = ScrollView(this).apply {
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
        }

        val dashboardLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            setPadding(24, 24, 24, 24)
        }

        // Welcome message
        val welcomeText = TextView(this).apply {
            text = "Welcome back! Your network is being monitored 24/7"
            textSize = 18f
            setTextColor(Color.parseColor("#1976D2"))
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            setPadding(0, 0, 0, 24)
        }

        // Network status cards
        val statusCards = listOf(
            "🌐 Network Status: SECURE" to "All devices monitored",
            "🔍 Active Scans: 5" to "Real-time monitoring",
            "⚠️ Threats Detected: 0" to "Network is clean",
            "📊 Connected Devices: 12" to "All devices identified"
        )

        dashboardLayout.addView(welcomeText)

        statusCards.forEach { (title, subtitle) ->
            val cardLayout = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                setBackgroundColor(Color.parseColor("#E3F2FD"))
                setPadding(20, 16, 20, 16)
                val margins = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                margins.setMargins(0, 0, 0, 16)
                layoutParams = margins
            }

            val titleView = TextView(this).apply {
                text = title
                textSize = 16f
                setTextColor(Color.parseColor("#1976D2"))
            }

            val subtitleView = TextView(this).apply {
                text = subtitle
                textSize = 14f
                setTextColor(Color.parseColor("#666666"))
            }

            cardLayout.addView(titleView)
            cardLayout.addView(subtitleView)
            dashboardLayout.addView(cardLayout)
        }

        scrollView.addView(dashboardLayout)
        contentLayout.addView(scrollView)
    }

    private fun showDevices() {
        contentLayout.removeAllViews()

        val devicesLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
            setPadding(24, 24, 24, 24)
        }

        val titleText = TextView(this).apply {
            text = "📱 CONNECTED DEVICES"
            textSize = 20f
            setTextColor(Color.parseColor("#1976D2"))
            setPadding(0, 0, 0, 24)
        }

        val devicesList = listOf(
            "📱 iPhone 15 Pro" to "************* • Apple Inc.",
            "💻 MacBook Pro" to "************* • Apple Inc.",
            "📺 Samsung TV" to "************* • Samsung Electronics",
            "🖨️ HP Printer" to "************* • Hewlett-Packard",
            "🎮 PlayStation 5" to "************* • Sony Interactive"
        )

        devicesLayout.addView(titleText)

        devicesList.forEach { (device, info) ->
            val deviceCard = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                setBackgroundColor(Color.parseColor("#E8F5E8"))
                setPadding(20, 16, 20, 16)
                val margins = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                margins.setMargins(0, 0, 0, 12)
                layoutParams = margins
            }

            val deviceName = TextView(this).apply {
                text = device
                textSize = 16f
                setTextColor(Color.parseColor("#2E7D32"))
            }

            val deviceInfo = TextView(this).apply {
                text = info
                textSize = 14f
                setTextColor(Color.parseColor("#666666"))
            }

            deviceCard.addView(deviceName)
            deviceCard.addView(deviceInfo)
            devicesLayout.addView(deviceCard)
        }

        contentLayout.addView(devicesLayout)
    }

    private fun showAnalytics() {
        contentLayout.removeAllViews()

        val analyticsLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
            setPadding(24, 24, 24, 24)
        }

        val titleText = TextView(this).apply {
            text = "📈 NETWORK ANALYTICS"
            textSize = 20f
            setTextColor(Color.parseColor("#1976D2"))
            setPadding(0, 0, 0, 24)
        }

        val analyticsData = listOf(
            "📊 Total Bandwidth Usage" to "2.4 GB today",
            "⏱️ Average Connection Time" to "8.5 hours per device",
            "🚀 Network Speed" to "150 Mbps download",
            "🔒 Security Score" to "98% - Excellent",
            "📱 Most Active Device" to "MacBook Pro (1.2 GB)"
        )

        analyticsLayout.addView(titleText)

        analyticsData.forEach { (metric, value) ->
            val metricCard = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                setBackgroundColor(Color.parseColor("#FFF3E0"))
                setPadding(20, 16, 20, 16)
                val margins = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                margins.setMargins(0, 0, 0, 12)
                layoutParams = margins
            }

            val metricName = TextView(this).apply {
                text = metric
                textSize = 16f
                setTextColor(Color.parseColor("#F57C00"))
            }

            val metricValue = TextView(this).apply {
                text = value
                textSize = 14f
                setTextColor(Color.parseColor("#666666"))
            }

            metricCard.addView(metricName)
            metricCard.addView(metricValue)
            analyticsLayout.addView(metricCard)
        }

        contentLayout.addView(analyticsLayout)
    }

    private fun showTools() {
        contentLayout.removeAllViews()

        val toolsLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
            setPadding(24, 24, 24, 24)
        }

        val titleText = TextView(this).apply {
            text = "🔧 NETWORK TOOLS"
            textSize = 20f
            setTextColor(Color.parseColor("#1976D2"))
            setPadding(0, 0, 0, 24)
        }

        val tools = listOf(
            "🔍 Network Scanner" to "Scan for new devices",
            "🛡️ Security Check" to "Run vulnerability assessment",
            "📊 Speed Test" to "Test network performance",
            "🚫 Block Device" to "Manage device access",
            "📋 Export Report" to "Generate network report"
        )

        toolsLayout.addView(titleText)

        tools.forEach { (tool, description) ->
            val toolCard = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                setBackgroundColor(Color.parseColor("#F3E5F5"))
                setPadding(20, 16, 20, 16)
                val margins = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                margins.setMargins(0, 0, 0, 12)
                layoutParams = margins
                setOnClickListener {
                    Toast.makeText(context, "Opening $tool", Toast.LENGTH_SHORT).show()
                }
            }

            val toolName = TextView(this).apply {
                text = tool
                textSize = 16f
                setTextColor(Color.parseColor("#7B1FA2"))
            }

            val toolDesc = TextView(this).apply {
                text = description
                textSize = 14f
                setTextColor(Color.parseColor("#666666"))
            }

            toolCard.addView(toolName)
            toolCard.addView(toolDesc)
            toolsLayout.addView(toolCard)
        }

        contentLayout.addView(toolsLayout)
    }

    private fun showSettings() {
        contentLayout.removeAllViews()

        val settingsLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
            setPadding(24, 24, 24, 24)
        }

        val titleText = TextView(this).apply {
            text = "⚙️ SETTINGS"
            textSize = 20f
            setTextColor(Color.parseColor("#1976D2"))
            setPadding(0, 0, 0, 24)
        }

        val settings = listOf(
            "🔔 Notifications" to "Manage alert preferences",
            "🔒 Security Level" to "Configure protection settings",
            "📊 Data Storage" to "Cloud sync and local storage",
            "🌐 API Configuration" to "Manage security APIs",
            "ℹ️ About" to "App version and information"
        )

        settingsLayout.addView(titleText)

        settings.forEach { (setting, description) ->
            val settingCard = LinearLayout(this).apply {
                orientation = LinearLayout.VERTICAL
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                setBackgroundColor(Color.parseColor("#EFEBE9"))
                setPadding(20, 16, 20, 16)
                val margins = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
                margins.setMargins(0, 0, 0, 12)
                layoutParams = margins
                setOnClickListener {
                    Toast.makeText(context, "Opening $setting", Toast.LENGTH_SHORT).show()
                }
            }

            val settingName = TextView(this).apply {
                text = setting
                textSize = 16f
                setTextColor(Color.parseColor("#5D4037"))
            }

            val settingDesc = TextView(this).apply {
                text = description
                textSize = 14f
                setTextColor(Color.parseColor("#666666"))
            }

            settingCard.addView(settingName)
            settingCard.addView(settingDesc)
            settingsLayout.addView(settingCard)
        }

        contentLayout.addView(settingsLayout)
    }

    private fun showNotifications() {
        Toast.makeText(this, "🔔 Notifications: All systems secure!", Toast.LENGTH_LONG).show()
    }
}
