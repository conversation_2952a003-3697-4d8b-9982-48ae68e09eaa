package com.zilal.networkguardian

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.dp
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.Surface
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.model.ScanResult
import com.zilal.networkguardian.service.DeviceMonitorService
import com.zilal.networkguardian.service.NetworkScannerService
import com.zilal.networkguardian.ui.AboutScreen
import com.zilal.networkguardian.ui.AirborneScanScreen
import com.zilal.networkguardian.ui.ApiTestScreen
import com.zilal.networkguardian.ui.BehavioralAnalysisScreen
import com.zilal.networkguardian.ui.ComprehensiveScanScreen
import com.zilal.networkguardian.ui.DashboardScreen
import com.zilal.networkguardian.ui.enhanced.ProductionDashboard
import com.zilal.networkguardian.ui.DevicesToolScreen
import com.zilal.networkguardian.ui.DockerConfigScreen
import com.zilal.networkguardian.ui.NetworkMapScreen
import com.zilal.networkguardian.ui.HiddenNetworkScreen
import com.zilal.networkguardian.ui.AIAnalysisScreen
import com.zilal.networkguardian.ui.PingToolScreen
import com.zilal.networkguardian.ui.ScanDetailScreen
import com.zilal.networkguardian.ui.ScanHistoryScreen
import com.zilal.networkguardian.ui.ScanScreen
import com.zilal.networkguardian.ui.ScanTypeSelectionScreen
import com.zilal.networkguardian.ui.SettingsScreen
import com.zilal.networkguardian.ui.UpdateScreen
import com.zilal.networkguardian.ui.UserGuideScreen
import com.zilal.networkguardian.ui.WirelessSecurityScreen
import com.zilal.networkguardian.ui.screens.DeviceApprovalScreen
import com.zilal.networkguardian.ui.screens.WiFiConnectionScreen
// import com.zilal.networkguardian.ui.enhanced.EnhancedDashboardScreen
// import com.zilal.networkguardian.ui.enhanced.AdvancedDeviceManagementScreen
// import com.zilal.networkguardian.ui.enhanced.SecurityMonitoringScreen
// import com.zilal.networkguardian.ui.enhanced.AdvancedNetworkToolsScreen
import com.zilal.networkguardian.service.DeviceMonitoringService
import com.zilal.networkguardian.ui.components.NavigationDrawer
import com.zilal.networkguardian.ui.theme.ZilalTheme
import com.zilal.networkguardian.util.WifiController
import com.zilal.networkguardian.viewmodel.ScanViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    // Required permissions
    private val requiredPermissions = mutableListOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.ACCESS_WIFI_STATE,
        Manifest.permission.ACCESS_NETWORK_STATE,
        Manifest.permission.CHANGE_WIFI_STATE,
        Manifest.permission.CHANGE_NETWORK_STATE,
        Manifest.permission.CHANGE_WIFI_MULTICAST_STATE,
        Manifest.permission.INTERNET
    ).apply {
        // Add notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            add(Manifest.permission.POST_NOTIFICATIONS)
        }

        // Add Bluetooth permissions for Android 12+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            add(Manifest.permission.BLUETOOTH_SCAN)
            add(Manifest.permission.BLUETOOTH_CONNECT)
            add(Manifest.permission.BLUETOOTH_ADVERTISE)
        }
    }

    // Permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            startNetworkScannerService()
        } else {
            Toast.makeText(
                this,
                "Some permissions were denied. App functionality may be limited.",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        val splashScreen = installSplashScreen()

        // Keep the splash screen visible until we've checked WiFi connectivity
        splashScreen.setKeepOnScreenCondition { true }

        super.onCreate(savedInstanceState)

        // Check and request permissions
        checkAndRequestPermissions()

        // Start the device monitoring service
        startDeviceMonitoringService()

        // Set the initial screen to Device Management for testing
        intent.putExtra("NAVIGATE_TO", "device_approval")

        // Initialize WiFi controller to check connectivity
        val wifiController = WifiController(this)

        setContent {
            val isDarkTheme = rememberSaveable { mutableStateOf(true) }

            // Check WiFi connectivity immediately
            LaunchedEffect(Unit) {
                wifiController.checkWifiConnection()
                // Allow the splash screen to be removed after checking
                splashScreen.setKeepOnScreenCondition { false }
            }

            // Handle navigation from notifications
            val initialScreen = remember {
                when (intent?.getStringExtra("NAVIGATE_TO")) {
                    "device_approval" -> Screen.DeviceApproval
                    "scan_history" -> Screen.ScanHistory
                    else -> Screen.Dashboard
                }
            }

            ZilalTheme(darkTheme = isDarkTheme.value) {
                // A surface container using the 'background' color from the theme
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background,
                ) {
                    ZilalApp(
                        initialScreen = initialScreen,
                        onThemeToggle = { isDarkTheme.value = it }
                    )
                }
            }
        }
    }

    /**
     * Check and request necessary permissions
     */
    private fun checkAndRequestPermissions() {
        val permissionsToRequest = requiredPermissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()

        if (permissionsToRequest.isNotEmpty()) {
            requestPermissionLauncher.launch(permissionsToRequest)
        } else {
            // All permissions are already granted
            startNetworkScannerService()
        }
    }

    /**
     * Start the network scanner service and device monitor service
     */
    private fun startNetworkScannerService() {
        // Start network scanner service
        val scannerIntent = Intent(this, NetworkScannerService::class.java).apply {
            action = NetworkScannerService.ACTION_START_SCANNING
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(scannerIntent)
        } else {
            startService(scannerIntent)
        }

        // Start device monitor service for new device notifications
        val monitorIntent = Intent(this, DeviceMonitorService::class.java).apply {
            action = DeviceMonitorService.ACTION_START_MONITORING
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(monitorIntent)
        } else {
            startService(monitorIntent)
        }
    }

    /**
     * Start the device monitoring service for trusted devices
     */
    private fun startDeviceMonitoringService() {
        val serviceIntent = Intent(this, DeviceMonitoringService::class.java).apply {
            action = DeviceMonitoringService.ACTION_START_MONITORING
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ZilalApp(
    scanViewModel: ScanViewModel = viewModel(),
    initialScreen: Screen = Screen.Dashboard,
    onThemeToggle: (Boolean) -> Unit
) {
    val context = LocalContext.current
    val wifiController = remember { WifiController(context) }
    val isConnectedToWifi by wifiController.isConnectedToWifi.collectAsStateWithLifecycle()
    var showWifiScreen by remember { mutableStateOf(!isConnectedToWifi) }

    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
    val scope = rememberCoroutineScope()
    // Use the provided initial screen or default to Dashboard
    var currentScreen by remember { mutableStateOf<Screen>(initialScreen) }
    var isDarkTheme by remember { mutableStateOf(true) }

    // Check WiFi connection on startup and whenever isConnectedToWifi changes
    LaunchedEffect(isConnectedToWifi) {
        showWifiScreen = !isConnectedToWifi
    }

    // Periodically check WiFi connection
    LaunchedEffect(Unit) {
        while(true) {
            wifiController.checkWifiConnection()
            delay(5000) // Check every 5 seconds
        }
    }

    // Show WiFi connection screen if not connected
    if (showWifiScreen) {
        WiFiConnectionScreen(
            onWifiConnected = {
                showWifiScreen = false
            }
        )
        return
    }

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                NavigationDrawer(
                    drawerState = drawerState,
                    scope = scope,
                    onThemeToggle = {
                        isDarkTheme = it
                        onThemeToggle(it)
                    },
                    isDarkTheme = isDarkTheme,
                    onNavigate = { destination ->
                        when (destination) {
                            "scan_selection" -> currentScreen = Screen.ScanTypeSelection
                            "internal_scan" -> currentScreen = Screen.Scan(ScanResult.ScanType.INTERNAL_NETWORK)
                            "router_security" -> currentScreen = Screen.RouterSecurity
                            "vulnerability_scan" -> currentScreen = Screen.VulnerabilityScan
                            "traffic_analysis" -> currentScreen = Screen.TrafficAnalysis
                            "tool_ping" -> currentScreen = Screen.ToolPing
                            "tool_devices" -> currentScreen = Screen.ToolDevices
                            "settings" -> currentScreen = Screen.Settings
                            "about" -> currentScreen = Screen.About
                            "scan_history" -> currentScreen = Screen.ScanHistory
                            "user_guide" -> currentScreen = Screen.UserGuide
                            "device_approval" -> currentScreen = Screen.DeviceApproval
                            "update" -> currentScreen = Screen.Update
                            else -> currentScreen = Screen.Dashboard
                        }
                    }
                )
            }
        }
    ) {
        when (val screen = currentScreen) {
            is Screen.Dashboard -> {
                // Use Production Dashboard with enhanced features
                Column {
                    // Top App Bar
                    TopAppBar(
                        title = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "Zilal",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "🛡️",
                                    fontSize = 20.sp
                                )
                            }
                        },
                        navigationIcon = {
                            IconButton(
                                onClick = {
                                    scope.launch {
                                        drawerState.open()
                                    }
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Menu,
                                    contentDescription = "Menu"
                                )
                            }
                        },
                        actions = {
                            IconButton(
                                onClick = { /* TODO: Notifications */ }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Notifications,
                                    contentDescription = "Notifications"
                                )
                            }
                        }
                    )

                    // Production Dashboard Content
                    ProductionDashboard()
                }
            }
            is Screen.Scan -> {
                ScanScreen(
                    scanType = screen.scanType,
                    scanViewModel = scanViewModel,
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }
            is Screen.ToolPing -> {
                PingToolScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }
            is Screen.ToolDevices -> {
                DevicesToolScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }
            is Screen.About -> {
                AboutScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }
            is Screen.RouterSecurity -> {
                // Router Security Screen - placeholder for now
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Router Security",
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Text(
                            text = "Analyzing router security settings...",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { currentScreen = Screen.Dashboard }
                        ) {
                            Text("Back to Dashboard")
                        }
                    }
                }
            }

            is Screen.VulnerabilityScan -> {
                // Vulnerability Scan Screen - placeholder for now
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Vulnerability Scan",
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Text(
                            text = "Scanning for security vulnerabilities...",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { currentScreen = Screen.Dashboard }
                        ) {
                            Text("Back to Dashboard")
                        }
                    }
                }
            }

            is Screen.TrafficAnalysis -> {
                // Traffic Analysis Screen - placeholder for now
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Traffic Analysis",
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Text(
                            text = "Analyzing network traffic patterns...",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { currentScreen = Screen.Dashboard }
                        ) {
                            Text("Back to Dashboard")
                        }
                    }
                }
            }

            is Screen.ScanHistory -> {
                ScanHistoryScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    },
                    onScanSelected = { scanId ->
                        currentScreen = Screen.ScanDetail(scanId)
                    }
                )
            }

            is Screen.ScanDetail -> {
                ScanDetailScreen(
                    scanId = screen.scanId,
                    onNavigateBack = {
                        currentScreen = Screen.ScanHistory
                    }
                )
            }

            is Screen.Settings -> {
                SettingsScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }

            is Screen.UserGuide -> {
                UserGuideScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }

            is Screen.DeviceApproval -> {
                DeviceApprovalScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }

            is Screen.ScanTypeSelection -> {
                ScanTypeSelectionScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    },
                    onScanTypeSelected = { scanType ->
                        currentScreen = Screen.Scan(scanType)
                    }
                )
            }

            is Screen.Update -> {
                UpdateScreen(
                    onNavigateBack = {
                        currentScreen = Screen.Dashboard
                    }
                )
            }


        }
    }
}

/**
 * Represents the different screens in the app
 */
sealed class Screen {
    object Dashboard : Screen()
    data class Scan(val scanType: ScanResult.ScanType) : Screen()
    object ScanTypeSelection : Screen()
    object RouterSecurity : Screen()
    object VulnerabilityScan : Screen()
    object TrafficAnalysis : Screen()
    object ToolPing : Screen()
    object ToolDevices : Screen()
    object About : Screen()
    object ScanHistory : Screen()
    data class ScanDetail(val scanId: String) : Screen()
    object Settings : Screen()
    object UserGuide : Screen()
    object DeviceApproval : Screen()
    object Update : Screen()
}