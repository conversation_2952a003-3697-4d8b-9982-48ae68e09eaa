package com.zilal.networkguardian.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.model.*
import com.zilal.networkguardian.database.TrustedDevice
import com.zilal.networkguardian.database.TrustedDeviceDao
import com.zilal.networkguardian.database.Converters

/**
 * Main application database with authentication and analytics
 */
@Database(
    entities = [
        // Authentication entities
        User::class,
        UserSession::class,
        UserAnalytics::class,
        ScanAnalytics::class,
        AIInsight::class,
        BehaviorPattern::class,
        UsageStatistics::class,
        DeviceAnalytics::class,
        // Existing entities
        TrustedDevice::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {

    // Authentication DAO
    abstract fun authDao(): AuthDao
    
    // Existing DAOs
    abstract fun trustedDeviceDao(): TrustedDeviceDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "securenet_database"
                )
                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigration() // For development only
                .build()
                INSTANCE = instance
                instance
            }
        }

        /**
         * Migration from version 1 to 2 (adding authentication tables)
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Create users table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS users (
                        id TEXT PRIMARY KEY NOT NULL,
                        email TEXT NOT NULL,
                        username TEXT NOT NULL,
                        passwordHash TEXT NOT NULL,
                        firstName TEXT,
                        lastName TEXT,
                        profileImageUrl TEXT,
                        role TEXT NOT NULL DEFAULT 'USER',
                        isEmailVerified INTEGER NOT NULL DEFAULT 0,
                        isActive INTEGER NOT NULL DEFAULT 1,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL,
                        lastLoginAt INTEGER,
                        loginCount INTEGER NOT NULL DEFAULT 0,
                        preferences TEXT,
                        analyticsEnabled INTEGER NOT NULL DEFAULT 1,
                        aiInsightsEnabled INTEGER NOT NULL DEFAULT 1,
                        subscriptionType TEXT NOT NULL DEFAULT 'FREE',
                        subscriptionExpiresAt INTEGER
                    )
                """)

                // Create user_sessions table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        sessionId TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        deviceId TEXT NOT NULL,
                        deviceName TEXT NOT NULL,
                        deviceType TEXT NOT NULL,
                        ipAddress TEXT,
                        userAgent TEXT,
                        isActive INTEGER NOT NULL DEFAULT 1,
                        createdAt INTEGER NOT NULL,
                        lastActivityAt INTEGER NOT NULL,
                        expiresAt INTEGER NOT NULL,
                        location TEXT,
                        appVersion TEXT,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create user_analytics table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS user_analytics (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        sessionId TEXT NOT NULL,
                        eventType TEXT NOT NULL,
                        eventName TEXT NOT NULL,
                        eventData TEXT,
                        screenName TEXT,
                        timestamp INTEGER NOT NULL,
                        duration INTEGER,
                        networkType TEXT,
                        batteryLevel INTEGER,
                        memoryUsage INTEGER,
                        cpuUsage REAL,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY(sessionId) REFERENCES user_sessions(sessionId) ON DELETE CASCADE
                    )
                """)

                // Create scan_analytics table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS scan_analytics (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        scanId TEXT NOT NULL,
                        scanType TEXT NOT NULL,
                        targetRange TEXT NOT NULL,
                        devicesFound INTEGER NOT NULL,
                        vulnerabilitiesFound INTEGER NOT NULL,
                        scanDuration INTEGER NOT NULL,
                        scanSuccess INTEGER NOT NULL,
                        errorMessage TEXT,
                        networkConditions TEXT,
                        deviceTypes TEXT,
                        securityScore INTEGER,
                        timestamp INTEGER NOT NULL,
                        location TEXT,
                        networkName TEXT,
                        signalStrength INTEGER,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create ai_insights table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS ai_insights (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        insightType TEXT NOT NULL,
                        title TEXT NOT NULL,
                        description TEXT NOT NULL,
                        recommendation TEXT,
                        severity TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        dataPoints TEXT,
                        isRead INTEGER NOT NULL DEFAULT 0,
                        isActioned INTEGER NOT NULL DEFAULT 0,
                        createdAt INTEGER NOT NULL,
                        expiresAt INTEGER,
                        relatedScanId TEXT,
                        relatedDeviceId TEXT,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create behavior_patterns table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS behavior_patterns (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        patternType TEXT NOT NULL,
                        patternData TEXT NOT NULL,
                        frequency INTEGER NOT NULL,
                        lastOccurrence INTEGER NOT NULL,
                        confidence REAL NOT NULL,
                        isAnomaly INTEGER NOT NULL DEFAULT 0,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create usage_statistics table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS usage_statistics (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        date TEXT NOT NULL,
                        sessionCount INTEGER NOT NULL DEFAULT 0,
                        totalUsageTime INTEGER NOT NULL DEFAULT 0,
                        scansPerformed INTEGER NOT NULL DEFAULT 0,
                        devicesDiscovered INTEGER NOT NULL DEFAULT 0,
                        vulnerabilitiesFound INTEGER NOT NULL DEFAULT 0,
                        featuresUsed TEXT,
                        mostUsedFeature TEXT,
                        averageSessionDuration INTEGER NOT NULL DEFAULT 0,
                        crashCount INTEGER NOT NULL DEFAULT 0,
                        errorCount INTEGER NOT NULL DEFAULT 0,
                        performanceScore REAL NOT NULL DEFAULT 0.0,
                        batteryUsage REAL NOT NULL DEFAULT 0.0,
                        dataUsage INTEGER NOT NULL DEFAULT 0,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create device_analytics table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS device_analytics (
                        id TEXT PRIMARY KEY NOT NULL,
                        userId TEXT NOT NULL,
                        deviceId TEXT NOT NULL,
                        deviceName TEXT,
                        deviceType TEXT NOT NULL,
                        macAddress TEXT,
                        ipAddress TEXT NOT NULL,
                        manufacturer TEXT,
                        firstSeen INTEGER NOT NULL,
                        lastSeen INTEGER NOT NULL,
                        totalConnections INTEGER NOT NULL DEFAULT 1,
                        averageConnectionDuration INTEGER NOT NULL DEFAULT 0,
                        dataTransferred INTEGER NOT NULL DEFAULT 0,
                        securityRisk TEXT NOT NULL DEFAULT 'LOW',
                        behaviorPattern TEXT,
                        isKnownDevice INTEGER NOT NULL DEFAULT 0,
                        isTrusted INTEGER NOT NULL DEFAULT 0,
                        notes TEXT,
                        FOREIGN KEY(userId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create indexes for better performance
                database.execSQL("CREATE INDEX IF NOT EXISTS index_users_email ON users(email)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_users_username ON users(username)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_user_sessions_userId ON user_sessions(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_user_analytics_userId ON user_analytics(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_user_analytics_timestamp ON user_analytics(timestamp)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_scan_analytics_userId ON scan_analytics(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_ai_insights_userId ON ai_insights(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_behavior_patterns_userId ON behavior_patterns(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_usage_statistics_userId ON usage_statistics(userId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_device_analytics_userId ON device_analytics(userId)")
            }
        }
    }
}

// Type converters are defined in TrustedDevice.kt to avoid duplication
