package com.zilal.networkguardian.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.zilal.networkguardian.R
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.ui.mobile.MobileMainActivity
import java.text.SimpleDateFormat
import java.util.*

/**
 * Enhanced Notification Manager for Real-Time Network Security Alerts
 * Features:
 * - Real-time threat notifications
 * - New device discovery alerts
 * - Security score changes
 * - Network anomaly detection
 * - Customizable notification preferences
 */
class EnhancedNotificationManager(private val context: Context) {
    
    companion object {
        private const val CHANNEL_ID_SECURITY = "security_alerts"
        private const val CHANNEL_ID_DEVICES = "device_discovery"
        private const val CHANNEL_ID_NETWORK = "network_status"
        private const val CHANNEL_ID_ANALYTICS = "analytics_updates"
        
        private const val NOTIFICATION_ID_SECURITY = 1001
        private const val NOTIFICATION_ID_DEVICE = 1002
        private const val NOTIFICATION_ID_NETWORK = 1003
        private const val NOTIFICATION_ID_ANALYTICS = 1004
    }
    
    private val notificationManager = NotificationManagerCompat.from(context)
    
    init {
        createNotificationChannels()
    }
    
    /**
     * Create notification channels for different types of alerts
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_ID_SECURITY,
                    "Security Alerts",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Critical security threats and vulnerabilities"
                    enableVibration(true)
                    enableLights(true)
                },
                
                NotificationChannel(
                    CHANNEL_ID_DEVICES,
                    "Device Discovery",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "New devices discovered on your network"
                    enableVibration(true)
                },
                
                NotificationChannel(
                    CHANNEL_ID_NETWORK,
                    "Network Status",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Network performance and connectivity updates"
                },
                
                NotificationChannel(
                    CHANNEL_ID_ANALYTICS,
                    "Analytics Updates",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Network analytics and performance reports"
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { systemNotificationManager.createNotificationChannel(it) }
        }
    }
    
    /**
     * Show security threat notification
     */
    fun showSecurityThreatNotification(
        threatTitle: String,
        threatDescription: String,
        severity: ThreatSeverity,
        deviceIP: String? = null
    ) {
        val intent = Intent(context, MobileMainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", "security")
            putExtra("threat_ip", deviceIP)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_SECURITY)
            .setSmallIcon(R.drawable.ic_security_alert)
            .setContentTitle("🚨 Security Alert: $threatTitle")
            .setContentText(threatDescription)
            .setStyle(NotificationCompat.BigTextStyle().bigText(
                buildString {
                    append(threatDescription)
                    if (deviceIP != null) {
                        append("\n\nAffected Device: $deviceIP")
                    }
                    append("\n\nTap to view details and take action.")
                }
            ))
            .setPriority(when (severity) {
                ThreatSeverity.CRITICAL -> NotificationCompat.PRIORITY_MAX
                ThreatSeverity.HIGH -> NotificationCompat.PRIORITY_HIGH
                ThreatSeverity.MEDIUM -> NotificationCompat.PRIORITY_DEFAULT
                ThreatSeverity.LOW -> NotificationCompat.PRIORITY_LOW
            })
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(when (severity) {
                ThreatSeverity.CRITICAL -> 0xFFD32F2F.toInt()
                ThreatSeverity.HIGH -> 0xFFF57C00.toInt()
                ThreatSeverity.MEDIUM -> 0xFFFBC02D.toInt()
                ThreatSeverity.LOW -> 0xFF388E3C.toInt()
            })
            .addAction(
                R.drawable.ic_security,
                "View Details",
                pendingIntent
            )
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_SECURITY, notification)
    }
    
    /**
     * Show new device discovery notification
     */
    fun showNewDeviceNotification(device: NetworkDevice) {
        val intent = Intent(context, MobileMainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", "devices")
            putExtra("device_ip", device.ipAddress)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val deviceName = device.customName ?: device.hostname ?: "Unknown Device"
        val deviceType = device.deviceType?.name ?: "Unknown Type"
        val manufacturer = device.manufacturer ?: "Unknown Manufacturer"
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_DEVICES)
            .setSmallIcon(R.drawable.ic_device_discovery)
            .setContentTitle("📱 New Device Discovered")
            .setContentText("$deviceName connected to your network")
            .setStyle(NotificationCompat.BigTextStyle().bigText(
                buildString {
                    append("Device: $deviceName\n")
                    append("Type: $deviceType\n")
                    append("Manufacturer: $manufacturer\n")
                    append("IP Address: ${device.ipAddress}\n")
                    if (device.macAddress != null) {
                        append("MAC: ${device.macAddress}\n")
                    }
                    append("\nTap to view device details and manage settings.")
                }
            ))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(0xFF1976D2.toInt())
            .addAction(
                R.drawable.ic_devices,
                "View Device",
                pendingIntent
            )
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_DEVICE, notification)
    }
    
    /**
     * Show network status change notification
     */
    fun showNetworkStatusNotification(
        title: String,
        message: String,
        statusType: NetworkStatusType
    ) {
        val intent = Intent(context, MobileMainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", "dashboard")
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_NETWORK)
            .setSmallIcon(when (statusType) {
                NetworkStatusType.PERFORMANCE_ISSUE -> R.drawable.ic_network_slow
                NetworkStatusType.CONNECTIVITY_ISSUE -> R.drawable.ic_network_offline
                NetworkStatusType.SECURITY_CHANGE -> R.drawable.ic_security
                NetworkStatusType.CONFIGURATION_CHANGE -> R.drawable.ic_settings
            })
            .setContentTitle("🌐 Network Status: $title")
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(
                "$message\n\nTimestamp: ${getCurrentTimestamp()}\n\nTap to view network details."
            ))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(0xFF00796B.toInt())
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_NETWORK, notification)
    }
    
    /**
     * Show analytics update notification
     */
    fun showAnalyticsUpdateNotification(
        deviceCount: Int,
        securityScore: Int,
        newThreats: Int,
        dataUsageMB: Int
    ) {
        val intent = Intent(context, MobileMainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", "analytics")
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ANALYTICS)
            .setSmallIcon(R.drawable.ic_analytics)
            .setContentTitle("📊 Network Analytics Update")
            .setContentText("$deviceCount devices • Security: $securityScore%")
            .setStyle(NotificationCompat.BigTextStyle().bigText(
                buildString {
                    append("Network Analytics Summary:\n\n")
                    append("• Devices: $deviceCount\n")
                    append("• Security Score: $securityScore%\n")
                    append("• New Threats: $newThreats\n")
                    append("• Data Usage: ${dataUsageMB}MB\n")
                    append("\nGenerated: ${getCurrentTimestamp()}")
                }
            ))
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(0xFF7B1FA2.toInt())
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_ANALYTICS, notification)
    }
    
    /**
     * Show vulnerability detection notification
     */
    fun showVulnerabilityNotification(
        vulnerabilityName: String,
        cvssScore: Float,
        affectedDevices: List<String>
    ) {
        val severity = when {
            cvssScore >= 9.0 -> ThreatSeverity.CRITICAL
            cvssScore >= 7.0 -> ThreatSeverity.HIGH
            cvssScore >= 4.0 -> ThreatSeverity.MEDIUM
            else -> ThreatSeverity.LOW
        }
        
        val description = buildString {
            append("CVSS Score: $cvssScore\n")
            append("Affected devices: ${affectedDevices.size}\n")
            if (affectedDevices.isNotEmpty()) {
                append("Devices: ${affectedDevices.take(3).joinToString(", ")}")
                if (affectedDevices.size > 3) {
                    append(" and ${affectedDevices.size - 3} more")
                }
            }
        }
        
        showSecurityThreatNotification(
            threatTitle = vulnerabilityName,
            threatDescription = description,
            severity = severity
        )
    }
    
    /**
     * Clear all notifications
     */
    fun clearAllNotifications() {
        notificationManager.cancelAll()
    }
    
    /**
     * Clear specific notification type
     */
    fun clearNotification(notificationType: NotificationType) {
        val notificationId = when (notificationType) {
            NotificationType.SECURITY -> NOTIFICATION_ID_SECURITY
            NotificationType.DEVICE -> NOTIFICATION_ID_DEVICE
            NotificationType.NETWORK -> NOTIFICATION_ID_NETWORK
            NotificationType.ANALYTICS -> NOTIFICATION_ID_ANALYTICS
        }
        notificationManager.cancel(notificationId)
    }
    
    /**
     * Get current timestamp for notifications
     */
    private fun getCurrentTimestamp(): String {
        val formatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        return formatter.format(Date())
    }
}

/**
 * Enums for notification types and severity levels
 */
enum class ThreatSeverity {
    CRITICAL, HIGH, MEDIUM, LOW
}

enum class NetworkStatusType {
    PERFORMANCE_ISSUE, CONNECTIVITY_ISSUE, SECURITY_CHANGE, CONFIGURATION_CHANGE
}

enum class NotificationType {
    SECURITY, DEVICE, NETWORK, ANALYTICS
}

/**
 * Notification preferences data class
 */
data class NotificationPreferences(
    val securityAlertsEnabled: Boolean = true,
    val deviceDiscoveryEnabled: Boolean = true,
    val networkStatusEnabled: Boolean = true,
    val analyticsUpdatesEnabled: Boolean = false,
    val quietHoursEnabled: Boolean = false,
    val quietHoursStart: String = "22:00",
    val quietHoursEnd: String = "08:00",
    val vibrationEnabled: Boolean = true,
    val soundEnabled: Boolean = true
)
