package com.zilal.networkguardian.auth.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

/**
 * User entity for authentication and analytics
 */
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val email: String,
    val username: String,
    val passwordHash: String,
    val firstName: String? = null,
    val lastName: String? = null,
    val profileImageUrl: String? = null,
    val role: UserRole = UserRole.USER,
    val isEmailVerified: Boolean = false,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastLoginAt: Long? = null,
    val loginCount: Int = 0,
    val preferences: String? = null, // JSON string for user preferences
    val analyticsEnabled: Boolean = true,
    val aiInsightsEnabled: Boolean = true,
    val subscriptionType: SubscriptionType = SubscriptionType.FREE,
    val subscriptionExpiresAt: Long? = null
)

enum class UserRole {
    USER,
    ADMIN,
    ANALYST,
    ENTERPRISE
}

enum class SubscriptionType {
    FREE,
    PREMIUM,
    ENTERPRISE,
    LIFETIME
}

/**
 * User session for tracking active sessions
 */
@Entity(tableName = "user_sessions")
data class UserSession(
    @PrimaryKey
    val sessionId: String = UUID.randomUUID().toString(),
    val userId: String,
    val deviceId: String,
    val deviceName: String,
    val deviceType: String,
    val ipAddress: String? = null,
    val userAgent: String? = null,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val lastActivityAt: Long = System.currentTimeMillis(),
    val expiresAt: Long = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L), // 30 days
    val location: String? = null,
    val appVersion: String? = null
)

/**
 * User analytics data for AI and intelligence
 */
@Entity(tableName = "user_analytics")
data class UserAnalytics(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val sessionId: String,
    val eventType: AnalyticsEventType,
    val eventName: String,
    val eventData: String? = null, // JSON string for event data
    val screenName: String? = null,
    val timestamp: Long = System.currentTimeMillis(),
    val duration: Long? = null,
    val networkType: String? = null,
    val batteryLevel: Int? = null,
    val memoryUsage: Long? = null,
    val cpuUsage: Float? = null
)

enum class AnalyticsEventType {
    APP_LAUNCH,
    APP_CLOSE,
    SCREEN_VIEW,
    BUTTON_CLICK,
    SCAN_START,
    SCAN_COMPLETE,
    DEVICE_DISCOVERED,
    VULNERABILITY_FOUND,
    SETTINGS_CHANGED,
    FEATURE_USED,
    ERROR_OCCURRED,
    PERFORMANCE_METRIC,
    USER_INTERACTION,
    AI_INSIGHT_GENERATED,
    SECURITY_ALERT
}

/**
 * Network scan analytics for AI analysis
 */
@Entity(tableName = "scan_analytics")
data class ScanAnalytics(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val scanId: String,
    val scanType: String,
    val targetRange: String,
    val devicesFound: Int,
    val vulnerabilitiesFound: Int,
    val scanDuration: Long,
    val scanSuccess: Boolean,
    val errorMessage: String? = null,
    val networkConditions: String? = null, // JSON string
    val deviceTypes: String? = null, // JSON string of device type counts
    val securityScore: Int? = null,
    val timestamp: Long = System.currentTimeMillis(),
    val location: String? = null,
    val networkName: String? = null,
    val signalStrength: Int? = null
)

/**
 * AI insights and recommendations
 */
@Entity(tableName = "ai_insights")
data class AIInsight(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val insightType: AIInsightType,
    val title: String,
    val description: String,
    val recommendation: String? = null,
    val severity: InsightSeverity,
    val confidence: Float, // 0.0 to 1.0
    val dataPoints: String? = null, // JSON string of supporting data
    val isRead: Boolean = false,
    val isActioned: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val expiresAt: Long? = null,
    val relatedScanId: String? = null,
    val relatedDeviceId: String? = null
)

enum class AIInsightType {
    SECURITY_RECOMMENDATION,
    PERFORMANCE_OPTIMIZATION,
    DEVICE_BEHAVIOR_ANOMALY,
    NETWORK_PATTERN_ANALYSIS,
    THREAT_PREDICTION,
    USAGE_OPTIMIZATION,
    COST_SAVING,
    MAINTENANCE_ALERT
}

enum class InsightSeverity {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

/**
 * User behavior patterns for AI analysis
 */
@Entity(tableName = "behavior_patterns")
data class BehaviorPattern(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val patternType: BehaviorPatternType,
    val patternData: String, // JSON string of pattern data
    val frequency: Int,
    val lastOccurrence: Long,
    val confidence: Float,
    val isAnomaly: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

enum class BehaviorPatternType {
    SCAN_FREQUENCY,
    USAGE_TIME,
    FEATURE_PREFERENCE,
    NETWORK_SWITCHING,
    SECURITY_AWARENESS,
    DEVICE_MANAGEMENT,
    ALERT_RESPONSE
}

/**
 * App usage statistics for analytics
 */
@Entity(tableName = "usage_statistics")
data class UsageStatistics(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val date: String, // YYYY-MM-DD format
    val sessionCount: Int = 0,
    val totalUsageTime: Long = 0, // in milliseconds
    val scansPerformed: Int = 0,
    val devicesDiscovered: Int = 0,
    val vulnerabilitiesFound: Int = 0,
    val featuresUsed: String? = null, // JSON array of feature names
    val mostUsedFeature: String? = null,
    val averageSessionDuration: Long = 0,
    val crashCount: Int = 0,
    val errorCount: Int = 0,
    val performanceScore: Float = 0.0f,
    val batteryUsage: Float = 0.0f,
    val dataUsage: Long = 0L,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Device analytics for network intelligence
 */
@Entity(tableName = "device_analytics")
data class DeviceAnalytics(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val deviceId: String,
    val deviceName: String? = null,
    val deviceType: String,
    val macAddress: String? = null,
    val ipAddress: String,
    val manufacturer: String? = null,
    val firstSeen: Long = System.currentTimeMillis(),
    val lastSeen: Long = System.currentTimeMillis(),
    val totalConnections: Int = 1,
    val averageConnectionDuration: Long = 0,
    val dataTransferred: Long = 0,
    val securityRisk: SecurityRiskLevel = SecurityRiskLevel.LOW,
    val behaviorPattern: String? = null, // JSON string
    val isKnownDevice: Boolean = false,
    val isTrusted: Boolean = false,
    val notes: String? = null
)

enum class SecurityRiskLevel {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL,
    UNKNOWN
}
