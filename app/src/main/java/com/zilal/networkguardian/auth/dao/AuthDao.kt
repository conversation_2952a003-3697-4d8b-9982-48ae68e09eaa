package com.zilal.networkguardian.auth.dao

import androidx.room.*
import com.zilal.networkguardian.auth.model.*
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for authentication and user management
 */
@Dao
interface AuthDao {
    
    // User operations
    @Query("SELECT * FROM users WHERE email = :email LIMIT 1")
    suspend fun getUserByEmail(email: String): User?
    
    @Query("SELECT * FROM users WHERE username = :username LIMIT 1")
    suspend fun getUserByUsername(username: String): User?
    
    @Query("SELECT * FROM users WHERE id = :userId LIMIT 1")
    suspend fun getUserById(userId: String): User?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User): Long
    
    @Update
    suspend fun updateUser(user: User)
    
    @Query("UPDATE users SET lastLoginAt = :timestamp, loginCount = loginCount + 1 WHERE id = :userId")
    suspend fun updateLastLogin(userId: String, timestamp: Long)
    
    @Query("UPDATE users SET isActive = :isActive WHERE id = :userId")
    suspend fun updateUserActiveStatus(userId: String, isActive: Boolean)
    
    @Query("SELECT * FROM users WHERE isActive = 1")
    fun getAllActiveUsers(): Flow<List<User>>
    
    // Session operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: UserSession): Long
    
    @Query("SELECT * FROM user_sessions WHERE sessionId = :sessionId AND isActive = 1 LIMIT 1")
    suspend fun getActiveSession(sessionId: String): UserSession?
    
    @Query("SELECT * FROM user_sessions WHERE userId = :userId AND isActive = 1")
    suspend fun getActiveSessionsForUser(userId: String): List<UserSession>
    
    @Query("UPDATE user_sessions SET isActive = 0 WHERE sessionId = :sessionId")
    suspend fun deactivateSession(sessionId: String)
    
    @Query("UPDATE user_sessions SET isActive = 0 WHERE userId = :userId")
    suspend fun deactivateAllUserSessions(userId: String)
    
    @Query("UPDATE user_sessions SET lastActivityAt = :timestamp WHERE sessionId = :sessionId")
    suspend fun updateSessionActivity(sessionId: String, timestamp: Long)
    
    @Query("DELETE FROM user_sessions WHERE expiresAt < :currentTime OR isActive = 0")
    suspend fun cleanupExpiredSessions(currentTime: Long)
    
    // Analytics operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnalyticsEvent(analytics: UserAnalytics): Long
    
    @Query("SELECT * FROM user_analytics WHERE userId = :userId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getUserAnalytics(userId: String, limit: Int = 100): List<UserAnalytics>
    
    @Query("SELECT * FROM user_analytics WHERE userId = :userId AND eventType = :eventType ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getUserAnalyticsByType(userId: String, eventType: AnalyticsEventType, limit: Int = 50): List<UserAnalytics>
    
    @Query("SELECT COUNT(*) FROM user_analytics WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getAnalyticsCountSince(userId: String, startTime: Long): Int
    
    @Query("DELETE FROM user_analytics WHERE timestamp < :cutoffTime")
    suspend fun cleanupOldAnalytics(cutoffTime: Long)
    
    // Scan analytics operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScanAnalytics(scanAnalytics: ScanAnalytics): Long
    
    @Query("SELECT * FROM scan_analytics WHERE userId = :userId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getUserScanAnalytics(userId: String, limit: Int = 50): List<ScanAnalytics>
    
    @Query("SELECT AVG(scanDuration) FROM scan_analytics WHERE userId = :userId AND scanSuccess = 1")
    suspend fun getAverageScanDuration(userId: String): Double?
    
    @Query("SELECT COUNT(*) FROM scan_analytics WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getScanCountSince(userId: String, startTime: Long): Int
    
    @Query("SELECT SUM(devicesFound) FROM scan_analytics WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getTotalDevicesFoundSince(userId: String, startTime: Long): Int?
    
    @Query("SELECT SUM(vulnerabilitiesFound) FROM scan_analytics WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getTotalVulnerabilitiesFoundSince(userId: String, startTime: Long): Int?
    
    // AI insights operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAIInsight(insight: AIInsight): Long
    
    @Query("SELECT * FROM ai_insights WHERE userId = :userId AND isRead = 0 ORDER BY createdAt DESC")
    suspend fun getUnreadInsights(userId: String): List<AIInsight>
    
    @Query("SELECT * FROM ai_insights WHERE userId = :userId ORDER BY createdAt DESC LIMIT :limit")
    suspend fun getUserInsights(userId: String, limit: Int = 20): List<AIInsight>
    
    @Query("UPDATE ai_insights SET isRead = 1 WHERE id = :insightId")
    suspend fun markInsightAsRead(insightId: String)
    
    @Query("UPDATE ai_insights SET isActioned = 1 WHERE id = :insightId")
    suspend fun markInsightAsActioned(insightId: String)
    
    @Query("DELETE FROM ai_insights WHERE expiresAt < :currentTime")
    suspend fun cleanupExpiredInsights(currentTime: Long)
    
    // Behavior patterns operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBehaviorPattern(pattern: BehaviorPattern): Long
    
    @Query("SELECT * FROM behavior_patterns WHERE userId = :userId")
    suspend fun getUserBehaviorPatterns(userId: String): List<BehaviorPattern>
    
    @Query("SELECT * FROM behavior_patterns WHERE userId = :userId AND patternType = :type LIMIT 1")
    suspend fun getBehaviorPattern(userId: String, type: BehaviorPatternType): BehaviorPattern?
    
    @Update
    suspend fun updateBehaviorPattern(pattern: BehaviorPattern)
    
    // Usage statistics operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUsageStatistics(stats: UsageStatistics): Long
    
    @Query("SELECT * FROM usage_statistics WHERE userId = :userId AND date = :date LIMIT 1")
    suspend fun getUsageStatisticsForDate(userId: String, date: String): UsageStatistics?
    
    @Query("SELECT * FROM usage_statistics WHERE userId = :userId ORDER BY date DESC LIMIT :limit")
    suspend fun getUserUsageStatistics(userId: String, limit: Int = 30): List<UsageStatistics>
    
    @Update
    suspend fun updateUsageStatistics(stats: UsageStatistics)
    
    // Device analytics operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeviceAnalytics(deviceAnalytics: DeviceAnalytics): Long
    
    @Query("SELECT * FROM device_analytics WHERE userId = :userId ORDER BY lastSeen DESC")
    suspend fun getUserDeviceAnalytics(userId: String): List<DeviceAnalytics>
    
    @Query("SELECT * FROM device_analytics WHERE userId = :userId AND deviceId = :deviceId LIMIT 1")
    suspend fun getDeviceAnalytics(userId: String, deviceId: String): DeviceAnalytics?
    
    @Update
    suspend fun updateDeviceAnalytics(deviceAnalytics: DeviceAnalytics)
    
    @Query("UPDATE device_analytics SET lastSeen = :timestamp, totalConnections = totalConnections + 1 WHERE userId = :userId AND deviceId = :deviceId")
    suspend fun updateDeviceConnection(userId: String, deviceId: String, timestamp: Long)
    
    // Advanced analytics queries
    @Query("""
        SELECT eventType, COUNT(*) as count 
        FROM user_analytics 
        WHERE userId = :userId AND timestamp >= :startTime 
        GROUP BY eventType
    """)
    suspend fun getEventTypeCounts(userId: String, startTime: Long): List<EventTypeCount>
    
    @Query("""
        SELECT date, SUM(totalUsageTime) as totalTime, SUM(scansPerformed) as totalScans
        FROM usage_statistics 
        WHERE userId = :userId 
        ORDER BY date DESC 
        LIMIT :days
    """)
    suspend fun getDailyUsageTrends(userId: String, days: Int = 7): List<DailyUsageTrend>
    
    @Query("""
        SELECT deviceType, COUNT(*) as count, AVG(securityRisk) as avgRisk
        FROM device_analytics 
        WHERE userId = :userId 
        GROUP BY deviceType
    """)
    suspend fun getDeviceTypeAnalytics(userId: String): List<DeviceTypeAnalytics>
}

// Data classes for complex query results
data class EventTypeCount(
    val eventType: AnalyticsEventType,
    val count: Int
)

data class DailyUsageTrend(
    val date: String,
    val totalTime: Long,
    val totalScans: Int
)

data class DeviceTypeAnalytics(
    val deviceType: String,
    val count: Int,
    val avgRisk: Float
)
