package com.zilal.networkguardian.auth.repository

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.model.*
import com.zilal.networkguardian.auth.service.PasswordHasher
import com.zilal.networkguardian.auth.service.SessionManager
import com.zilal.networkguardian.auth.service.AnalyticsService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton
import java.util.*

/**
 * Repository for authentication and user management with analytics
 */
@Singleton
class AuthRepository @Inject constructor(
    private val authDao: AuthDao,
    private val passwordHasher: PasswordHasher,
    private val sessionManager: SessionManager,
    private val analyticsService: AnalyticsService,
    private val context: Context
) {
    
    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()
    
    private val _isAuthenticated = MutableStateFlow(false)
    val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val encryptedPrefs: SharedPreferences by lazy {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
        
        EncryptedSharedPreferences.create(
            context,
            "auth_prefs",
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    init {
        // Check for existing session on init
        repositoryScope.launch {
            checkExistingSession()
        }
    }
    
    /**
     * Register a new user
     */
    suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String? = null,
        lastName: String? = null
    ): AuthResult {
        try {
            _authState.value = AuthState.Loading
            
            // Check if user already exists
            val existingUserByEmail = authDao.getUserByEmail(email)
            if (existingUserByEmail != null) {
                _authState.value = AuthState.Error("Email already registered")
                return AuthResult.Error("Email already registered")
            }
            
            val existingUserByUsername = authDao.getUserByUsername(username)
            if (existingUserByUsername != null) {
                _authState.value = AuthState.Error("Username already taken")
                return AuthResult.Error("Username already taken")
            }
            
            // Create new user
            val hashedPassword = passwordHasher.hashPassword(password)
            val user = User(
                email = email,
                username = username,
                passwordHash = hashedPassword,
                firstName = firstName,
                lastName = lastName,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            
            // Save user to database
            authDao.insertUser(user)
            
            // Create session
            val session = createUserSession(user)
            
            // Update state
            _currentUser.value = user
            _isAuthenticated.value = true
            _authState.value = AuthState.Authenticated(user)
            
            // Save session locally
            saveSessionLocally(session)
            
            // Track registration analytics
            analyticsService.trackEvent(
                userId = user.id,
                sessionId = session.sessionId,
                eventType = AnalyticsEventType.APP_LAUNCH,
                eventName = "user_registered",
                eventData = """{"registration_method": "email", "has_name": ${firstName != null}}"""
            )
            
            return AuthResult.Success(user)
            
        } catch (e: Exception) {
            _authState.value = AuthState.Error(e.message ?: "Registration failed")
            return AuthResult.Error(e.message ?: "Registration failed")
        }
    }
    
    /**
     * Login user
     */
    suspend fun login(emailOrUsername: String, password: String): AuthResult {
        try {
            _authState.value = AuthState.Loading
            
            // Find user by email or username
            val user = authDao.getUserByEmail(emailOrUsername) 
                ?: authDao.getUserByUsername(emailOrUsername)
            
            if (user == null) {
                _authState.value = AuthState.Error("User not found")
                return AuthResult.Error("User not found")
            }
            
            if (!user.isActive) {
                _authState.value = AuthState.Error("Account is deactivated")
                return AuthResult.Error("Account is deactivated")
            }
            
            // Verify password
            if (!passwordHasher.verifyPassword(password, user.passwordHash)) {
                _authState.value = AuthState.Error("Invalid password")
                return AuthResult.Error("Invalid password")
            }
            
            // Update last login
            authDao.updateLastLogin(user.id, System.currentTimeMillis())
            
            // Create session
            val session = createUserSession(user)
            
            // Update state
            val updatedUser = user.copy(
                lastLoginAt = System.currentTimeMillis(),
                loginCount = user.loginCount + 1
            )
            _currentUser.value = updatedUser
            _isAuthenticated.value = true
            _authState.value = AuthState.Authenticated(updatedUser)
            
            // Save session locally
            saveSessionLocally(session)
            
            // Track login analytics
            analyticsService.trackEvent(
                userId = user.id,
                sessionId = session.sessionId,
                eventType = AnalyticsEventType.APP_LAUNCH,
                eventName = "user_login",
                eventData = """{"login_method": "password", "login_count": ${user.loginCount + 1}}"""
            )
            
            return AuthResult.Success(updatedUser)
            
        } catch (e: Exception) {
            _authState.value = AuthState.Error(e.message ?: "Login failed")
            return AuthResult.Error(e.message ?: "Login failed")
        }
    }
    
    /**
     * Logout user
     */
    suspend fun logout() {
        try {
            val currentSession = getCurrentSession()
            val currentUserId = _currentUser.value?.id
            
            // Deactivate current session
            currentSession?.let { session ->
                authDao.deactivateSession(session.sessionId)
                
                // Track logout analytics
                currentUserId?.let { userId ->
                    analyticsService.trackEvent(
                        userId = userId,
                        sessionId = session.sessionId,
                        eventType = AnalyticsEventType.APP_CLOSE,
                        eventName = "user_logout"
                    )
                }
            }
            
            // Clear local session
            clearSessionLocally()
            
            // Update state
            _currentUser.value = null
            _isAuthenticated.value = false
            _authState.value = AuthState.Unauthenticated
            
        } catch (e: Exception) {
            // Even if logout fails, clear local state
            clearSessionLocally()
            _currentUser.value = null
            _isAuthenticated.value = false
            _authState.value = AuthState.Unauthenticated
        }
    }
    
    /**
     * Check existing session on app start
     */
    private suspend fun checkExistingSession() {
        try {
            val sessionId = encryptedPrefs.getString("session_id", null)
            if (sessionId != null) {
                val session = authDao.getActiveSession(sessionId)
                if (session != null && session.expiresAt > System.currentTimeMillis()) {
                    val user = authDao.getUserById(session.userId)
                    if (user != null && user.isActive) {
                        // Update session activity
                        authDao.updateSessionActivity(sessionId, System.currentTimeMillis())
                        
                        // Restore user state
                        _currentUser.value = user
                        _isAuthenticated.value = true
                        _authState.value = AuthState.Authenticated(user)
                        
                        // Track session restore
                        analyticsService.trackEvent(
                            userId = user.id,
                            sessionId = sessionId,
                            eventType = AnalyticsEventType.APP_LAUNCH,
                            eventName = "session_restored"
                        )
                        
                        return
                    }
                }
            }
            
            // No valid session found
            _authState.value = AuthState.Unauthenticated
            
        } catch (e: Exception) {
            _authState.value = AuthState.Error(e.message ?: "Session check failed")
        }
    }
    
    /**
     * Create user session
     */
    private suspend fun createUserSession(user: User): UserSession {
        val session = UserSession(
            userId = user.id,
            deviceId = getDeviceId(),
            deviceName = getDeviceName(),
            deviceType = "Android",
            ipAddress = getDeviceIpAddress(),
            userAgent = getUserAgent(),
            appVersion = getAppVersion()
        )
        
        authDao.insertSession(session)
        return session
    }
    
    /**
     * Get current session
     */
    private suspend fun getCurrentSession(): UserSession? {
        val sessionId = encryptedPrefs.getString("session_id", null)
        return if (sessionId != null) {
            authDao.getActiveSession(sessionId)
        } else null
    }
    
    /**
     * Save session locally
     */
    private fun saveSessionLocally(session: UserSession) {
        encryptedPrefs.edit()
            .putString("session_id", session.sessionId)
            .putString("user_id", session.userId)
            .apply()
    }
    
    /**
     * Clear local session
     */
    private fun clearSessionLocally() {
        encryptedPrefs.edit()
            .remove("session_id")
            .remove("user_id")
            .apply()
    }
    
    /**
     * Update user profile
     */
    suspend fun updateProfile(
        firstName: String? = null,
        lastName: String? = null,
        profileImageUrl: String? = null
    ): AuthResult {
        try {
            val currentUser = _currentUser.value ?: return AuthResult.Error("Not authenticated")
            
            val updatedUser = currentUser.copy(
                firstName = firstName ?: currentUser.firstName,
                lastName = lastName ?: currentUser.lastName,
                profileImageUrl = profileImageUrl ?: currentUser.profileImageUrl,
                updatedAt = System.currentTimeMillis()
            )
            
            authDao.updateUser(updatedUser)
            _currentUser.value = updatedUser
            
            // Track profile update
            getCurrentSession()?.let { session ->
                analyticsService.trackEvent(
                    userId = currentUser.id,
                    sessionId = session.sessionId,
                    eventType = AnalyticsEventType.SETTINGS_CHANGED,
                    eventName = "profile_updated"
                )
            }
            
            return AuthResult.Success(updatedUser)
            
        } catch (e: Exception) {
            return AuthResult.Error(e.message ?: "Profile update failed")
        }
    }
    
    /**
     * Change password
     */
    suspend fun changePassword(currentPassword: String, newPassword: String): AuthResult {
        try {
            val user = _currentUser.value ?: return AuthResult.Error("Not authenticated")
            
            // Verify current password
            if (!passwordHasher.verifyPassword(currentPassword, user.passwordHash)) {
                return AuthResult.Error("Current password is incorrect")
            }
            
            // Hash new password
            val newHashedPassword = passwordHasher.hashPassword(newPassword)
            
            // Update user
            val updatedUser = user.copy(
                passwordHash = newHashedPassword,
                updatedAt = System.currentTimeMillis()
            )
            
            authDao.updateUser(updatedUser)
            _currentUser.value = updatedUser
            
            // Track password change
            getCurrentSession()?.let { session ->
                analyticsService.trackEvent(
                    userId = user.id,
                    sessionId = session.sessionId,
                    eventType = AnalyticsEventType.SETTINGS_CHANGED,
                    eventName = "password_changed"
                )
            }
            
            return AuthResult.Success(updatedUser)
            
        } catch (e: Exception) {
            return AuthResult.Error(e.message ?: "Password change failed")
        }
    }
    
    // Helper methods
    private fun getDeviceId(): String = android.provider.Settings.Secure.getString(
        context.contentResolver,
        android.provider.Settings.Secure.ANDROID_ID
    ) ?: UUID.randomUUID().toString()
    
    private fun getDeviceName(): String = android.os.Build.MODEL ?: "Unknown Device"
    
    private fun getDeviceIpAddress(): String? {
        // Implementation to get device IP address
        return null // Placeholder
    }
    
    private fun getUserAgent(): String = "SecureNet Pro Android/${getAppVersion()}"
    
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
}

/**
 * Authentication result sealed class
 */
sealed class AuthResult {
    data class Success(val user: User) : AuthResult()
    data class Error(val message: String) : AuthResult()
}

/**
 * Authentication state sealed class
 */
sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User) : AuthState()
    data class Error(val message: String) : AuthState()
}
