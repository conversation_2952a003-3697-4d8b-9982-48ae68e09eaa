package com.zilal.networkguardian.auth.service

import java.security.MessageDigest
import java.security.SecureRandom
import javax.crypto.spec.PBEKeySpec
import javax.crypto.SecretKeyFactory
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.text.Charsets.UTF_8

/**
 * Secure password hashing service using PBKDF2
 */
@Singleton
class PasswordHasher @Inject constructor() {
    
    companion object {
        private const val ALGORITHM = "PBKDF2WithHmacSHA256"
        private const val ITERATIONS = 100000
        private const val KEY_LENGTH = 256
        private const val SALT_LENGTH = 32
    }
    
    /**
     * Hash a password with salt
     */
    fun hashPassword(password: String): String {
        val salt = generateSalt()
        val hash = hashPasswordWithSalt(password, salt)
        return "${salt.toHex()}:${hash.toHex()}"
    }
    
    /**
     * Verify a password against a hash
     */
    fun verifyPassword(password: String, hashedPassword: String): Boolean {
        return try {
            val parts = hashedPassword.split(":")
            if (parts.size != 2) return false
            
            val salt = parts[0].fromHex()
            val hash = parts[1].fromHex()
            
            val testHash = hashPasswordWithSalt(password, salt)
            hash.contentEquals(testHash)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Generate a random salt
     */
    private fun generateSalt(): ByteArray {
        val salt = ByteArray(SALT_LENGTH)
        SecureRandom().nextBytes(salt)
        return salt
    }
    
    /**
     * Hash password with given salt
     */
    private fun hashPasswordWithSalt(password: String, salt: ByteArray): ByteArray {
        val spec = PBEKeySpec(password.toCharArray(), salt, ITERATIONS, KEY_LENGTH)
        val factory = SecretKeyFactory.getInstance(ALGORITHM)
        return factory.generateSecret(spec).encoded
    }
    
    /**
     * Convert byte array to hex string
     */
    private fun ByteArray.toHex(): String {
        return joinToString("") { "%02x".format(it) }
    }
    
    /**
     * Convert hex string to byte array
     */
    private fun String.fromHex(): ByteArray {
        return chunked(2).map { it.toInt(16).toByte() }.toByteArray()
    }
}

/**
 * Session management service
 */
@Singleton
class SessionManager @Inject constructor() {
    
    /**
     * Generate a secure session token
     */
    fun generateSessionToken(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return bytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * Validate session token format
     */
    fun isValidSessionToken(token: String): Boolean {
        return token.length == 64 && token.all { it.isDigit() || it in 'a'..'f' }
    }
    
    /**
     * Check if session is expired
     */
    fun isSessionExpired(expiresAt: Long): Boolean {
        return System.currentTimeMillis() > expiresAt
    }
    
    /**
     * Calculate session expiry time
     */
    fun calculateExpiryTime(durationMillis: Long = 30 * 24 * 60 * 60 * 1000L): Long {
        return System.currentTimeMillis() + durationMillis
    }
}
