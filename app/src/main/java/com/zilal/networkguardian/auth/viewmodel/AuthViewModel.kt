package com.zilal.networkguardian.auth.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zilal.networkguardian.auth.model.User
import com.zilal.networkguardian.auth.repository.AuthRepository
import com.zilal.networkguardian.auth.repository.AuthResult
import com.zilal.networkguardian.auth.repository.AuthState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for authentication operations
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    // Auth state from repository
    val authState: StateFlow<AuthState> = authRepository.authState
    val currentUser: StateFlow<User?> = authRepository.currentUser
    val isAuthenticated: StateFlow<Boolean> = authRepository.isAuthenticated
    
    // Loading state for UI
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // Error messages
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // Success messages
    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()
    
    /**
     * Login user
     */
    fun login(emailOrUsername: String, password: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = authRepository.login(emailOrUsername.trim(), password)
                
                when (result) {
                    is AuthResult.Success -> {
                        _successMessage.value = "Welcome back, ${result.user.firstName ?: result.user.username}!"
                        clearMessages()
                    }
                    is AuthResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Login failed"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Register new user
     */
    fun register(
        email: String,
        username: String,
        password: String,
        confirmPassword: String,
        firstName: String? = null,
        lastName: String? = null
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                // Validate inputs
                val validationError = validateRegistrationInputs(
                    email, username, password, confirmPassword
                )
                if (validationError != null) {
                    _errorMessage.value = validationError
                    return@launch
                }
                
                val result = authRepository.register(
                    email = email.trim(),
                    username = username.trim(),
                    password = password,
                    firstName = firstName?.trim(),
                    lastName = lastName?.trim()
                )
                
                when (result) {
                    is AuthResult.Success -> {
                        _successMessage.value = "Account created successfully! Welcome to SecureNet Pro."
                        clearMessages()
                    }
                    is AuthResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Registration failed"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Logout user
     */
    fun logout() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                authRepository.logout()
                _successMessage.value = "Logged out successfully"
                clearMessages()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Logout failed"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Update user profile
     */
    fun updateProfile(
        firstName: String? = null,
        lastName: String? = null,
        profileImageUrl: String? = null
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = authRepository.updateProfile(
                    firstName = firstName?.trim(),
                    lastName = lastName?.trim(),
                    profileImageUrl = profileImageUrl
                )
                
                when (result) {
                    is AuthResult.Success -> {
                        _successMessage.value = "Profile updated successfully"
                        clearMessages()
                    }
                    is AuthResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Profile update failed"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Change password
     */
    fun changePassword(currentPassword: String, newPassword: String, confirmPassword: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                // Validate passwords
                if (newPassword != confirmPassword) {
                    _errorMessage.value = "New passwords don't match"
                    return@launch
                }
                
                if (newPassword.length < 8) {
                    _errorMessage.value = "Password must be at least 8 characters long"
                    return@launch
                }
                
                val result = authRepository.changePassword(currentPassword, newPassword)
                
                when (result) {
                    is AuthResult.Success -> {
                        _successMessage.value = "Password changed successfully"
                        clearMessages()
                    }
                    is AuthResult.Error -> {
                        _errorMessage.value = result.message
                    }
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Password change failed"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Clear error and success messages
     */
    fun clearMessages() {
        viewModelScope.launch {
            kotlinx.coroutines.delay(3000) // Clear after 3 seconds
            _errorMessage.value = null
            _successMessage.value = null
        }
    }
    
    /**
     * Clear error message immediately
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * Clear success message immediately
     */
    fun clearSuccess() {
        _successMessage.value = null
    }
    
    /**
     * Validate registration inputs
     */
    private fun validateRegistrationInputs(
        email: String,
        username: String,
        password: String,
        confirmPassword: String
    ): String? {
        // Email validation
        if (email.isBlank()) {
            return "Email is required"
        }
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            return "Please enter a valid email address"
        }
        
        // Username validation
        if (username.isBlank()) {
            return "Username is required"
        }
        if (username.length < 3) {
            return "Username must be at least 3 characters long"
        }
        if (!username.matches(Regex("^[a-zA-Z0-9_]+$"))) {
            return "Username can only contain letters, numbers, and underscores"
        }
        
        // Password validation
        if (password.isBlank()) {
            return "Password is required"
        }
        if (password.length < 8) {
            return "Password must be at least 8 characters long"
        }
        if (!password.matches(Regex(".*[A-Z].*"))) {
            return "Password must contain at least one uppercase letter"
        }
        if (!password.matches(Regex(".*[a-z].*"))) {
            return "Password must contain at least one lowercase letter"
        }
        if (!password.matches(Regex(".*[0-9].*"))) {
            return "Password must contain at least one number"
        }
        
        // Confirm password validation
        if (password != confirmPassword) {
            return "Passwords don't match"
        }
        
        return null
    }
    
    /**
     * Validate login inputs
     */
    fun validateLoginInputs(emailOrUsername: String, password: String): String? {
        if (emailOrUsername.isBlank()) {
            return "Email or username is required"
        }
        if (password.isBlank()) {
            return "Password is required"
        }
        return null
    }
    
    /**
     * Check if user is authenticated
     */
    fun checkAuthStatus() {
        // Auth status is automatically managed by the repository
        // This method can be used to trigger a manual check if needed
    }
    
    /**
     * Get user display name
     */
    fun getUserDisplayName(): String {
        val user = currentUser.value
        return when {
            !user?.firstName.isNullOrBlank() && !user?.lastName.isNullOrBlank() -> 
                "${user?.firstName} ${user?.lastName}"
            !user?.firstName.isNullOrBlank() -> user?.firstName ?: ""
            else -> user?.username ?: "User"
        }
    }
    
    /**
     * Get user initials for avatar
     */
    fun getUserInitials(): String {
        val user = currentUser.value
        return when {
            !user?.firstName.isNullOrBlank() && !user?.lastName.isNullOrBlank() -> 
                "${user?.firstName?.first()}${user?.lastName?.first()}".uppercase()
            !user?.firstName.isNullOrBlank() -> 
                user?.firstName?.take(2)?.uppercase() ?: ""
            else -> 
                user?.username?.take(2)?.uppercase() ?: "U"
        }
    }
    
    /**
     * Check if user has premium subscription
     */
    fun isPremiumUser(): Boolean {
        val user = currentUser.value
        return user?.subscriptionType != com.zilal.networkguardian.auth.model.SubscriptionType.FREE
    }
    
    /**
     * Check if user is admin
     */
    fun isAdmin(): Boolean {
        val user = currentUser.value
        return user?.role == com.zilal.networkguardian.auth.model.UserRole.ADMIN
    }
}
