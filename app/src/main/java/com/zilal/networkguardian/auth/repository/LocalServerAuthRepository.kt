package com.zilal.networkguardian.auth.repository

import com.zilal.networkguardian.api.LocalApiClient
import com.zilal.networkguardian.api.LocalRegisterRequest
import com.zilal.networkguardian.api.LocalLoginRequest
import com.zilal.networkguardian.api.LocalAnalyticsEvent
import com.zilal.networkguardian.api.LocalCreateScanRequest
import com.zilal.networkguardian.api.LocalUpdateScanRequest
import com.zilal.networkguardian.api.LocalDeviceRequest
import com.zilal.networkguardian.auth.model.User
import com.zilal.networkguardian.auth.model.UserRole
import com.zilal.networkguardian.auth.model.SubscriptionType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Authentication repository that integrates with local development server
 */
@Singleton
class LocalServerAuthRepository @Inject constructor(
    private val localApiClient: LocalApiClient
) : IAuthRepository {
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    override val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _currentUser = MutableStateFlow<User?>(null)
    override val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    private val _isAuthenticated = MutableStateFlow(false)
    override val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()
    
    init {
        // Check if user is already authenticated
        checkExistingSession()
    }
    
    private fun checkExistingSession() {
        if (localApiClient.hasValidToken()) {
            _authState.value = AuthState.Loading
            // In a real implementation, we would verify the token with the server
            // For now, we'll assume it's valid if it exists
            _isAuthenticated.value = true
            // Create a placeholder user for now
            val placeholderUser = User(
                id = localApiClient.getUserId() ?: "unknown",
                email = "<EMAIL>",
                username = "testuser",
                passwordHash = "",
                firstName = "Test",
                lastName = "User",
                profileImageUrl = null,
                role = UserRole.USER,
                isEmailVerified = true,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastLoginAt = System.currentTimeMillis(),
                loginCount = 1,
                preferences = null,
                analyticsEnabled = true,
                aiInsightsEnabled = true,
                subscriptionType = SubscriptionType.PREMIUM,
                subscriptionExpiresAt = null
            )
            _currentUser.value = placeholderUser
            _authState.value = AuthState.Authenticated(placeholderUser)
        } else {
            _authState.value = AuthState.Unauthenticated
            _isAuthenticated.value = false
        }
    }
    
    /**
     * Register new user with local server
     */
    override suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String?,
        lastName: String?
    ): AuthResult {
        return try {
            _authState.value = AuthState.Loading
            
            val request = LocalRegisterRequest(
                email = email,
                username = username,
                password = password,
                firstName = firstName,
                lastName = lastName
            )
            
            val response = localApiClient.apiService.register(request)
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                // Save token
                localApiClient.saveToken(authResponse.token, authResponse.user.id)
                
                // Convert to our User model
                val user = convertToUser(authResponse.user)
                _currentUser.value = user
                _isAuthenticated.value = true
                _authState.value = AuthState.Authenticated(user)
                
                // Track registration event
                trackEvent("user_registered", "account_created", mapOf(
                    "registration_method" to "email",
                    "has_name" to (firstName != null)
                ))
                
                AuthResult.Success(user)
            } else {
                val errorMessage = response.errorBody()?.string() ?: "Registration failed"
                _authState.value = AuthState.Error(errorMessage)
                AuthResult.Error(errorMessage)
            }
            
        } catch (e: Exception) {
            val errorMessage = e.message ?: "Network error during registration"
            _authState.value = AuthState.Error(errorMessage)
            AuthResult.Error(errorMessage)
        }
    }
    
    /**
     * Login user with local server
     */
    override suspend fun login(emailOrUsername: String, password: String): AuthResult {
        return try {
            _authState.value = AuthState.Loading
            
            val request = LocalLoginRequest(
                emailOrUsername = emailOrUsername,
                password = password
            )
            
            val response = localApiClient.apiService.login(request)
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                // Save token
                localApiClient.saveToken(authResponse.token, authResponse.user.id)
                
                // Convert to our User model
                val user = convertToUser(authResponse.user)
                _currentUser.value = user
                _isAuthenticated.value = true
                _authState.value = AuthState.Authenticated(user)
                
                // Track login event
                trackEvent("user_login", "login_success", mapOf(
                    "login_method" to "password"
                ))
                
                AuthResult.Success(user)
            } else {
                val errorMessage = response.errorBody()?.string() ?: "Login failed"
                _authState.value = AuthState.Error(errorMessage)
                AuthResult.Error(errorMessage)
            }
            
        } catch (e: Exception) {
            val errorMessage = e.message ?: "Network error during login"
            _authState.value = AuthState.Error(errorMessage)
            AuthResult.Error(errorMessage)
        }
    }
    
    /**
     * Logout user
     */
    override suspend fun logout() {
        try {
            // Track logout event before clearing token
            trackEvent("user_logout", "logout_success")
            
            // Clear local tokens
            localApiClient.clearTokens()
            
            // Update state
            _currentUser.value = null
            _isAuthenticated.value = false
            _authState.value = AuthState.Unauthenticated
            
        } catch (e: Exception) {
            // Even if tracking fails, we should still logout locally
            localApiClient.clearTokens()
            _currentUser.value = null
            _isAuthenticated.value = false
            _authState.value = AuthState.Unauthenticated
        }
    }
    
    /**
     * Track analytics event
     */
    suspend fun trackEvent(
        eventType: String,
        eventName: String,
        eventData: Map<String, Any>? = null,
        screenName: String? = null,
        duration: Long? = null
    ) {
        try {
            if (!localApiClient.hasValidToken()) return
            
            val event = LocalAnalyticsEvent(
                eventType = eventType,
                eventName = eventName,
                eventData = eventData,
                screenName = screenName,
                duration = duration,
                networkType = getNetworkType(),
                batteryLevel = getBatteryLevel(),
                memoryUsage = getMemoryUsage(),
                cpuUsage = getCpuUsage()
            )
            
            localApiClient.apiService.trackEvent(event)
        } catch (e: Exception) {
            // Don't crash app if analytics fails
            e.printStackTrace()
        }
    }
    
    /**
     * Track network scan
     */
    suspend fun trackNetworkScan(
        scanId: String,
        scanType: String,
        targetRange: String,
        devicesFound: Int = 0,
        vulnerabilitiesFound: Int = 0,
        scanDurationMs: Long = 0,
        success: Boolean = false,
        errorMessage: String? = null,
        scanResults: Map<String, Any>? = null
    ) {
        try {
            if (!localApiClient.hasValidToken()) return
            
            // Create scan record
            val createRequest = LocalCreateScanRequest(
                scanId = scanId,
                scanType = scanType,
                targetRange = targetRange
            )
            
            val createResponse = localApiClient.apiService.createScan(createRequest)
            
            if (createResponse.isSuccessful) {
                val serverScanId = createResponse.body()?.scanId
                
                // Update scan with results
                if (serverScanId != null) {
                    val updateRequest = LocalUpdateScanRequest(
                        devicesFound = devicesFound,
                        vulnerabilitiesFound = vulnerabilitiesFound,
                        scanDuration = scanDurationMs,
                        success = success,
                        errorMessage = errorMessage,
                        scanResults = scanResults
                    )
                    
                    localApiClient.apiService.updateScan(serverScanId, updateRequest)
                }
            }
            
            // Track analytics event
            trackEvent(
                eventType = "network_scan",
                eventName = if (success) "scan_completed" else "scan_failed",
                eventData = mapOf(
                    "scan_type" to scanType,
                    "devices_found" to devicesFound,
                    "vulnerabilities_found" to vulnerabilitiesFound,
                    "duration_ms" to scanDurationMs,
                    "success" to success
                ),
                duration = scanDurationMs
            )
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Track discovered device
     */
    suspend fun trackDiscoveredDevice(
        deviceId: String,
        deviceName: String? = null,
        ipAddress: String,
        macAddress: String? = null,
        deviceType: String = "unknown",
        manufacturer: String? = null,
        securityRisk: String = "low",
        isKnownDevice: Boolean = false
    ) {
        try {
            if (!localApiClient.hasValidToken()) return
            
            val deviceRequest = LocalDeviceRequest(
                deviceId = deviceId,
                deviceName = deviceName,
                ipAddress = ipAddress,
                macAddress = macAddress,
                deviceType = deviceType,
                manufacturer = manufacturer,
                securityRisk = securityRisk,
                isKnownDevice = isKnownDevice
            )
            
            localApiClient.apiService.saveDevice(deviceRequest)
            
            // Track analytics
            trackEvent(
                eventType = "device_discovered",
                eventName = "new_device_found",
                eventData = mapOf(
                    "device_type" to deviceType,
                    "security_risk" to securityRisk,
                    "is_known" to isKnownDevice,
                    "has_name" to (deviceName != null)
                )
            )
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Get dashboard data
     */
    suspend fun getDashboardData(): Map<String, Any>? {
        return try {
            if (!localApiClient.hasValidToken()) return null
            
            val response = localApiClient.apiService.getDashboardData()
            if (response.isSuccessful) {
                val data = response.body()!!
                mapOf(
                    "stats" to data.stats,
                    "recentScans" to data.recentScans,
                    "recentDevices" to data.recentDevices,
                    "recentInsights" to data.recentInsights
                )
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Test server connection
     */
    suspend fun testServerConnection(): Boolean {
        return localApiClient.testConnection()
    }
    
    /**
     * Update user profile (interface implementation)
     */
    override suspend fun updateProfile(
        firstName: String?,
        lastName: String?,
        profileImageUrl: String?
    ): com.zilal.networkguardian.auth.repository.AuthResult {
        // For local server, we'll just return success for now
        // In a real implementation, we would call the API
        return com.zilal.networkguardian.auth.repository.AuthResult.Success(currentUser.value!!)
    }

    /**
     * Change password (interface implementation)
     */
    override suspend fun changePassword(currentPassword: String, newPassword: String): com.zilal.networkguardian.auth.repository.AuthResult {
        // For local server, we would implement password change API
        // This is a simplified implementation
        return com.zilal.networkguardian.auth.repository.AuthResult.Error("Password change not implemented with local server yet")
    }

    /**
     * Get current server URL
     */
    fun getServerUrl(): String {
        return localApiClient.getCurrentServerUrl()
    }

    // Helper methods
    private fun convertToUser(localUser: com.zilal.networkguardian.api.LocalUser): User {
        return User(
            id = localUser.id,
            email = localUser.email,
            username = localUser.username,
            passwordHash = "", // Not needed for API integration
            firstName = localUser.first_name,
            lastName = localUser.last_name,
            profileImageUrl = null,
            role = when (localUser.role) {
                "admin" -> UserRole.ADMIN
                else -> UserRole.USER
            },
            isEmailVerified = true,
            isActive = localUser.subscription_status == "active",
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            lastLoginAt = System.currentTimeMillis(),
            loginCount = localUser.login_count,
            preferences = null,
            analyticsEnabled = true,
            aiInsightsEnabled = true,
            subscriptionType = when (localUser.subscription_plan) {
                "enterprise" -> SubscriptionType.ENTERPRISE
                else -> SubscriptionType.PREMIUM
            },
            subscriptionExpiresAt = null
        )
    }
    
    private fun getNetworkType(): String {
        // Implementation to get network type
        return "WiFi" // Placeholder
    }
    
    private fun getBatteryLevel(): Int {
        // Implementation to get battery level
        return 100 // Placeholder
    }
    
    private fun getMemoryUsage(): Int {
        val runtime = Runtime.getRuntime()
        return ((runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)).toInt()
    }
    
    private fun getCpuUsage(): Float {
        // Implementation to get CPU usage
        return 0.0f // Placeholder
    }
}
