package com.zilal.networkguardian.auth.repository

import com.zilal.networkguardian.auth.model.User
import kotlinx.coroutines.flow.StateFlow

/**
 * Interface for authentication repository
 */
interface IAuthRepository {
    
    val currentUser: StateFlow<User?>
    val isAuthenticated: StateFlow<Boolean>
    val authState: StateFlow<AuthState>
    
    suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String? = null,
        lastName: String? = null
    ): AuthResult
    
    suspend fun login(emailOrUsername: String, password: String): AuthResult
    
    suspend fun logout()
    
    suspend fun updateProfile(
        firstName: String? = null,
        lastName: String? = null,
        profileImageUrl: String? = null
    ): AuthResult
    
    suspend fun changePassword(currentPassword: String, newPassword: String): AuthResult
}
