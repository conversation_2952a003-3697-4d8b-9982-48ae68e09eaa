package com.zilal.networkguardian.auth.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.model.*
import com.zilal.networkguardian.auth.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for analytics and AI insights
 */
@HiltViewModel
class AnalyticsViewModel @Inject constructor(
    private val authDao: AuthDao,
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val currentUser = authRepository.currentUser
    
    // Analytics data flows
    private val _userAnalytics = MutableStateFlow<List<UserAnalytics>>(emptyList())
    val userAnalytics: StateFlow<List<UserAnalytics>> = _userAnalytics.asStateFlow()
    
    private val _aiInsights = MutableStateFlow<List<AIInsight>>(emptyList())
    val aiInsights: StateFlow<List<AIInsight>> = _aiInsights.asStateFlow()
    
    private val _usageStats = MutableStateFlow<List<UsageStatistics>>(emptyList())
    val usageStats: StateFlow<List<UsageStatistics>> = _usageStats.asStateFlow()
    
    private val _scanAnalytics = MutableStateFlow<List<ScanAnalytics>>(emptyList())
    val scanAnalytics: StateFlow<List<ScanAnalytics>> = _scanAnalytics.asStateFlow()
    
    private val _behaviorPatterns = MutableStateFlow<List<BehaviorPattern>>(emptyList())
    val behaviorPatterns: StateFlow<List<BehaviorPattern>> = _behaviorPatterns.asStateFlow()
    
    private val _deviceAnalytics = MutableStateFlow<List<DeviceAnalytics>>(emptyList())
    val deviceAnalytics: StateFlow<List<DeviceAnalytics>> = _deviceAnalytics.asStateFlow()
    
    // Loading states
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    init {
        // Load analytics data when user is authenticated
        viewModelScope.launch {
            currentUser.collect { user ->
                if (user != null) {
                    loadAllAnalytics(user.id)
                }
            }
        }
    }
    
    /**
     * Load all analytics data for the current user
     */
    private suspend fun loadAllAnalytics(userId: String) {
        try {
            _isLoading.value = true
            
            // Load user analytics
            val analytics = authDao.getUserAnalytics(userId, 100)
            _userAnalytics.value = analytics
            
            // Load AI insights
            val insights = authDao.getUserInsights(userId, 20)
            _aiInsights.value = insights
            
            // Load usage statistics
            val usage = authDao.getUserUsageStatistics(userId, 30)
            _usageStats.value = usage
            
            // Load scan analytics
            val scans = authDao.getUserScanAnalytics(userId, 50)
            _scanAnalytics.value = scans
            
            // Load behavior patterns
            val patterns = authDao.getUserBehaviorPatterns(userId)
            _behaviorPatterns.value = patterns
            
            // Load device analytics
            val devices = authDao.getUserDeviceAnalytics(userId)
            _deviceAnalytics.value = devices
            
        } catch (e: Exception) {
            _errorMessage.value = e.message ?: "Failed to load analytics"
        } finally {
            _isLoading.value = false
        }
    }
    
    /**
     * Generate AI insights for the current user
     */
    fun generateAIInsights() {
        viewModelScope.launch {
            try {
                val user = currentUser.value ?: return@launch
                _isLoading.value = true
                
                // Generate insights based on current data
                val insights = generateInsightsFromData(user.id)
                
                // Save insights to database
                insights.forEach { insight ->
                    authDao.insertAIInsight(insight)
                }
                
                // Reload insights
                val updatedInsights = authDao.getUserInsights(user.id, 20)
                _aiInsights.value = updatedInsights
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to generate insights"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Generate insights from user data
     */
    private suspend fun generateInsightsFromData(userId: String): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()
        
        // Analyze scan frequency
        val recentScans = authDao.getUserScanAnalytics(userId, 10)
        if (recentScans.size >= 5) {
            val avgDuration = recentScans.map { it.scanDuration }.average()
            val totalDevices = recentScans.sumOf { it.devicesFound }
            
            if (avgDuration > 30000) { // More than 30 seconds
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.PERFORMANCE_OPTIMIZATION,
                        title = "Scan Performance Optimization",
                        description = "Your scans are taking longer than average. Consider optimizing scan parameters.",
                        recommendation = "Reduce scan range or increase timeout values for better performance.",
                        severity = InsightSeverity.LOW,
                        confidence = 0.75f,
                        dataPoints = """{"avg_duration": $avgDuration, "scan_count": ${recentScans.size}}"""
                    )
                )
            }
            
            if (totalDevices > 50) {
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.NETWORK_PATTERN_ANALYSIS,
                        title = "Large Network Detected",
                        description = "Your network has $totalDevices devices. Consider network segmentation.",
                        recommendation = "Implement network segmentation for better security and performance.",
                        severity = InsightSeverity.MEDIUM,
                        confidence = 0.85f,
                        dataPoints = """{"total_devices": $totalDevices}"""
                    )
                )
            }
        }
        
        // Analyze usage patterns
        val usageStats = authDao.getUserUsageStatistics(userId, 7)
        if (usageStats.isNotEmpty()) {
            val avgUsageTime = usageStats.map { it.totalUsageTime }.average()
            val totalScans = usageStats.sumOf { it.scansPerformed }
            
            if (avgUsageTime > 2 * 60 * 60 * 1000) { // More than 2 hours daily
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.USAGE_OPTIMIZATION,
                        title = "High App Usage Detected",
                        description = "You're spending significant time monitoring your network.",
                        recommendation = "Consider setting up automated alerts to reduce manual monitoring.",
                        severity = InsightSeverity.LOW,
                        confidence = 0.70f,
                        dataPoints = """{"avg_daily_usage_hours": ${avgUsageTime / (60 * 60 * 1000)}}"""
                    )
                )
            }
            
            if (totalScans > 20) {
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.SECURITY_RECOMMENDATION,
                        title = "Active Security Monitoring",
                        description = "Excellent! You're actively monitoring your network security.",
                        recommendation = "Continue your vigilant security practices.",
                        severity = InsightSeverity.LOW,
                        confidence = 0.90f,
                        dataPoints = """{"weekly_scans": $totalScans}"""
                    )
                )
            }
        }
        
        // Analyze device patterns
        val deviceAnalytics = authDao.getUserDeviceAnalytics(userId)
        val unknownDevices = deviceAnalytics.filter { !it.isKnownDevice }
        val highRiskDevices = deviceAnalytics.filter { 
            it.securityRisk == SecurityRiskLevel.HIGH || it.securityRisk == SecurityRiskLevel.CRITICAL 
        }
        
        if (unknownDevices.isNotEmpty()) {
            insights.add(
                AIInsight(
                    userId = userId,
                    insightType = AIInsightType.SECURITY_RECOMMENDATION,
                    title = "Unknown Devices on Network",
                    description = "Found ${unknownDevices.size} unidentified devices on your network.",
                    recommendation = "Review and identify all unknown devices for security.",
                    severity = InsightSeverity.MEDIUM,
                    confidence = 0.95f,
                    dataPoints = """{"unknown_device_count": ${unknownDevices.size}}"""
                )
            )
        }
        
        if (highRiskDevices.isNotEmpty()) {
            insights.add(
                AIInsight(
                    userId = userId,
                    insightType = AIInsightType.THREAT_PREDICTION,
                    title = "High-Risk Devices Detected",
                    description = "Identified ${highRiskDevices.size} devices with elevated security risk.",
                    recommendation = "Investigate high-risk devices and consider network isolation.",
                    severity = InsightSeverity.HIGH,
                    confidence = 0.88f,
                    dataPoints = """{"high_risk_device_count": ${highRiskDevices.size}}"""
                )
            )
        }
        
        return insights
    }
    
    /**
     * Mark insight as read
     */
    fun markInsightAsRead(insightId: String) {
        viewModelScope.launch {
            try {
                authDao.markInsightAsRead(insightId)
                
                // Update local state
                val currentInsights = _aiInsights.value.toMutableList()
                val index = currentInsights.indexOfFirst { it.id == insightId }
                if (index != -1) {
                    currentInsights[index] = currentInsights[index].copy(isRead = true)
                    _aiInsights.value = currentInsights
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to mark insight as read"
            }
        }
    }
    
    /**
     * Mark insight as actioned
     */
    fun markInsightAsActioned(insightId: String) {
        viewModelScope.launch {
            try {
                authDao.markInsightAsActioned(insightId)
                
                // Update local state
                val currentInsights = _aiInsights.value.toMutableList()
                val index = currentInsights.indexOfFirst { it.id == insightId }
                if (index != -1) {
                    currentInsights[index] = currentInsights[index].copy(isActioned = true)
                    _aiInsights.value = currentInsights
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to mark insight as actioned"
            }
        }
    }
    
    /**
     * Get analytics summary
     */
    fun getAnalyticsSummary(): AnalyticsSummary {
        val analytics = _userAnalytics.value
        val scans = _scanAnalytics.value
        val usage = _usageStats.value
        val insights = _aiInsights.value
        
        return AnalyticsSummary(
            totalEvents = analytics.size,
            totalScans = scans.size,
            totalUsageTime = usage.sumOf { it.totalUsageTime },
            unreadInsights = insights.count { !it.isRead },
            averageScanDuration = if (scans.isNotEmpty()) scans.map { it.scanDuration }.average() else 0.0,
            totalDevicesFound = scans.sumOf { it.devicesFound },
            totalVulnerabilitiesFound = scans.sumOf { it.vulnerabilitiesFound }
        )
    }
    
    /**
     * Export analytics data
     */
    fun exportAnalyticsData(): String {
        val summary = getAnalyticsSummary()
        val analytics = _userAnalytics.value
        val scans = _scanAnalytics.value
        val insights = _aiInsights.value
        
        return buildString {
            appendLine("SecureNet Pro Analytics Export")
            appendLine("Generated: ${java.util.Date()}")
            appendLine()
            appendLine("Summary:")
            appendLine("- Total Events: ${summary.totalEvents}")
            appendLine("- Total Scans: ${summary.totalScans}")
            appendLine("- Total Usage Time: ${summary.totalUsageTime / (60 * 60 * 1000)} hours")
            appendLine("- Unread Insights: ${summary.unreadInsights}")
            appendLine("- Average Scan Duration: ${summary.averageScanDuration / 1000} seconds")
            appendLine("- Total Devices Found: ${summary.totalDevicesFound}")
            appendLine("- Total Vulnerabilities: ${summary.totalVulnerabilitiesFound}")
            appendLine()
            appendLine("Recent Events: ${analytics.size}")
            appendLine("Recent Scans: ${scans.size}")
            appendLine("AI Insights: ${insights.size}")
        }
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

/**
 * Analytics summary data class
 */
data class AnalyticsSummary(
    val totalEvents: Int,
    val totalScans: Int,
    val totalUsageTime: Long,
    val unreadInsights: Int,
    val averageScanDuration: Double,
    val totalDevicesFound: Int,
    val totalVulnerabilitiesFound: Int
)
