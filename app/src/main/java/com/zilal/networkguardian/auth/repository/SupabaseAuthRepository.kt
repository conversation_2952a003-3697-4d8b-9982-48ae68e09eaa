package com.zilal.networkguardian.auth.repository

import android.content.Context
import android.util.Log
import com.zilal.networkguardian.api.SupabaseClient
import com.zilal.networkguardian.auth.model.User
import com.zilal.networkguardian.auth.model.UserRole
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Production Supabase authentication repository
 * Handles all authentication operations with cloud backend
 */
@Singleton
class SupabaseAuthRepository @Inject constructor(
    private val supabaseClient: SupabaseClient,
    @ApplicationContext private val context: Context
) : IAuthRepository {

    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    override val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _currentUser = MutableStateFlow<User?>(null)
    override val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    private val _isAuthenticated = MutableStateFlow(false)
    override val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()

    companion object {
        private const val TAG = "SupabaseAuthRepository"
    }

    init {
        // Check if user is already signed in
        checkAuthState()
    }

    /**
     * Check current authentication state
     */
    private fun checkAuthState() {
        try {
            if (supabaseClient.isSignedIn()) {
                val userId = supabaseClient.getCurrentUserId()
                val email = supabaseClient.getContext()
                    .getSharedPreferences("NetworkGuardianPrefs", Context.MODE_PRIVATE)
                    .getString("user_email", null)
                
                if (userId != null && email != null) {
                    val user = User(
                        id = userId,
                        email = email,
                        username = email.substringBefore("@"),
                        passwordHash = "",
                        firstName = email.substringBefore("@").replaceFirstChar { it.uppercase() },
                        lastName = "",
                        profileImageUrl = null,
                        role = UserRole.USER,
                        isEmailVerified = true,
                        isActive = true,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                        lastLoginAt = System.currentTimeMillis(),
                        loginCount = 1,
                        preferences = null,
                        analyticsEnabled = true
                    )
                    _currentUser.value = user
                    _isAuthenticated.value = true
                    _authState.value = AuthState.Authenticated(user)
                    Log.d(TAG, "User already authenticated: $email")
                } else {
                    _authState.value = AuthState.Unauthenticated
                    _isAuthenticated.value = false
                }
            } else {
                _authState.value = AuthState.Unauthenticated
                _isAuthenticated.value = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking auth state: ${e.message}")
            _authState.value = AuthState.Unauthenticated
            _isAuthenticated.value = false
        }
    }

    /**
     * Register a new user
     */
    override suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String?,
        lastName: String?
    ): AuthResult {
        return try {
            _authState.value = AuthState.Loading
            
            Log.d(TAG, "Attempting to register user: $email")
            
            val result = supabaseClient.signUp(email, password)
            
            if (result.isSuccess) {
                val userId = supabaseClient.getCurrentUserId()
                if (userId != null) {
                    val user = User(
                        id = userId,
                        email = email,
                        username = username,
                        passwordHash = "",
                        firstName = firstName ?: "",
                        lastName = lastName ?: "",
                        profileImageUrl = null,
                        role = UserRole.USER,
                        isEmailVerified = true,
                        isActive = true,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                        lastLoginAt = System.currentTimeMillis(),
                        loginCount = 1,
                        preferences = null,
                        analyticsEnabled = true
                    )

                    _currentUser.value = user
                    _isAuthenticated.value = true
                    _authState.value = AuthState.Authenticated(user)

                    Log.d(TAG, "User registered successfully: $email")
                    AuthResult.Success(user)
                } else {
                    _authState.value = AuthState.Error("Registration failed: Invalid user ID")
                    AuthResult.Error("Registration failed: Invalid user ID")
                }
            } else {
                val errorMessage = result.exceptionOrNull()?.message ?: "Registration failed"
                _authState.value = AuthState.Error(errorMessage)
                Log.e(TAG, "Registration failed: $errorMessage")
                AuthResult.Error(errorMessage)
            }
        } catch (e: Exception) {
            val errorMessage = "Registration failed: ${e.message}"
            _authState.value = AuthState.Error(errorMessage)
            Log.e(TAG, errorMessage, e)
            AuthResult.Error(errorMessage)
        }
    }

    /**
     * Login an existing user
     */
    override suspend fun login(emailOrUsername: String, password: String): AuthResult {
        return try {
            _authState.value = AuthState.Loading
            
            Log.d(TAG, "Attempting to login user: $emailOrUsername")
            
            val result = supabaseClient.signIn(emailOrUsername, password)
            
            if (result.isSuccess) {
                val userId = supabaseClient.getCurrentUserId()
                if (userId != null) {
                    val user = User(
                        id = userId,
                        email = emailOrUsername,
                        username = emailOrUsername.substringBefore("@"),
                        passwordHash = "",
                        firstName = emailOrUsername.substringBefore("@").replaceFirstChar { it.uppercase() },
                        lastName = "",
                        profileImageUrl = null,
                        role = UserRole.USER,
                        isEmailVerified = true,
                        isActive = true,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                        lastLoginAt = System.currentTimeMillis(),
                        loginCount = 1,
                        preferences = null,
                        analyticsEnabled = true
                    )

                    _currentUser.value = user
                    _isAuthenticated.value = true
                    _authState.value = AuthState.Authenticated(user)

                    Log.d(TAG, "User logged in successfully: $emailOrUsername")
                    AuthResult.Success(user)
                } else {
                    _authState.value = AuthState.Error("Login failed: Invalid user ID")
                    AuthResult.Error("Login failed: Invalid user ID")
                }
            } else {
                val errorMessage = result.exceptionOrNull()?.message ?: "Login failed"
                _authState.value = AuthState.Error(errorMessage)
                Log.e(TAG, "Login failed: $errorMessage")
                AuthResult.Error(errorMessage)
            }
        } catch (e: Exception) {
            val errorMessage = "Login failed: ${e.message}"
            _authState.value = AuthState.Error(errorMessage)
            Log.e(TAG, errorMessage, e)
            AuthResult.Error(errorMessage)
        }
    }

    /**
     * Logout the current user
     */
    override suspend fun logout() {
        try {
            Log.d(TAG, "Attempting to logout user")

            val result = supabaseClient.signOut()

            if (result.isSuccess) {
                _currentUser.value = null
                _isAuthenticated.value = false
                _authState.value = AuthState.Unauthenticated

                Log.d(TAG, "User logged out successfully")
            } else {
                val errorMessage = result.exceptionOrNull()?.message ?: "Logout failed"
                Log.e(TAG, "Logout failed: $errorMessage")
            }
        } catch (e: Exception) {
            val errorMessage = "Logout failed: ${e.message}"
            Log.e(TAG, errorMessage, e)
        }
    }

    /**
     * Update user profile
     */
    override suspend fun updateProfile(
        firstName: String?,
        lastName: String?,
        profileImageUrl: String?
    ): AuthResult {
        return try {
            val currentUser = _currentUser.value
            if (currentUser != null) {
                val updatedUser = currentUser.copy(
                    firstName = firstName ?: currentUser.firstName,
                    lastName = lastName ?: currentUser.lastName,
                    profileImageUrl = profileImageUrl ?: currentUser.profileImageUrl,
                    updatedAt = System.currentTimeMillis()
                )
                _currentUser.value = updatedUser

                Log.d(TAG, "User profile updated successfully")
                AuthResult.Success(updatedUser)
            } else {
                AuthResult.Error("No user logged in")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Profile update failed: ${e.message}")
            AuthResult.Error(e.message ?: "Profile update failed")
        }
    }

    /**
     * Change user password
     */
    override suspend fun changePassword(
        currentPassword: String,
        newPassword: String
    ): AuthResult {
        return try {
            // TODO: Implement password change with Supabase
            Log.d(TAG, "Password change not yet implemented")
            AuthResult.Error("Password change not yet implemented")
        } catch (e: Exception) {
            Log.e(TAG, "Password change failed: ${e.message}")
            AuthResult.Error(e.message ?: "Password change failed")
        }
    }
}
