package com.zilal.networkguardian.auth.repository

import com.zilal.networkguardian.auth.model.User
import com.zilal.networkguardian.supabase.SupabaseClient
import com.zilal.networkguardian.supabase.AuthResult
import com.zilal.networkguardian.supabase.AuthState
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Supabase-powered authentication repository for premium users
 */
@Singleton
class SupabaseAuthRepository @Inject constructor(
    private val supabaseClient: SupabaseClient
) {
    
    // Map Supabase auth state to our app's auth state
    val authState: StateFlow<com.zilal.networkguardian.auth.repository.AuthState> = 
        supabaseClient.authState.map { supabaseState ->
            when (supabaseState) {
                is AuthState.Loading -> com.zilal.networkguardian.auth.repository.AuthState.Loading
                is AuthState.Unauthenticated -> com.zilal.networkguardian.auth.repository.AuthState.Unauthenticated
                is AuthState.Authenticated -> {
                    val user = supabaseState.user?.let { profile ->
                        User(
                            id = profile.id,
                            email = profile.username + "@domain.com", // We'll need to get actual email
                            username = profile.username,
                            passwordHash = "", // Not needed for Supabase
                            firstName = profile.firstName,
                            lastName = profile.lastName,
                            profileImageUrl = profile.avatarUrl,
                            role = com.zilal.networkguardian.auth.model.UserRole.USER,
                            isEmailVerified = true, // Supabase handles this
                            isActive = profile.subscriptionStatus == "active",
                            createdAt = System.currentTimeMillis(),
                            updatedAt = System.currentTimeMillis(),
                            lastLoginAt = System.currentTimeMillis(),
                            loginCount = 1,
                            preferences = null,
                            analyticsEnabled = profile.analyticsEnabled,
                            aiInsightsEnabled = profile.aiInsightsEnabled,
                            subscriptionType = when (profile.subscriptionPlan) {
                                "premium" -> com.zilal.networkguardian.auth.model.SubscriptionType.PREMIUM
                                "enterprise" -> com.zilal.networkguardian.auth.model.SubscriptionType.ENTERPRISE
                                else -> com.zilal.networkguardian.auth.model.SubscriptionType.PREMIUM
                            },
                            subscriptionExpiresAt = null
                        )
                    }
                    com.zilal.networkguardian.auth.repository.AuthState.Authenticated(user)
                }
                is AuthState.Error -> com.zilal.networkguardian.auth.repository.AuthState.Error(supabaseState.message)
            }
        } as StateFlow<com.zilal.networkguardian.auth.repository.AuthState>
    
    val currentUser: StateFlow<User?> = supabaseClient.currentUser.map { profile ->
        profile?.let {
            User(
                id = it.id,
                email = it.username + "@domain.com", // We'll need to get actual email
                username = it.username,
                passwordHash = "", // Not needed for Supabase
                firstName = it.firstName,
                lastName = it.lastName,
                profileImageUrl = it.avatarUrl,
                role = com.zilal.networkguardian.auth.model.UserRole.USER,
                isEmailVerified = true,
                isActive = it.subscriptionStatus == "active",
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastLoginAt = System.currentTimeMillis(),
                loginCount = 1,
                preferences = null,
                analyticsEnabled = it.analyticsEnabled,
                aiInsightsEnabled = it.aiInsightsEnabled,
                subscriptionType = when (it.subscriptionPlan) {
                    "premium" -> com.zilal.networkguardian.auth.model.SubscriptionType.PREMIUM
                    "enterprise" -> com.zilal.networkguardian.auth.model.SubscriptionType.ENTERPRISE
                    else -> com.zilal.networkguardian.auth.model.SubscriptionType.PREMIUM
                },
                subscriptionExpiresAt = null
            )
        }
    } as StateFlow<User?>
    
    val isAuthenticated: StateFlow<Boolean> = supabaseClient.authState.map { state ->
        state is AuthState.Authenticated
    } as StateFlow<Boolean>
    
    /**
     * Register new premium user
     */
    suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String? = null,
        lastName: String? = null
    ): com.zilal.networkguardian.auth.repository.AuthResult {
        return when (val result = supabaseClient.signUp(email, password, username, firstName, lastName)) {
            is AuthResult.Success -> {
                // Track registration analytics
                supabaseClient.trackEvent(
                    eventType = "user_registered",
                    eventName = "account_created",
                    eventData = mapOf(
                        "registration_method" to "email",
                        "subscription_plan" to "premium",
                        "has_name" to (firstName != null)
                    )
                )
                com.zilal.networkguardian.auth.repository.AuthResult.Success(currentUser.value!!)
            }
            is AuthResult.Error -> com.zilal.networkguardian.auth.repository.AuthResult.Error(result.message)
        }
    }
    
    /**
     * Login premium user
     */
    suspend fun login(emailOrUsername: String, password: String): com.zilal.networkguardian.auth.repository.AuthResult {
        return when (val result = supabaseClient.signIn(emailOrUsername, password)) {
            is AuthResult.Success -> {
                // Track login analytics
                supabaseClient.trackEvent(
                    eventType = "user_login",
                    eventName = "login_success",
                    eventData = mapOf(
                        "login_method" to "password",
                        "subscription_plan" to "premium"
                    )
                )
                com.zilal.networkguardian.auth.repository.AuthResult.Success(currentUser.value!!)
            }
            is AuthResult.Error -> com.zilal.networkguardian.auth.repository.AuthResult.Error(result.message)
        }
    }
    
    /**
     * Logout user
     */
    suspend fun logout() {
        // Track logout analytics
        supabaseClient.trackEvent(
            eventType = "user_logout",
            eventName = "logout_success"
        )
        
        supabaseClient.signOut()
    }
    
    /**
     * Update user profile
     */
    suspend fun updateProfile(
        firstName: String? = null,
        lastName: String? = null,
        profileImageUrl: String? = null
    ): com.zilal.networkguardian.auth.repository.AuthResult {
        return when (val result = supabaseClient.updateProfile(firstName, lastName)) {
            is AuthResult.Success -> {
                supabaseClient.trackEvent(
                    eventType = "profile_updated",
                    eventName = "profile_changed"
                )
                com.zilal.networkguardian.auth.repository.AuthResult.Success(currentUser.value!!)
            }
            is AuthResult.Error -> com.zilal.networkguardian.auth.repository.AuthResult.Error(result.message)
        }
    }
    
    /**
     * Change password (Supabase handles this differently)
     */
    suspend fun changePassword(currentPassword: String, newPassword: String): com.zilal.networkguardian.auth.repository.AuthResult {
        // For Supabase, we would use the password reset flow
        // This is a simplified implementation
        return com.zilal.networkguardian.auth.repository.AuthResult.Error("Password change not implemented with Supabase yet")
    }
    
    /**
     * Track network scan
     */
    suspend fun trackNetworkScan(
        scanId: String,
        scanType: String,
        targetRange: String,
        devicesFound: Int = 0,
        vulnerabilitiesFound: Int = 0,
        scanDurationMs: Long = 0,
        success: Boolean = false,
        errorMessage: String? = null,
        scanResults: Map<String, Any>? = null
    ) {
        // Save to Supabase
        supabaseClient.saveNetworkScan(
            scanId = scanId,
            scanType = scanType,
            targetRange = targetRange,
            devicesFound = devicesFound,
            vulnerabilitiesFound = vulnerabilitiesFound,
            scanDurationMs = scanDurationMs,
            success = success,
            errorMessage = errorMessage,
            scanResults = scanResults
        )
        
        // Track analytics event
        supabaseClient.trackEvent(
            eventType = "network_scan",
            eventName = if (success) "scan_completed" else "scan_failed",
            eventData = mapOf(
                "scan_type" to scanType,
                "devices_found" to devicesFound,
                "vulnerabilities_found" to vulnerabilitiesFound,
                "duration_ms" to scanDurationMs,
                "success" to success
            ),
            duration = scanDurationMs
        )
    }
    
    /**
     * Track discovered device
     */
    suspend fun trackDiscoveredDevice(
        deviceId: String,
        deviceName: String? = null,
        ipAddress: String,
        macAddress: String? = null,
        deviceType: String = "unknown",
        manufacturer: String? = null,
        securityRisk: String = "low",
        isKnownDevice: Boolean = false
    ) {
        supabaseClient.saveNetworkDevice(
            deviceId = deviceId,
            deviceName = deviceName,
            ipAddress = ipAddress,
            macAddress = macAddress,
            deviceType = deviceType,
            manufacturer = manufacturer,
            securityRisk = securityRisk,
            isKnownDevice = isKnownDevice
        )
        
        // Track analytics
        supabaseClient.trackEvent(
            eventType = "device_discovered",
            eventName = "new_device_found",
            eventData = mapOf(
                "device_type" to deviceType,
                "security_risk" to securityRisk,
                "is_known" to isKnownDevice,
                "has_name" to (deviceName != null)
            )
        )
    }
    
    /**
     * Get AI insights
     */
    suspend fun getAIInsights(limit: Int = 20): List<com.zilal.networkguardian.supabase.AIInsight> {
        return supabaseClient.getAIInsights(limit)
    }
    
    /**
     * Get network scans
     */
    suspend fun getNetworkScans(limit: Int = 50): List<com.zilal.networkguardian.supabase.NetworkScan> {
        return supabaseClient.getNetworkScans(limit)
    }
    
    /**
     * Get network devices
     */
    suspend fun getNetworkDevices(): List<com.zilal.networkguardian.supabase.NetworkDevice> {
        return supabaseClient.getNetworkDevices()
    }
}
