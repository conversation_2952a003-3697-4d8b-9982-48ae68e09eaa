package com.zilal.networkguardian.auth.service

import android.content.Context
import android.os.Build
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.model.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import javax.inject.Inject
import javax.inject.Singleton
import java.text.SimpleDateFormat
import java.util.*

/**
 * Analytics service for tracking user behavior and app usage
 */
@Singleton
class AnalyticsService @Inject constructor(
    private val authDao: AuthDao,
    private val context: Context
) {
    
    private val analyticsScope = CoroutineScope(Dispatchers.IO)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    /**
     * Track user event
     */
    fun trackEvent(
        userId: String,
        sessionId: String,
        eventType: AnalyticsEventType,
        eventName: String,
        eventData: String? = null,
        screenName: String? = null,
        duration: Long? = null
    ) {
        analyticsScope.launch {
            try {
                val analytics = UserAnalytics(
                    userId = userId,
                    sessionId = sessionId,
                    eventType = eventType,
                    eventName = eventName,
                    eventData = eventData,
                    screenName = screenName,
                    duration = duration,
                    networkType = getNetworkType(),
                    batteryLevel = getBatteryLevel(),
                    memoryUsage = getMemoryUsage(),
                    cpuUsage = getCpuUsage()
                )
                
                authDao.insertAnalyticsEvent(analytics)
                updateUsageStatistics(userId, eventType, duration)
                
            } catch (e: Exception) {
                // Log error but don't crash the app
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Track scan analytics
     */
    fun trackScanAnalytics(
        userId: String,
        scanId: String,
        scanType: String,
        targetRange: String,
        devicesFound: Int,
        vulnerabilitiesFound: Int,
        scanDuration: Long,
        scanSuccess: Boolean,
        errorMessage: String? = null,
        deviceTypes: Map<String, Int>? = null,
        securityScore: Int? = null
    ) {
        analyticsScope.launch {
            try {
                val networkConditions = JSONObject().apply {
                    put("network_type", getNetworkType())
                    put("signal_strength", getWifiSignalStrength())
                    put("timestamp", System.currentTimeMillis())
                }.toString()
                
                val deviceTypesJson = deviceTypes?.let { types ->
                    JSONObject().apply {
                        types.forEach { (type, count) ->
                            put(type, count)
                        }
                    }.toString()
                }
                
                val scanAnalytics = ScanAnalytics(
                    userId = userId,
                    scanId = scanId,
                    scanType = scanType,
                    targetRange = targetRange,
                    devicesFound = devicesFound,
                    vulnerabilitiesFound = vulnerabilitiesFound,
                    scanDuration = scanDuration,
                    scanSuccess = scanSuccess,
                    errorMessage = errorMessage,
                    networkConditions = networkConditions,
                    deviceTypes = deviceTypesJson,
                    securityScore = securityScore,
                    networkName = getCurrentNetworkName(),
                    signalStrength = getWifiSignalStrength()
                )
                
                authDao.insertScanAnalytics(scanAnalytics)
                updateDailyScanStats(userId, devicesFound, vulnerabilitiesFound)
                
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Track device analytics
     */
    fun trackDeviceAnalytics(
        userId: String,
        deviceId: String,
        deviceName: String?,
        deviceType: String,
        macAddress: String?,
        ipAddress: String,
        manufacturer: String?,
        isKnownDevice: Boolean = false
    ) {
        analyticsScope.launch {
            try {
                val existing = authDao.getDeviceAnalytics(userId, deviceId)
                
                if (existing != null) {
                    // Update existing device
                    val updated = existing.copy(
                        deviceName = deviceName ?: existing.deviceName,
                        manufacturer = manufacturer ?: existing.manufacturer,
                        lastSeen = System.currentTimeMillis(),
                        totalConnections = existing.totalConnections + 1,
                        isKnownDevice = isKnownDevice
                    )
                    authDao.updateDeviceAnalytics(updated)
                } else {
                    // Create new device analytics
                    val deviceAnalytics = DeviceAnalytics(
                        userId = userId,
                        deviceId = deviceId,
                        deviceName = deviceName,
                        deviceType = deviceType,
                        macAddress = macAddress,
                        ipAddress = ipAddress,
                        manufacturer = manufacturer,
                        isKnownDevice = isKnownDevice
                    )
                    authDao.insertDeviceAnalytics(deviceAnalytics)
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Generate AI insights based on user behavior
     */
    fun generateAIInsights(userId: String) {
        analyticsScope.launch {
            try {
                val insights = mutableListOf<AIInsight>()
                
                // Analyze scan patterns
                val recentScans = authDao.getUserScanAnalytics(userId, 50)
                insights.addAll(analyzeScanPatterns(userId, recentScans))
                
                // Analyze device behavior
                val deviceAnalytics = authDao.getUserDeviceAnalytics(userId)
                insights.addAll(analyzeDeviceBehavior(userId, deviceAnalytics))
                
                // Analyze usage patterns
                val usageStats = authDao.getUserUsageStatistics(userId, 30)
                insights.addAll(analyzeUsagePatterns(userId, usageStats))
                
                // Save insights
                insights.forEach { insight ->
                    authDao.insertAIInsight(insight)
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Update usage statistics
     */
    private suspend fun updateUsageStatistics(
        userId: String,
        eventType: AnalyticsEventType,
        duration: Long?
    ) {
        val today = dateFormat.format(Date())
        val existing = authDao.getUsageStatisticsForDate(userId, today)
        
        if (existing != null) {
            val updated = existing.copy(
                sessionCount = if (eventType == AnalyticsEventType.APP_LAUNCH) existing.sessionCount + 1 else existing.sessionCount,
                totalUsageTime = existing.totalUsageTime + (duration ?: 0),
                scansPerformed = if (eventType == AnalyticsEventType.SCAN_START) existing.scansPerformed + 1 else existing.scansPerformed,
                updatedAt = System.currentTimeMillis()
            )
            authDao.updateUsageStatistics(updated)
        } else {
            val newStats = UsageStatistics(
                userId = userId,
                date = today,
                sessionCount = if (eventType == AnalyticsEventType.APP_LAUNCH) 1 else 0,
                totalUsageTime = duration ?: 0,
                scansPerformed = if (eventType == AnalyticsEventType.SCAN_START) 1 else 0
            )
            authDao.insertUsageStatistics(newStats)
        }
    }
    
    /**
     * Update daily scan statistics
     */
    private suspend fun updateDailyScanStats(
        userId: String,
        devicesFound: Int,
        vulnerabilitiesFound: Int
    ) {
        val today = dateFormat.format(Date())
        val existing = authDao.getUsageStatisticsForDate(userId, today)
        
        if (existing != null) {
            val updated = existing.copy(
                devicesDiscovered = existing.devicesDiscovered + devicesFound,
                vulnerabilitiesFound = existing.vulnerabilitiesFound + vulnerabilitiesFound,
                updatedAt = System.currentTimeMillis()
            )
            authDao.updateUsageStatistics(updated)
        }
    }
    
    /**
     * Analyze scan patterns for AI insights
     */
    private fun analyzeScanPatterns(userId: String, scans: List<ScanAnalytics>): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()
        
        if (scans.size >= 5) {
            val avgDuration = scans.map { it.scanDuration }.average()
            val avgDevices = scans.map { it.devicesFound }.average()
            val totalVulnerabilities = scans.sumOf { it.vulnerabilitiesFound }
            
            // Insight about scan frequency
            if (scans.size > 10) {
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.USAGE_OPTIMIZATION,
                        title = "High Scan Activity Detected",
                        description = "You've performed ${scans.size} scans recently. Consider setting up automated monitoring.",
                        recommendation = "Enable background monitoring to reduce manual scanning needs.",
                        severity = InsightSeverity.LOW,
                        confidence = 0.8f,
                        dataPoints = """{"scan_count": ${scans.size}, "avg_duration": $avgDuration}"""
                    )
                )
            }
            
            // Security insight
            if (totalVulnerabilities > 0) {
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.SECURITY_RECOMMENDATION,
                        title = "Security Issues Found",
                        description = "Found $totalVulnerabilities vulnerabilities across recent scans.",
                        recommendation = "Review and address security vulnerabilities immediately.",
                        severity = if (totalVulnerabilities > 5) InsightSeverity.HIGH else InsightSeverity.MEDIUM,
                        confidence = 0.9f,
                        dataPoints = """{"total_vulnerabilities": $totalVulnerabilities}"""
                    )
                )
            }
        }
        
        return insights
    }
    
    /**
     * Analyze device behavior for AI insights
     */
    private fun analyzeDeviceBehavior(userId: String, devices: List<DeviceAnalytics>): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()
        
        val unknownDevices = devices.filter { !it.isKnownDevice }
        val highRiskDevices = devices.filter { it.securityRisk == SecurityRiskLevel.HIGH || it.securityRisk == SecurityRiskLevel.CRITICAL }
        
        if (unknownDevices.isNotEmpty()) {
            insights.add(
                AIInsight(
                    userId = userId,
                    insightType = AIInsightType.SECURITY_RECOMMENDATION,
                    title = "Unknown Devices Detected",
                    description = "Found ${unknownDevices.size} unknown devices on your network.",
                    recommendation = "Review and identify unknown devices to ensure network security.",
                    severity = InsightSeverity.MEDIUM,
                    confidence = 0.85f,
                    dataPoints = """{"unknown_device_count": ${unknownDevices.size}}"""
                )
            )
        }
        
        if (highRiskDevices.isNotEmpty()) {
            insights.add(
                AIInsight(
                    userId = userId,
                    insightType = AIInsightType.THREAT_PREDICTION,
                    title = "High-Risk Devices Found",
                    description = "Detected ${highRiskDevices.size} devices with high security risk.",
                    recommendation = "Investigate high-risk devices and consider network isolation.",
                    severity = InsightSeverity.HIGH,
                    confidence = 0.9f,
                    dataPoints = """{"high_risk_device_count": ${highRiskDevices.size}}"""
                )
            )
        }
        
        return insights
    }
    
    /**
     * Analyze usage patterns for AI insights
     */
    private fun analyzeUsagePatterns(userId: String, usageStats: List<UsageStatistics>): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()
        
        if (usageStats.size >= 7) {
            val avgUsageTime = usageStats.map { it.totalUsageTime }.average()
            val avgScans = usageStats.map { it.scansPerformed }.average()
            
            if (avgUsageTime > 30 * 60 * 1000) { // More than 30 minutes daily
                insights.add(
                    AIInsight(
                        userId = userId,
                        insightType = AIInsightType.USAGE_OPTIMIZATION,
                        title = "High App Usage Detected",
                        description = "You're spending significant time in the app daily.",
                        recommendation = "Consider setting up automated alerts to reduce manual monitoring.",
                        severity = InsightSeverity.LOW,
                        confidence = 0.7f,
                        dataPoints = """{"avg_daily_usage_minutes": ${avgUsageTime / 60000}}"""
                    )
                )
            }
        }
        
        return insights
    }
    
    // Helper methods for system information
    private fun getNetworkType(): String {
        // Implementation to get network type
        return "WiFi" // Placeholder
    }
    
    private fun getBatteryLevel(): Int {
        // Implementation to get battery level
        return 100 // Placeholder
    }
    
    private fun getMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }
    
    private fun getCpuUsage(): Float {
        // Implementation to get CPU usage
        return 0.0f // Placeholder
    }
    
    private fun getWifiSignalStrength(): Int {
        // Implementation to get WiFi signal strength
        return 100 // Placeholder
    }
    
    private fun getCurrentNetworkName(): String? {
        // Implementation to get current network name
        return null // Placeholder
    }
}
