package com.zilal.networkguardian.auth.ui

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.zilal.networkguardian.auth.viewmodel.AnalyticsViewModel
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*

/**
 * Analytics dashboard showing user behavior, AI insights, and data analysis
 */
@Composable
fun AnalyticsDashboard() {
    val analyticsViewModel: AnalyticsViewModel = viewModel()
    val userAnalytics by analyticsViewModel.userAnalytics.collectAsState()
    val aiInsights by analyticsViewModel.aiInsights.collectAsState()
    val usageStats by analyticsViewModel.usageStats.collectAsState()
    val scanAnalytics by analyticsViewModel.scanAnalytics.collectAsState()
    
    var currentTime by remember { mutableStateOf(System.currentTimeMillis()) }
    
    // Update time every second
    LaunchedEffect(Unit) {
        while (true) {
            currentTime = System.currentTimeMillis()
            delay(1000)
        }
    }
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Analytics Header
        item {
            AnalyticsHeader(currentTime = currentTime)
        }
        
        // AI Insights Section
        item {
            AIInsightsSection(insights = aiInsights)
        }
        
        // Usage Statistics
        item {
            UsageStatisticsSection(usageStats = usageStats)
        }
        
        // Scan Analytics
        item {
            ScanAnalyticsSection(scanAnalytics = scanAnalytics)
        }
        
        // Behavior Patterns
        item {
            BehaviorPatternsSection()
        }
        
        // Data Privacy & Controls
        item {
            DataPrivacySection()
        }
    }
}

@Composable
fun AnalyticsHeader(currentTime: Long) {
    val timeFormat = SimpleDateFormat("EEEE, MMMM dd, yyyy", Locale.getDefault())
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF667EEA),
                            Color(0xFF764BA2),
                            Color(0xFFF093FB)
                        )
                    )
                )
                .padding(24.dp)
        ) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "Analytics & Intelligence",
                            fontSize = 28.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        Text(
                            text = timeFormat.format(Date(currentTime)),
                            fontSize = 14.sp,
                            color = Color.White.copy(alpha = 0.8f)
                        )
                    }
                    
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .clip(RoundedCornerShape(16.dp))
                            .background(Color.White.copy(alpha = 0.2f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analytics",
                            tint = Color.White,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "AI-powered insights from your network security data",
                    fontSize = 16.sp,
                    color = Color.White.copy(alpha = 0.9f)
                )
            }
        }
    }
}

@Composable
fun AIInsightsSection(insights: List<com.zilal.networkguardian.auth.model.AIInsight>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🤖 AI Insights",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF8B5CF6).copy(alpha = 0.1f)
                    )
                ) {
                    Text(
                        text = "${insights.filter { !it.isRead }.size} New",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF8B5CF6),
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (insights.isEmpty()) {
                // Generate sample insights for demo
                val sampleInsights = listOf(
                    AIInsightItem(
                        title = "Network Security Optimized",
                        description = "Your network security score has improved by 15% this week",
                        type = "Security",
                        severity = "Low",
                        confidence = 0.92f,
                        icon = Icons.Default.Security,
                        color = Color(0xFF10B981)
                    ),
                    AIInsightItem(
                        title = "Unusual Device Activity",
                        description = "Detected 3 new devices connecting during off-hours",
                        type = "Behavior",
                        severity = "Medium",
                        confidence = 0.78f,
                        icon = Icons.Default.Warning,
                        color = Color(0xFFF59E0B)
                    ),
                    AIInsightItem(
                        title = "Scan Frequency Recommendation",
                        description = "Consider automated scanning to reduce manual effort",
                        type = "Optimization",
                        severity = "Low",
                        confidence = 0.85f,
                        icon = Icons.Default.TrendingUp,
                        color = Color(0xFF3B82F6)
                    )
                )
                
                sampleInsights.forEach { insight ->
                    AIInsightCard(insight = insight)
                    Spacer(modifier = Modifier.height(12.dp))
                }
            } else {
                insights.take(3).forEach { insight ->
                    // Convert real insight to display format
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

data class AIInsightItem(
    val title: String,
    val description: String,
    val type: String,
    val severity: String,
    val confidence: Float,
    val icon: ImageVector,
    val color: Color
)

@Composable
fun AIInsightCard(insight: AIInsightItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = insight.color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(insight.color.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = insight.icon,
                    contentDescription = insight.type,
                    tint = insight.color,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = insight.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = insight.color
                        )
                    ) {
                        Text(
                            text = insight.type,
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                Text(
                    text = insight.description,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Confidence: ${(insight.confidence * 100).toInt()}%",
                        fontSize = 12.sp,
                        color = insight.color
                    )
                    Text(
                        text = insight.severity,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun UsageStatisticsSection(usageStats: List<com.zilal.networkguardian.auth.model.UsageStatistics>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Text(
                text = "📊 Usage Analytics",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Sample usage metrics
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    UsageMetricCard(
                        icon = Icons.Default.AccessTime,
                        title = "Daily Usage",
                        value = "2h 15m",
                        subtitle = "Average",
                        color = Color(0xFF3B82F6),
                        trend = "+12% vs last week"
                    )
                }
                item {
                    UsageMetricCard(
                        icon = Icons.Default.Search,
                        title = "Scans Performed",
                        value = "47",
                        subtitle = "This week",
                        color = Color(0xFF10B981),
                        trend = "+8 vs last week"
                    )
                }
                item {
                    UsageMetricCard(
                        icon = Icons.Default.Devices,
                        title = "Devices Found",
                        value = "156",
                        subtitle = "Total",
                        color = Color(0xFF8B5CF6),
                        trend = "+23 this week"
                    )
                }
                item {
                    UsageMetricCard(
                        icon = Icons.Default.Security,
                        title = "Threats Blocked",
                        value = "12",
                        subtitle = "This month",
                        color = Color(0xFFEF4444),
                        trend = "-3 vs last month"
                    )
                }
            }
        }
    }
}

@Composable
fun UsageMetricCard(
    icon: ImageVector,
    title: String,
    value: String,
    subtitle: String,
    color: Color,
    trend: String
) {
    Card(
        modifier = Modifier
            .width(160.dp)
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = color,
                    modifier = Modifier.size(24.dp)
                )
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = color.copy(alpha = 0.2f)
                    )
                ) {
                    Text(
                        text = trend,
                        fontSize = 8.sp,
                        fontWeight = FontWeight.Medium,
                        color = color,
                        modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                    )
                }
            }
            
            Column {
                Text(
                    text = value,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
                Text(
                    text = title,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = subtitle,
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

@Composable
fun ScanAnalyticsSection(scanAnalytics: List<com.zilal.networkguardian.auth.model.ScanAnalytics>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Text(
                text = "🔍 Scan Analytics",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Performance insights from your network scans",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Sample scan metrics
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ScanMetric(
                    icon = Icons.Default.Speed,
                    label = "Avg Duration",
                    value = "23s",
                    color = Color(0xFF3B82F6)
                )
                ScanMetric(
                    icon = Icons.Default.CheckCircle,
                    label = "Success Rate",
                    value = "98%",
                    color = Color(0xFF10B981)
                )
                ScanMetric(
                    icon = Icons.Default.TrendingUp,
                    label = "Efficiency",
                    value = "+15%",
                    color = Color(0xFF8B5CF6)
                )
            }
        }
    }
}

@Composable
fun ScanMetric(
    icon: ImageVector,
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(color.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun BehaviorPatternsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Text(
                text = "🧠 AI Behavior Analysis",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Machine learning insights from your usage patterns",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Sample behavior patterns
            BehaviorPatternItem(
                pattern = "Peak Activity Hours",
                description = "Most network activity between 9 AM - 5 PM",
                confidence = 0.94f,
                icon = Icons.Default.Schedule,
                color = Color(0xFF3B82F6)
            )

            Spacer(modifier = Modifier.height(12.dp))

            BehaviorPatternItem(
                pattern = "Security Vigilance",
                description = "Performs security scans every 2-3 days consistently",
                confidence = 0.87f,
                icon = Icons.Default.Security,
                color = Color(0xFF10B981)
            )

            Spacer(modifier = Modifier.height(12.dp))

            BehaviorPatternItem(
                pattern = "Device Management",
                description = "Actively monitors new device connections",
                confidence = 0.76f,
                icon = Icons.Default.Devices,
                color = Color(0xFF8B5CF6)
            )
        }
    }
}

@Composable
fun BehaviorPatternItem(
    pattern: String,
    description: String,
    confidence: Float,
    icon: ImageVector,
    color: Color
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(color.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = pattern,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
        }

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = pattern,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        Card(
            colors = CardDefaults.cardColors(
                containerColor = color.copy(alpha = 0.1f)
            )
        ) {
            Text(
                text = "${(confidence * 100).toInt()}%",
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = color,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
    }
}

@Composable
fun DataPrivacySection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🔒 Privacy & Data Control",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Icon(
                    imageVector = Icons.Default.PrivacyTip,
                    contentDescription = "Privacy",
                    tint = Color(0xFF10B981),
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Card(
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF10B981).copy(alpha = 0.1f)
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Shield,
                        contentDescription = "Secure",
                        tint = Color(0xFF10B981),
                        modifier = Modifier.size(24.dp)
                    )
                    Column {
                        Text(
                            text = "Your Data is Secure",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF10B981)
                        )
                        Text(
                            text = "All analytics data is encrypted and stored locally on your device",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedButton(
                    onClick = { /* TODO: Export analytics data */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "Export",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Export Data")
                }

                OutlinedButton(
                    onClick = { /* TODO: Clear analytics data */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Clear",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Clear Data")
                }
            }
        }
    }
}
