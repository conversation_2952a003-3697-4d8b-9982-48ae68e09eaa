package com.zilal.networkguardian

import android.app.Application
import android.util.Log
import dagger.hilt.android.HiltAndroidApp

/**
 * Network Guardian Application Class
 * Initializes the application with Hilt dependency injection
 */
@HiltAndroidApp
class NetworkGuardianApplication : Application() {
    
    companion object {
        private const val TAG = "NetworkGuardianApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "Zilal Network Security Application started")
        
        // Initialize any global configurations here
        initializeApplication()
    }
    
    private fun initializeApplication() {
        // Any global initialization can be done here
        Log.d(TAG, "Application initialization completed")
    }
}
