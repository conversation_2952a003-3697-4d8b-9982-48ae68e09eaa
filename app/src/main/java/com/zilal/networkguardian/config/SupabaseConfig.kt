package com.zilal.networkguardian.config

/**
 * Supabase configuration for production deployment
 * Premium Network Guardian - Cloud Backend
 */
object SupabaseConfig {
    
    // Production Supabase Configuration
    const val SUPABASE_URL = "https://olbuwutmdhhdktkpsghp.supabase.co"
    const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sYnV3dXRtZGhoZGt0a3BzZ2hwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjAyODMsImV4cCI6MjA2NDczNjI4M30.Ii_IqcdkhEm9zdAuOiTVCAAkWIJqgxUBHgQfEGrjlXg"
    const val SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sYnV3dXRtZGhoZGt0a3BzZ2hwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTE2MDI4MywiZXhwIjoyMDY0NzM2MjgzfQ.-ighIS8vJAbut7IuJx_3mmKJ5F7zRe-sKUjnxQv8RgI"
    
    // Database Tables
    object Tables {
        const val USERS = "users"
        const val NETWORK_SCANS = "network_scans"
        const val DISCOVERED_DEVICES = "discovered_devices"
        const val SECURITY_EVENTS = "security_events"
        const val USER_ANALYTICS = "user_analytics"
        const val SUBSCRIPTION_PLANS = "subscription_plans"
        const val USER_SUBSCRIPTIONS = "user_subscriptions"
    }
    
    // API Endpoints
    object Endpoints {
        const val AUTH_SIGNUP = "/auth/v1/signup"
        const val AUTH_LOGIN = "/auth/v1/token?grant_type=password"
        const val AUTH_LOGOUT = "/auth/v1/logout"
        const val AUTH_USER = "/auth/v1/user"
        const val REST_API = "/rest/v1"
    }
    
    // Premium Features Configuration
    object Premium {
        const val MAX_DEVICES_FREE = 5
        const val MAX_DEVICES_PREMIUM = 1000
        const val MAX_SCANS_PER_DAY_FREE = 3
        const val MAX_SCANS_PER_DAY_PREMIUM = -1 // Unlimited
        const val PREMIUM_PRICE_MONTHLY = 29.99
        const val PREMIUM_PRICE_YEARLY = 299.99
    }
    
    // Feature Flags
    object Features {
        const val ENABLE_AI_ANALYTICS = true
        const val ENABLE_REAL_TIME_MONITORING = true
        const val ENABLE_VULNERABILITY_SCANNING = true
        const val ENABLE_NETWORK_MAPPING = true
        const val ENABLE_SECURITY_ALERTS = true
        const val ENABLE_DEVICE_FINGERPRINTING = true
        const val ENABLE_THREAT_INTELLIGENCE = true
    }
    
    // Security Configuration
    object Security {
        const val JWT_EXPIRY_HOURS = 24
        const val REFRESH_TOKEN_EXPIRY_DAYS = 30
        const val PASSWORD_MIN_LENGTH = 8
        const val ENABLE_2FA = true
        const val ENABLE_DEVICE_VERIFICATION = true
    }
    
    // Analytics Configuration
    object Analytics {
        const val ENABLE_USER_ANALYTICS = true
        const val ENABLE_PERFORMANCE_MONITORING = true
        const val ENABLE_CRASH_REPORTING = true
        const val DATA_RETENTION_DAYS = 90
    }
    
    // Network Configuration
    object Network {
        const val REQUEST_TIMEOUT_SECONDS = 30
        const val RETRY_ATTEMPTS = 3
        const val ENABLE_CACHING = true
        const val CACHE_DURATION_MINUTES = 15
    }
}
