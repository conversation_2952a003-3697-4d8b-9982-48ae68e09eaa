package com.zilal.networkguardian.di

import android.content.Context
import com.zilal.networkguardian.auth.dao.AuthDao
import com.zilal.networkguardian.auth.repository.AuthRepository
import com.zilal.networkguardian.auth.service.AnalyticsService
import com.zilal.networkguardian.auth.service.PasswordHasher
import com.zilal.networkguardian.auth.service.SessionManager
import com.zilal.networkguardian.database.AppDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dependency injection module for authentication components
 */
@Module
@InstallIn(SingletonComponent::class)
object AuthModule {

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getDatabase(context)
    }

    @Provides
    fun provideAuthDao(database: AppDatabase): AuthDao {
        return database.authDao()
    }

    @Provides
    @Singleton
    fun providePasswordHasher(): PasswordHasher {
        return PasswordHasher()
    }

    @Provides
    @Singleton
    fun provideSessionManager(): SessionManager {
        return SessionManager()
    }

    @Provides
    @Singleton
    fun provideAnalyticsService(
        authDao: AuthDao,
        @ApplicationContext context: Context
    ): AnalyticsService {
        return AnalyticsService(authDao, context)
    }

    // AuthRepository binding moved to AppModule for local server testing
}
