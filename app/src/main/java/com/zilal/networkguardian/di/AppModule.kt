package com.zilal.networkguardian.di

import android.content.Context
import com.zilal.networkguardian.api.LocalApiClient
import com.zilal.networkguardian.api.SupabaseClient
import com.zilal.networkguardian.analytics.SimpleAnalyticsManager
import com.zilal.networkguardian.analytics.FreeAnalyticsDashboard
import com.zilal.networkguardian.auth.repository.IAuthRepository
import com.zilal.networkguardian.auth.repository.LocalServerAuthRepository
import com.zilal.networkguardian.auth.repository.SupabaseAuthRepository
import com.zilal.networkguardian.network.FreeNetworkScanner
import com.zilal.networkguardian.network.WiFiAnalyzer
import com.zilal.networkguardian.network.BluetoothScanner
import com.zilal.networkguardian.network.NetworkSpeedTester
import com.zilal.networkguardian.network.NetworkTools
import com.zilal.networkguardian.network.ComprehensiveNetworkScanner
import com.zilal.networkguardian.network.TrafficMonitor
import com.zilal.networkguardian.security.VulnerabilityScanner
import com.zilal.networkguardian.storage.DataStorageManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger/Hilt module for providing application-level dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    /**
     * Provides the application context
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext context: Context): Context {
        return context
    }

    /**
     * Provides the Supabase client for production deployment
     */
    @Provides
    @Singleton
    fun provideSupabaseClient(@ApplicationContext context: Context): SupabaseClient {
        return SupabaseClient(context)
    }

    /**
     * Provides the local API client for development testing
     */
    @Provides
    @Singleton
    fun provideLocalApiClient(@ApplicationContext context: Context): LocalApiClient {
        return LocalApiClient(context)
    }

    /**
     * Provides the authentication repository - PRODUCTION DEPLOYMENT
     */
    @Provides
    @Singleton
    fun provideAuthRepository(
        supabaseClient: SupabaseClient,
        @ApplicationContext context: Context
    ): IAuthRepository {
        // 🚀 PRODUCTION: Using Supabase for cloud backend
        return SupabaseAuthRepository(
            supabaseClient = supabaseClient,
            context = context
        )

        // 🔧 DEVELOPMENT: Uncomment below for local server testing
        // return LocalServerAuthRepository(localApiClient)
    }

    /**
     * Provides simple analytics manager for user behavior tracking
     */
    @Provides
    @Singleton
    fun provideSimpleAnalyticsManager(
        @ApplicationContext context: Context
    ): SimpleAnalyticsManager {
        return SimpleAnalyticsManager(context)
    }

    /**
     * Provides free analytics dashboard for live monitoring - NO SUBSCRIPTION REQUIRED
     */
    @Provides
    @Singleton
    fun provideFreeAnalyticsDashboard(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): FreeAnalyticsDashboard {
        return FreeAnalyticsDashboard(context, analyticsManager)
    }

    /**
     * Provides free network scanner for actual network discovery - NO SUBSCRIPTION REQUIRED
     */
    @Provides
    @Singleton
    fun provideFreeNetworkScanner(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): FreeNetworkScanner {
        return FreeNetworkScanner(context, analyticsManager)
    }

    /**
     * Provides WiFi analyzer for comprehensive WiFi network analysis - FREE
     */
    @Provides
    @Singleton
    fun provideWiFiAnalyzer(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): WiFiAnalyzer {
        return WiFiAnalyzer(context, analyticsManager)
    }

    /**
     * Provides Bluetooth scanner for device discovery - FREE
     */
    @Provides
    @Singleton
    fun provideBluetoothScanner(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): BluetoothScanner {
        return BluetoothScanner(context, analyticsManager)
    }

    /**
     * Provides network speed tester for internet speed testing - FREE
     */
    @Provides
    @Singleton
    fun provideNetworkSpeedTester(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): NetworkSpeedTester {
        return NetworkSpeedTester(context, analyticsManager)
    }

    /**
     * Provides network tools for ping, traceroute, and DNS analysis
     */
    @Provides
    @Singleton
    fun provideNetworkTools(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): NetworkTools {
        return NetworkTools(context, analyticsManager)
    }

    /**
     * Provides comprehensive network scanner with real hardware integration
     */
    @Provides
    @Singleton
    fun provideComprehensiveNetworkScanner(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): ComprehensiveNetworkScanner {
        return ComprehensiveNetworkScanner(context, analyticsManager)
    }

    /**
     * Provides traffic monitor for real-time network traffic analysis
     */
    @Provides
    @Singleton
    fun provideTrafficMonitor(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): TrafficMonitor {
        return TrafficMonitor(context, analyticsManager)
    }

    /**
     * Provides vulnerability scanner for comprehensive security analysis
     */
    @Provides
    @Singleton
    fun provideVulnerabilityScanner(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): VulnerabilityScanner {
        return VulnerabilityScanner(context, analyticsManager)
    }

    /**
     * Provides data storage manager for cloud and local storage
     */
    @Provides
    @Singleton
    fun provideDataStorageManager(
        @ApplicationContext context: Context,
        analyticsManager: SimpleAnalyticsManager
    ): DataStorageManager {
        return DataStorageManager(context, analyticsManager)
    }
}
