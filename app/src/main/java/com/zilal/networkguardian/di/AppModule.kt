package com.zilal.networkguardian.di

import android.content.Context
import com.zilal.networkguardian.api.LocalApiClient
import com.zilal.networkguardian.auth.repository.IAuthRepository
import com.zilal.networkguardian.auth.repository.LocalServerAuthRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger/Hilt module for providing application-level dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    /**
     * Provides the application context
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext context: Context): Context {
        return context
    }

    /**
     * Provides the local API client for development testing
     */
    @Provides
    @Singleton
    fun provideLocalApiClient(@ApplicationContext context: Context): LocalApiClient {
        return LocalApiClient(context)
    }

    /**
     * Provides the authentication repository using local server
     */
    @Provides
    @Singleton
    fun provideAuthRepository(localApiClient: LocalApiClient): IAuthRepository {
        // For testing, we'll use the local server repository
        // In production, this would be the regular AuthRepository
        return LocalServerAuthRepository(localApiClient)
    }
}
