package com.zilal.networkguardian.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.zilal.networkguardian.model.NetworkDevice
import com.zilal.networkguardian.model.ScanResult
import com.zilal.networkguardian.model.Vulnerability
import com.zilal.networkguardian.network.NetworkScanner
import com.zilal.networkguardian.util.MacVendorLookup
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * ViewModel for network scanning and device discovery
 */
class NetworkScanViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "NetworkScanViewModel"
    }

    private val _devices = MutableStateFlow<List<NetworkDevice>>(emptyList())
    val devices: StateFlow<List<NetworkDevice>> = _devices.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()

    private val _scanResults = MutableStateFlow<ScanResult?>(null)
    val scanResults: StateFlow<ScanResult?> = _scanResults.asStateFlow()

    private val _recentScans = MutableStateFlow<List<ScanResult>>(emptyList())
    val recentScans: StateFlow<List<ScanResult>> = _recentScans.asStateFlow()

    private val networkScanner = NetworkScanner(application)
    private val macVendorLookup = MacVendorLookup.getInstance(application)

    /**
     * Start a network scan to discover devices
     */
    suspend fun startNetworkScan() {
        if (_isScanning.value) {
            return
        }

        _isScanning.value = true

        try {
            // Use the existing NetworkScanner to scan the network
            networkScanner.scanLocalNetwork().collect { scanResult ->
                _scanResults.value = scanResult
                if (scanResult.scanStatus == ScanResult.ScanStatus.COMPLETED) {
                    _devices.value = scanResult.devices
                    _isScanning.value = false

                    // Add to recent scans
                    val currentScans = _recentScans.value.toMutableList()
                    currentScans.add(0, scanResult)
                    if (currentScans.size > 10) {
                        currentScans.removeAt(currentScans.size - 1)
                    }
                    _recentScans.value = currentScans
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning network: ${e.message}")
            _isScanning.value = false
        }
    }

    /**
     * Initialize the ViewModel by starting a scan
     */
    init {
        viewModelScope.launch {
            startNetworkScan()
        }
    }

    /**
     * Get a specific device by IP address
     */
    fun getDeviceByIp(ipAddress: String): NetworkDevice? {
        return _devices.value.find { it.ipAddress == ipAddress }
    }

    /**
     * Get all vulnerabilities across all devices
     */
    fun getAllVulnerabilities(): List<Vulnerability> {
        return _devices.value.flatMap { it.vulnerabilities }
    }

    /**
     * Get count of devices by type
     */
    fun getDeviceCountByType(): Map<NetworkDevice.DeviceType, Int> {
        return _devices.value
            .groupBy { it.deviceType ?: NetworkDevice.DeviceType.UNKNOWN }
            .mapValues { it.value.size }
    }

    /**
     * Start a quick scan
     */
    fun startQuickScan() {
        viewModelScope.launch {
            startNetworkScan()
        }
    }

    /**
     * Start device discovery scan
     */
    fun startDeviceDiscovery() {
        viewModelScope.launch {
            startNetworkScan()
        }
    }

    /**
     * Start vulnerability scan
     */
    fun startVulnerabilityScan() {
        viewModelScope.launch {
            startNetworkScan()
        }
    }

    /**
     * Start port scan
     */
    fun startPortScan() {
        viewModelScope.launch {
            startNetworkScan()
        }
    }

    /**
     * Start security audit
     */
    fun startSecurityAudit() {
        viewModelScope.launch {
            startNetworkScan()
        }
    }
}
