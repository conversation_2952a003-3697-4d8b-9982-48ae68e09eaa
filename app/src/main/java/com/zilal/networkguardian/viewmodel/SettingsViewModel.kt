package com.zilal.networkguardian.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.zilal.networkguardian.model.Settings
import com.zilal.networkguardian.util.SettingsManager
import com.zilal.networkguardian.worker.WorkManagerHelper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * ViewModel for settings
 */
class SettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsManager = SettingsManager.getInstance(application)
    private val _settings = MutableStateFlow<Settings?>(null)
    val settings: StateFlow<Settings?> = _settings.asStateFlow()

    private val _isDarkTheme = MutableStateFlow(false)
    val isDarkTheme: StateFlow<Boolean> = _isDarkTheme.asStateFlow()

    init {
        loadSettings()
    }

    /**
     * Load settings from SettingsManager
     */
    private fun loadSettings() {
        viewModelScope.launch {
            _settings.value = settingsManager.settings.first()
        }
    }

    /**
     * Update background scanning setting
     */
    fun updateBackgroundScanning(enabled: Boolean) {
        updateSetting { it.copy(backgroundScanningEnabled = enabled) }

        // Update WorkManager scheduling
        if (enabled) {
            val scanInterval = _settings.value?.scanInterval ?: Settings.ScanInterval.NORMAL
            val scanMode = when (scanInterval) {
                Settings.ScanInterval.FREQUENT -> WorkManagerHelper.ScanMode.AGGRESSIVE
                Settings.ScanInterval.NORMAL -> WorkManagerHelper.ScanMode.DEFAULT
                Settings.ScanInterval.REDUCED, Settings.ScanInterval.MINIMAL -> WorkManagerHelper.ScanMode.CONSERVATIVE
                Settings.ScanInterval.MANUAL -> null
            }

            if (scanMode != null) {
                WorkManagerHelper.schedulePeriodicNetworkScans(
                    context = getApplication(),
                    scanMode = scanMode
                )
            } else {
                WorkManagerHelper.cancelPeriodicNetworkScans(getApplication())
            }
        } else {
            WorkManagerHelper.cancelPeriodicNetworkScans(getApplication())
        }
    }

    /**
     * Update scan interval setting
     */
    fun updateScanInterval(interval: Settings.ScanInterval) {
        updateSetting { it.copy(scanInterval = interval) }

        // Update WorkManager scheduling if background scanning is enabled
        if (_settings.value?.backgroundScanningEnabled == true) {
            val scanMode = when (interval) {
                Settings.ScanInterval.FREQUENT -> WorkManagerHelper.ScanMode.AGGRESSIVE
                Settings.ScanInterval.NORMAL -> WorkManagerHelper.ScanMode.DEFAULT
                Settings.ScanInterval.REDUCED, Settings.ScanInterval.MINIMAL -> WorkManagerHelper.ScanMode.CONSERVATIVE
                Settings.ScanInterval.MANUAL -> null
            }

            if (scanMode != null) {
                WorkManagerHelper.schedulePeriodicNetworkScans(
                    context = getApplication(),
                    scanMode = scanMode
                )
            } else {
                WorkManagerHelper.cancelPeriodicNetworkScans(getApplication())
            }
        }
    }

    /**
     * Update scan ports setting
     */
    fun updateScanPorts(enabled: Boolean) {
        updateSetting { it.copy(scanPortsEnabled = enabled) }
    }

    /**
     * Update scan vulnerabilities setting
     */
    fun updateScanVulnerabilities(enabled: Boolean) {
        updateSetting { it.copy(scanVulnerabilitiesEnabled = enabled) }
    }

    /**
     * Update thorough scan setting
     */
    fun updateThoroughScan(enabled: Boolean) {
        updateSetting { it.copy(thoroughScanEnabled = enabled) }
    }

    /**
     * Update notifications setting
     */
    fun updateNotifications(enabled: Boolean) {
        updateSetting { it.copy(notificationsEnabled = enabled) }
    }

    /**
     * Update new device notifications setting
     */
    fun updateNewDeviceNotifications(enabled: Boolean) {
        updateSetting { it.copy(newDeviceNotificationsEnabled = enabled) }
    }

    /**
     * Update vulnerability notifications setting
     */
    fun updateVulnerabilityNotifications(enabled: Boolean) {
        updateSetting { it.copy(vulnerabilityNotificationsEnabled = enabled) }
    }

    /**
     * Update offline device notifications setting
     */
    fun updateOfflineDeviceNotifications(enabled: Boolean) {
        updateSetting { it.copy(offlineDeviceNotificationsEnabled = enabled) }
    }

    /**
     * Update collect anonymous statistics setting
     */
    fun updateCollectAnonymousStatistics(enabled: Boolean) {
        updateSetting { it.copy(collectAnonymousStatistics = enabled) }
    }

    /**
     * Update save device history setting
     */
    fun updateSaveDeviceHistory(enabled: Boolean) {
        updateSetting { it.copy(saveDeviceHistory = enabled) }
    }

    /**
     * Update save vulnerability history setting
     */
    fun updateSaveVulnerabilityHistory(enabled: Boolean) {
        updateSetting { it.copy(saveVulnerabilityHistory = enabled) }
    }

    /**
     * Update save traffic analysis data setting
     */
    fun updateSaveTrafficAnalysisData(enabled: Boolean) {
        updateSetting { it.copy(saveTrafficAnalysisData = enabled) }
    }

    /**
     * Update continuous monitoring setting
     */
    fun updateContinuousMonitoring(enabled: Boolean) {
        updateSetting { it.copy(continuousMonitoringEnabled = enabled) }

        // Start or stop the monitoring service based on the setting
        if (enabled) {
            // Start continuous monitoring service
            WorkManagerHelper.startContinuousMonitoring(getApplication())
        } else {
            // Stop continuous monitoring service
            WorkManagerHelper.stopContinuousMonitoring(getApplication())
        }
    }

    /**
     * Update auto block unauthorized devices setting
     */
    fun updateAutoBlockUnauthorizedDevices(enabled: Boolean) {
        updateSetting { it.copy(autoBlockUnauthorizedDevices = enabled) }
    }

    /**
     * Update require verification for new devices setting
     */
    fun updateRequireVerificationForNewDevices(enabled: Boolean) {
        updateSetting { it.copy(requireVerificationForNewDevices = enabled) }
    }

    /**
     * Update monitor traffic patterns setting
     */
    fun updateMonitorTrafficPatterns(enabled: Boolean) {
        updateSetting { it.copy(monitorTrafficPatterns = enabled) }
    }

    /**
     * Update security settings
     */
    fun updateSecuritySettings(
        malwareDetection: Boolean,
        phishingProtection: Boolean,
        encryptionAssessment: Boolean,
        vulnerabilityScanning: Boolean
    ) {
        updateSetting {
            it.copy(
                malwareDetectionEnabled = malwareDetection,
                phishingProtectionEnabled = phishingProtection,
                encryptionAssessmentEnabled = encryptionAssessment,
                vulnerabilityScanningEnabled = vulnerabilityScanning
            )
        }
    }

    /**
     * Update API integration settings
     * Note: API integration has been disabled in the UI
     */
    fun updateApiIntegrationSettings(
        useVirusTotal: Boolean,
        virusTotalApiKey: String,
        useWigle: Boolean,
        wigleApiKey: String,
        useAbuseIPDB: Boolean,
        abuseIPDBApiKey: String
    ) {
        // API integration has been disabled in the UI
        // This method is kept for backward compatibility
        updateSetting {
            it.copy(
                useVirusTotalApi = false,
                virusTotalApiKey = "",
                useWigleApi = false,
                wigleApiKey = "",
                useAbuseIPDBApi = false,
                abuseIPDBApiKey = ""
            )
        }
    }

    /**
     * Update additional notification settings
     */
    fun updateAdditionalNotificationSettings(
        malwareNotifications: Boolean,
        trafficAnomalyNotifications: Boolean
    ) {
        updateSetting {
            it.copy(
                malwareNotificationsEnabled = malwareNotifications,
                trafficAnomalyNotificationsEnabled = trafficAnomalyNotifications
            )
        }
    }

    /**
     * Update Docker backend settings
     */
    fun updateDockerBackendSettings(
        useDockerBackend: Boolean,
        dockerBackendUrl: String,
        useLocalDockerBackend: Boolean,
        localDockerBackendUrl: String
    ) {
        updateSetting {
            it.copy(
                useDockerBackend = useDockerBackend,
                dockerBackendUrl = dockerBackendUrl,
                useLocalDockerBackend = useLocalDockerBackend,
                localDockerBackendUrl = localDockerBackendUrl
            )
        }
    }

    /**
     * Update developer mode setting
     */
    fun updateDeveloperMode(enabled: Boolean) {
        updateSetting { it.copy(developerModeEnabled = enabled) }
    }

    /**
     * Update debug logging setting
     */
    fun updateDebugLogging(enabled: Boolean) {
        updateSetting { it.copy(debugLoggingEnabled = enabled) }
    }

    /**
     * Update custom scan timeout setting
     */
    fun updateCustomScanTimeout(timeout: Int) {
        updateSetting { it.copy(customScanTimeout = timeout) }
    }

    /**
     * Update max threads setting
     */
    fun updateMaxThreads(maxThreads: Int) {
        updateSetting { it.copy(maxThreads = maxThreads) }
    }

    /**
     * Reset all settings to default
     */
    fun resetToDefaults() {
        // Create default settings with API integration explicitly disabled
        val defaultSettings = Settings().copy(
            useVirusTotalApi = false,
            virusTotalApiKey = "",
            useWigleApi = false,
            wigleApiKey = "",
            useAbuseIPDBApi = false,
            abuseIPDBApiKey = ""
        )

        settingsManager.saveSettings(defaultSettings)
        loadSettings()

        // Update WorkManager scheduling
        WorkManagerHelper.schedulePeriodicNetworkScans(
            context = getApplication(),
            scanMode = WorkManagerHelper.ScanMode.DEFAULT
        )
    }

    /**
     * Toggle dark theme
     */
    fun toggleTheme() {
        _isDarkTheme.value = !_isDarkTheme.value
    }

    /**
     * Update a setting
     */
    private fun updateSetting(update: (Settings) -> Settings) {
        _settings.value?.let { currentSettings ->
            val newSettings = update(currentSettings)
            settingsManager.saveSettings(newSettings)
            _settings.value = newSettings
        }
    }
}
