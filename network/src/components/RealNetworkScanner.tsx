import { useState } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function RealNetworkScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [scanType, setScanType] = useState("discovery");
  const [targetRange, setTargetRange] = useState("192.168.1.0/24");
  
  const performNetworkScan = useAction(api.realNetworkScanner.performNetworkScan);
  const checkThreatIntelligence = useAction(api.realNetworkScanner.checkThreatIntelligence);
  const lookupCVE = useAction(api.realNetworkScanner.lookupCVE);
  const recentScans = useQuery(api.networkScans.getRecentScans, { limit: 5 });

  const handleScan = async () => {
    setIsScanning(true);
    try {
      const result = await performNetworkScan({
        scanType,
        targetRange: scanType === "discovery" || scanType === "port" ? targetRange : undefined,
      });
      
      toast.success(`${scanType} scan completed! Found ${result.devicesFound} devices, ${result.vulnerabilitiesFound} vulnerabilities`);
    } catch (error: any) {
      toast.error(`Scan failed: ${error.message}`);
    } finally {
      setIsScanning(false);
    }
  };

  const handleThreatCheck = async (type: string, value: string) => {
    try {
      const result = await checkThreatIntelligence({ type, value });
      if (result.positives > 0) {
        toast.error(`⚠️ Threat detected! ${result.positives}/${result.total} engines flagged this ${type}`);
      } else {
        toast.success(`✅ ${type} appears clean`);
      }
    } catch (error) {
      toast.error("Threat intelligence check failed");
    }
  };

  const handleCVELookup = async (cveId: string) => {
    try {
      const result = await lookupCVE({ cveId });
      toast.success(`CVE ${cveId} found and stored in database`);
    } catch (error) {
      toast.error("CVE lookup failed");
    }
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <QuickActionCard
          title="Device Discovery"
          description="Find all devices"
          icon="🔍"
          onClick={() => {
            setScanType("discovery");
            handleScan();
          }}
          isActive={isScanning && scanType === "discovery"}
        />
        <QuickActionCard
          title="Vulnerability Scan"
          description="Check for CVEs"
          icon="🛡️"
          onClick={() => {
            setScanType("vulnerability");
            handleScan();
          }}
          isActive={isScanning && scanType === "vulnerability"}
        />
        <QuickActionCard
          title="Port Scan"
          description="Check open ports"
          icon="🔌"
          onClick={() => {
            setScanType("port");
            handleScan();
          }}
          isActive={isScanning && scanType === "port"}
        />
        <QuickActionCard
          title="Security Audit"
          description="Full security check"
          icon="🔒"
          onClick={() => {
            setScanType("security");
            handleScan();
          }}
          isActive={isScanning && scanType === "security"}
        />
      </div>

      {/* Advanced Scan Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Advanced Network Scanner
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Scan Type
            </label>
            <select
              value={scanType}
              onChange={(e) => setScanType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="discovery">Device Discovery</option>
              <option value="vulnerability">Vulnerability Scan</option>
              <option value="port">Port Scan</option>
              <option value="security">Security Audit</option>
            </select>
          </div>
          
          {(scanType === "discovery" || scanType === "port") && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Target Range
              </label>
              <input
                type="text"
                value={targetRange}
                onChange={(e) => setTargetRange(e.target.value)}
                placeholder="192.168.1.0/24"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          )}
          
          <div className="flex items-end">
            <button
              onClick={handleScan}
              disabled={isScanning}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {isScanning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Scanning...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <span>Start Scan</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Scan Description */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 dark:text-white mb-2">
            {scanType === "discovery" && "Device Discovery Scan"}
            {scanType === "vulnerability" && "Vulnerability Assessment"}
            {scanType === "port" && "Port Scanning"}
            {scanType === "security" && "Security Audit"}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {scanType === "discovery" && "Discovers all devices on the network using ARP scanning, ping sweeps, and device fingerprinting."}
            {scanType === "vulnerability" && "Scans for known vulnerabilities using CVE database and security advisories."}
            {scanType === "port" && "Performs comprehensive port scanning to identify open services and potential security risks."}
            {scanType === "security" && "Conducts a full security audit including weak passwords, misconfigurations, and security best practices."}
          </p>
        </div>
      </div>

      {/* Threat Intelligence */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Threat Intelligence & CVE Lookup
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ThreatCheckForm
            type="ip"
            placeholder="*************"
            onCheck={handleThreatCheck}
          />
          <ThreatCheckForm
            type="domain"
            placeholder="example.com"
            onCheck={handleThreatCheck}
          />
          <ThreatCheckForm
            type="url"
            placeholder="https://example.com/path"
            onCheck={handleThreatCheck}
          />
          <ThreatCheckForm
            type="hash"
            placeholder="MD5/SHA1/SHA256 hash"
            onCheck={handleThreatCheck}
          />
          <CVELookupForm onLookup={handleCVELookup} />
        </div>
      </div>

      {/* Recent Scans */}
      {recentScans && recentScans.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Recent Scans
          </h2>
          
          <div className="space-y-3">
            {recentScans.map((scan) => (
              <div key={scan._id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white capitalize">
                    {scan.scanType} Scan
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {new Date(scan.startTime).toLocaleString()}
                  </p>
                </div>
                <div className="text-right">
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                    scan.status === "completed" 
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : scan.status === "running"
                      ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  }`}>
                    {scan.status}
                  </span>
                  {scan.status === "completed" && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 space-y-1">
                      <p>📱 {scan.results.devicesFound} devices</p>
                      <p>🛡️ {scan.results.vulnerabilitiesFound} vulnerabilities</p>
                      <p>🔌 {scan.results.openPorts} open ports</p>
                      <p>⚠️ {scan.results.securityIssues} security issues</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function QuickActionCard({ title, description, icon, onClick, isActive }: {
  title: string;
  description: string;
  icon: string;
  onClick: () => void;
  isActive: boolean;
}) {
  return (
    <button
      onClick={onClick}
      disabled={isActive}
      className={`p-4 rounded-xl border transition-all duration-200 text-left ${
        isActive
          ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
          : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md"
      }`}
    >
      <div className="text-2xl mb-2">{icon}</div>
      <h3 className="font-medium text-gray-900 dark:text-white text-sm">{title}</h3>
      <p className="text-xs text-gray-500 dark:text-gray-400">{description}</p>
      {isActive && (
        <div className="mt-2 flex items-center space-x-2">
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500"></div>
          <span className="text-xs text-blue-600 dark:text-blue-400">Running...</span>
        </div>
      )}
    </button>
  );
}

function ThreatCheckForm({ type, placeholder, onCheck }: {
  type: string;
  placeholder: string;
  onCheck: (type: string, value: string) => void;
}) {
  const [value, setValue] = useState("");
  const [isChecking, setIsChecking] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!value.trim()) return;

    setIsChecking(true);
    try {
      await onCheck(type, value.trim());
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
        Check {type}
      </label>
      <div className="flex space-x-2">
        <input
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder={placeholder}
          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        />
        <button
          type="submit"
          disabled={isChecking || !value.trim()}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors"
        >
          {isChecking ? "..." : "Check"}
        </button>
      </div>
    </form>
  );
}

function CVELookupForm({ onLookup }: {
  onLookup: (cveId: string) => void;
}) {
  const [cveId, setCveId] = useState("");
  const [isLooking, setIsLooking] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!cveId.trim()) return;

    setIsLooking(true);
    try {
      await onLookup(cveId.trim());
    } finally {
      setIsLooking(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        CVE Lookup
      </label>
      <div className="flex space-x-2">
        <input
          type="text"
          value={cveId}
          onChange={(e) => setCveId(e.target.value)}
          placeholder="CVE-2024-0001"
          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        />
        <button
          type="submit"
          disabled={isLooking || !cveId.trim()}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors"
        >
          {isLooking ? "..." : "Lookup"}
        </button>
      </div>
    </form>
  );
}
