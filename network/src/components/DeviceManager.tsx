import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { DeviceCard } from "./DeviceCard";
import { useState } from "react";
import { toast } from "sonner";

export function DeviceManager() {
  const devices = useQuery(api.devices.listDevices);
  const scanNetwork = useAction(api.devices.scanNetwork);
  const [isScanning, setIsScanning] = useState(false);

  const handleScanNetwork = async () => {
    setIsScanning(true);
    try {
      const result = await scanNetwork();
      toast.success(`Network scan complete! Found ${result.devicesFound} devices`);
    } catch (error) {
      toast.error("Network scan failed");
    } finally {
      setIsScanning(false);
    }
  };

  if (devices === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (devices.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mb-6">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No devices found</h3>
        <p className="text-gray-500 mb-6">
          No network devices have been detected yet. In a real network environment, devices would be automatically discovered.
        </p>
        <button
          onClick={handleScanNetwork}
          disabled={isScanning}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isScanning ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Scanning Network...
            </>
          ) : (
            "Scan Network"
          )}
        </button>
      </div>
    );
  }

  const onlineDevices = devices.filter(device => device.isOnline);
  const offlineDevices = devices.filter(device => !device.isOnline);
  const kidsDevices = devices.filter(device => device.isKidsDevice);
  const blockedDevices = devices.filter(device => device.isCurrentlyBlocked);

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-green-600">{onlineDevices.length}</div>
          <div className="text-sm text-gray-500">Online</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-gray-400">{offlineDevices.length}</div>
          <div className="text-sm text-gray-500">Offline</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-blue-600">{kidsDevices.length}</div>
          <div className="text-sm text-gray-500">Kids Devices</div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="text-2xl font-bold text-red-600">{blockedDevices.length}</div>
          <div className="text-sm text-gray-500">Blocked</div>
        </div>
      </div>

      {/* Device List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Connected Devices</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {devices.map((device) => (
            <DeviceCard key={device._id} device={device} />
          ))}
        </div>
      </div>
    </div>
  );
}
