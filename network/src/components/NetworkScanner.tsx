import { useState } from "react";
import { useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function NetworkScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [wifiStatus, setWifiStatus] = useState<"connected" | "disconnected" | "checking">("checking");
  const scanNetwork = useAction(api.devices.scanNetwork);

  // Simulate WiFi status check
  useState(() => {
    setTimeout(() => {
      setWifiStatus("connected");
    }, 1000);
  });

  const handleScan = async () => {
    if (wifiStatus !== "connected") {
      toast.error("Please connect to WiFi first");
      return;
    }

    setIsScanning(true);
    try {
      const result = await scanNetwork();
      toast.success(`Network scan complete! Found ${result.devicesFound} devices`);
    } catch (error) {
      toast.error("Network scan failed");
    } finally {
      setIsScanning(false);
    }
  };

  const getWifiStatusDisplay = () => {
    switch (wifiStatus) {
      case "checking":
        return {
          icon: "🔄",
          text: "Checking connection...",
          color: "text-yellow-600 dark:text-yellow-400",
        };
      case "connected":
        return {
          icon: "📶",
          text: "Connected to WiFi",
          color: "text-green-600 dark:text-green-400",
        };
      case "disconnected":
        return {
          icon: "📵",
          text: "Not connected to WiFi",
          color: "text-red-600 dark:text-red-400",
        };
    }
  };

  const wifiDisplay = getWifiStatusDisplay();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
            </svg>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Network Scanner
            </h2>
            <div className={`flex items-center space-x-2 text-sm ${wifiDisplay.color}`}>
              <span>{wifiDisplay.icon}</span>
              <span>{wifiDisplay.text}</span>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          {wifiStatus === "disconnected" && (
            <button
              onClick={() => {
                toast.info("Please enable WiFi in your device settings and connect to your network");
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Enable WiFi
            </button>
          )}
          
          <button
            onClick={handleScan}
            disabled={isScanning || wifiStatus !== "connected"}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isScanning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Scanning Network...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span>Scan Network</span>
              </>
            )}
          </button>
        </div>
      </div>

      {wifiStatus === "disconnected" && (
        <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-start space-x-3">
            <span className="text-yellow-600 dark:text-yellow-400 text-lg">⚠️</span>
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                WiFi Connection Required
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                To scan and manage network devices, please connect to your WiFi network first. 
                Go to your device settings and connect to your home network.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
