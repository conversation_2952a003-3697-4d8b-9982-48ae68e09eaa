import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { BrowsingActivity } from "./BrowsingActivity";
import type { Id } from "../../convex/_generated/dataModel";

interface Device {
  _id: Id<"devices">;
  originalName: string;
  customName?: string;
  displayName: string;
  deviceType: string;
  isKidsDevice: boolean;
  isOnline: boolean;
  lastSeen: number;
  ipAddress?: string;
  isCurrentlyBlocked?: boolean;
  internetControl?: {
    isBlocked: boolean;
    blockedUntil?: number;
    reason?: string;
  } | null;
}

interface DeviceCardProps {
  device: Device;
}

export function DeviceCard({ device }: DeviceCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(device.customName || device.originalName);
  const [showBrowsing, setShowBrowsing] = useState(false);
  const [blockDuration, setBlockDuration] = useState("60");
  const [blockReason, setBlockReason] = useState("");
  const [showBlockForm, setShowBlockForm] = useState(false);

  const updateDeviceName = useMutation(api.devices.updateDeviceName);
  const toggleKidsDevice = useMutation(api.devices.toggleKidsDevice);
  const toggleInternetAccess = useMutation(api.devices.toggleInternetAccess);

  const handleNameUpdate = async () => {
    if (newName.trim() && newName !== device.displayName) {
      try {
        await updateDeviceName({
          deviceId: device._id,
          customName: newName.trim(),
        });
        toast.success("Device name updated");
      } catch (error) {
        toast.error("Failed to update device name");
      }
    }
    setIsEditing(false);
  };

  const handleToggleKids = async () => {
    try {
      await toggleKidsDevice({ deviceId: device._id });
      toast.success(device.isKidsDevice ? "Removed from kids devices" : "Added to kids devices");
    } catch (error) {
      toast.error("Failed to update device category");
    }
  };

  const handleToggleInternet = async () => {
    if (device.isCurrentlyBlocked) {
      // Unblock immediately
      try {
        await toggleInternetAccess({ deviceId: device._id });
        toast.success("Internet access restored");
      } catch (error) {
        toast.error("Failed to restore internet access");
      }
    } else {
      // Show block form
      setShowBlockForm(true);
    }
  };

  const handleBlockSubmit = async () => {
    try {
      await toggleInternetAccess({
        deviceId: device._id,
        duration: parseInt(blockDuration),
        reason: blockReason.trim() || undefined,
      });
      toast.success("Internet access blocked");
      setShowBlockForm(false);
      setBlockReason("");
    } catch (error) {
      toast.error("Failed to block internet access");
    }
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case "smartphone":
        return "📱";
      case "laptop":
        return "💻";
      case "tablet":
        return "📱";
      case "desktop":
        return "🖥️";
      default:
        return "📱";
    }
  };

  const formatLastSeen = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return "Just now";
  };

  const getBlockedUntilText = () => {
    if (!device.internetControl?.blockedUntil) return "Indefinitely";
    const remaining = device.internetControl.blockedUntil - Date.now();
    const minutes = Math.ceil(remaining / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m remaining`;
    }
    return `${minutes}m remaining`;
  };

  return (
    <div className="bg-white rounded-lg border shadow-sm p-4 space-y-4">
      {/* Device Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getDeviceIcon(device.deviceType)}</span>
          <div>
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  className="text-sm border rounded px-2 py-1 w-32"
                  onKeyDown={(e) => e.key === "Enter" && handleNameUpdate()}
                  autoFocus
                />
                <button
                  onClick={handleNameUpdate}
                  className="text-green-600 hover:text-green-700"
                >
                  ✓
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setNewName(device.displayName);
                  }}
                  className="text-red-600 hover:text-red-700"
                >
                  ✕
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-gray-900">{device.displayName}</h3>
                <button
                  onClick={() => setIsEditing(true)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✏️
                </button>
              </div>
            )}
            <p className="text-sm text-gray-500">{device.originalName}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            device.isOnline 
              ? "bg-green-100 text-green-800" 
              : "bg-gray-100 text-gray-800"
          }`}>
            {device.isOnline ? "Online" : "Offline"}
          </span>
          {device.isKidsDevice && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Kids
            </span>
          )}
          {device.isCurrentlyBlocked && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Blocked
            </span>
          )}
        </div>
      </div>

      {/* Device Info */}
      <div className="text-sm text-gray-500 space-y-1">
        <div>Type: {device.deviceType}</div>
        {device.ipAddress && <div>IP: {device.ipAddress}</div>}
        <div>Last seen: {formatLastSeen(device.lastSeen)}</div>
        {device.isCurrentlyBlocked && device.internetControl?.reason && (
          <div>Reason: {device.internetControl.reason}</div>
        )}
        {device.isCurrentlyBlocked && (
          <div className="text-red-600 font-medium">
            Blocked {getBlockedUntilText()}
          </div>
        )}
      </div>

      {/* Block Form */}
      {showBlockForm && (
        <div className="border-t pt-4 space-y-3">
          <h4 className="font-medium text-gray-900">Block Internet Access</h4>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Duration (minutes)
            </label>
            <select
              value={blockDuration}
              onChange={(e) => setBlockDuration(e.target.value)}
              className="w-full border rounded-md px-3 py-2 text-sm"
            >
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
              <option value="120">2 hours</option>
              <option value="240">4 hours</option>
              <option value="480">8 hours</option>
              <option value="0">Indefinitely</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason (optional)
            </label>
            <input
              type="text"
              value={blockReason}
              onChange={(e) => setBlockReason(e.target.value)}
              placeholder="e.g., Bedtime, Study time"
              className="w-full border rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleBlockSubmit}
              className="flex-1 bg-red-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700"
            >
              Block Access
            </button>
            <button
              onClick={() => setShowBlockForm(false)}
              className="flex-1 bg-gray-200 text-gray-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-2 pt-2 border-t">
        <button
          onClick={handleToggleKids}
          className={`flex-1 px-3 py-2 rounded-md text-sm font-medium ${
            device.isKidsDevice
              ? "bg-blue-100 text-blue-800 hover:bg-blue-200"
              : "bg-gray-100 text-gray-800 hover:bg-gray-200"
          }`}
        >
          {device.isKidsDevice ? "Remove from Kids" : "Mark as Kids"}
        </button>
        <button
          onClick={handleToggleInternet}
          className={`flex-1 px-3 py-2 rounded-md text-sm font-medium ${
            device.isCurrentlyBlocked
              ? "bg-green-100 text-green-800 hover:bg-green-200"
              : "bg-red-100 text-red-800 hover:bg-red-200"
          }`}
        >
          {device.isCurrentlyBlocked ? "Restore Access" : "Block Internet"}
        </button>
        <button
          onClick={() => setShowBrowsing(!showBrowsing)}
          className="flex-1 px-3 py-2 rounded-md text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200"
        >
          {showBrowsing ? "Hide Activity" : "View Activity"}
        </button>
      </div>

      {/* Browsing Activity */}
      {showBrowsing && (
        <div className="border-t pt-4">
          <BrowsingActivity deviceId={device._id} />
        </div>
      )}
    </div>
  );
}
