import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { LoginScreen } from "./LoginScreen";
import { SignOutButton } from "../SignOutButton";
import { Toaster } from "sonner";
import { NetworkManager } from "./NetworkManager";
import { ThemeProvider, useTheme } from "./ThemeProvider";
import { NotificationBell } from "./NotificationBell";
import { SuperAdminDashboard } from "./SuperAdminDashboard";
import { RealNetworkScanner } from "./RealNetworkScanner";
import { useState, useEffect } from "react";

export default function MobileOptimizedApp() {
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [currentView, setCurrentView] = useState("dashboard");

  useEffect(() => {
    const handler = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    window.addEventListener('beforeinstallprompt', handler);
    return () => window.removeEventListener('beforeinstallprompt', handler);
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;
    
    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      setIsInstallable(false);
      setDeferredPrompt(null);
    }
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
        <Authenticated>
          {/* Mobile Header */}
          <MobileHeader 
            isInstallable={isInstallable}
            onInstall={handleInstall}
            currentView={currentView}
            onViewChange={setCurrentView}
          />

          {/* Main Content */}
          <main className="pb-safe">
            <Content currentView={currentView} />
          </main>
        </Authenticated>

        <Unauthenticated>
          <LoginScreen />
        </Unauthenticated>

        <Toaster 
          position="top-center"
          toastOptions={{
            className: "dark:bg-gray-800 dark:text-white dark:border-gray-700",
          }}
        />
      </div>
    </ThemeProvider>
  );
}

function MobileHeader({ isInstallable, onInstall, currentView, onViewChange }: {
  isInstallable: boolean;
  onInstall: () => void;
  currentView: string;
  onViewChange: (view: string) => void;
}) {
  const { theme, setTheme, actualTheme } = useTheme();
  const [showMenu, setShowMenu] = useState(false);

  return (
    <header className="sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="flex justify-between items-center h-16 px-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg">🛡️</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              SecureNet
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Network Security Manager
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Theme Toggle */}
          <button
            onClick={() => {
              const themes = ["light", "dark", "system"];
              const currentIndex = themes.indexOf(theme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              setTheme(nextTheme as any);
            }}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
            title={`Current: ${theme} (${actualTheme})`}
          >
            {actualTheme === "dark" ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            )}
          </button>

          {isInstallable && (
            <button
              onClick={onInstall}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              title="Install App"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>
          )}
          
          <NotificationBell />
          
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {showMenu && (
        <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-lg">
          <div className="p-4 space-y-2">
            <button
              onClick={() => {
                onViewChange("dashboard");
                setShowMenu(false);
              }}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                currentView === "dashboard"
                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              🏠 Dashboard
            </button>
            <button
              onClick={() => {
                onViewChange("scanner");
                setShowMenu(false);
              }}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                currentView === "scanner"
                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              🔍 Network Scanner
            </button>
            <button
              onClick={() => {
                onViewChange("admin");
                setShowMenu(false);
              }}
              className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                currentView === "admin"
                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              ⚙️ Admin Dashboard
            </button>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
              <SignOutButton />
            </div>
          </div>
        </div>
      )}
    </header>
  );
}

function Content({ currentView }: { currentView: string }) {
  const loggedInUser = useQuery(api.auth.loggedInUser);

  if (loggedInUser === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 py-6 max-w-7xl mx-auto">
      {currentView === "dashboard" && <NetworkManager />}
      {currentView === "scanner" && <RealNetworkScanner />}
      {currentView === "admin" && <SuperAdminDashboard />}
    </div>
  );
}
