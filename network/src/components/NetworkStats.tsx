import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function NetworkStats() {
  const stats = useQuery(api.devices.getDeviceStats);

  if (!stats) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      label: "Online",
      value: stats.online,
      total: stats.total,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      icon: "🟢",
    },
    {
      label: "Offline", 
      value: stats.offline,
      total: stats.total,
      color: "text-gray-500 dark:text-gray-400",
      bgColor: "bg-gray-50 dark:bg-gray-800",
      icon: "⚫",
    },
    {
      label: "Kids Devices",
      value: stats.kids,
      total: stats.total,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      icon: "👶",
    },
    {
      label: "Blocked",
      value: stats.blocked,
      total: stats.total,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      icon: "🚫",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {statCards.map((stat) => (
        <div
          key={stat.label}
          className={`${stat.bgColor} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">{stat.icon}</span>
            <div className={`text-2xl font-bold ${stat.color}`}>
              {stat.value}
            </div>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300">
            {stat.label}
          </div>
          {stat.total > 0 && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {Math.round((stat.value / stat.total) * 100)}% of total
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
