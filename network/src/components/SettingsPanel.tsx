import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import { useTheme } from "./ThemeProvider";

export function SettingsPanel() {
  const settings = useQuery(api.settings.getUserSettings);
  const updateSettings = useMutation(api.settings.updateUserSettings);
  const { theme, setTheme } = useTheme();
  const [isSaving, setIsSaving] = useState(false);

  const handleNotificationChange = async (key: string, value: boolean) => {
    if (!settings) return;
    
    setIsSaving(true);
    try {
      await updateSettings({
        notifications: {
          ...settings.notifications,
          [key]: value,
        },
      });
      toast.success("Notification settings updated");
    } catch (error) {
      toast.error("Failed to update settings");
    } finally {
      setIsSaving(false);
    }
  };

  const handleScanIntervalChange = async (interval: number) => {
    setIsSaving(true);
    try {
      await updateSettings({ autoScanInterval: interval });
      toast.success("Auto-scan interval updated");
    } catch (error) {
      toast.error("Failed to update settings");
    } finally {
      setIsSaving(false);
    }
  };

  const handleAIToggle = async (enabled: boolean) => {
    setIsSaving(true);
    try {
      await updateSettings({ aiAnalysisEnabled: enabled });
      toast.success(`AI analysis ${enabled ? "enabled" : "disabled"}`);
    } catch (error) {
      toast.error("Failed to update AI settings");
    } finally {
      setIsSaving(false);
    }
  };

  if (!settings) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4 w-1/3"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Theme Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <span className="mr-2">🎨</span>
          Appearance
        </h3>
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Theme
          </label>
          <div className="grid grid-cols-3 gap-3">
            {[
              { value: "light", label: "Light", icon: "☀️" },
              { value: "dark", label: "Dark", icon: "🌙" },
              { value: "system", label: "System", icon: "💻" },
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setTheme(option.value as any)}
                className={`p-3 rounded-lg border-2 transition-colors ${
                  theme === option.value
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{option.icon}</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {option.label}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <span className="mr-2">🔔</span>
          Notifications
        </h3>
        <div className="space-y-4">
          {[
            { key: "newDevices", label: "New Device Connections", description: "Get notified when new devices join your network" },
            { key: "securityAlerts", label: "Security Alerts", description: "Receive alerts about security vulnerabilities" },
            { key: "unusualActivity", label: "Unusual Activity", description: "Get notified about suspicious browsing patterns" },
            { key: "deviceBlocked", label: "Device Blocking", description: "Notifications when devices are blocked or unblocked" },
          ].map((notification) => (
            <div key={notification.key} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="font-medium text-gray-900 dark:text-white">
                  {notification.label}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {notification.description}
                </div>
              </div>
              <button
                onClick={() => handleNotificationChange(notification.key, !settings.notifications[notification.key as keyof typeof settings.notifications])}
                disabled={isSaving}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 ${
                  settings.notifications[notification.key as keyof typeof settings.notifications]
                    ? "bg-blue-600"
                    : "bg-gray-200 dark:bg-gray-700"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.notifications[notification.key as keyof typeof settings.notifications]
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Auto-Scan Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <span className="mr-2">🔄</span>
          Auto-Scan
        </h3>
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Scan Interval
          </label>
          <select
            value={settings.autoScanInterval}
            onChange={(e) => handleScanIntervalChange(parseInt(e.target.value))}
            disabled={isSaving}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <option value={1}>Every minute</option>
            <option value={5}>Every 5 minutes</option>
            <option value={15}>Every 15 minutes</option>
            <option value={30}>Every 30 minutes</option>
            <option value={60}>Every hour</option>
          </select>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            How often to automatically scan for new devices on your network.
          </p>
        </div>
      </div>

      {/* AI Analysis Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <span className="mr-2">🤖</span>
          AI Analysis
        </h3>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="font-medium text-gray-900 dark:text-white">
              Enable AI-Powered Analysis
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Use AI to analyze browsing patterns and detect unusual activity
            </div>
          </div>
          <button
            onClick={() => handleAIToggle(!settings.aiAnalysisEnabled)}
            disabled={isSaving}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 ${
              settings.aiAnalysisEnabled
                ? "bg-blue-600"
                : "bg-gray-200 dark:bg-gray-700"
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.aiAnalysisEnabled
                  ? "translate-x-6"
                  : "translate-x-1"
              }`}
            />
          </button>
        </div>
      </div>
    </div>
  );
}
