import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import type { Id } from "../../convex/_generated/dataModel";

interface BrowsingActivityProps {
  deviceId: Id<"devices">;
}

export function BrowsingActivity({ deviceId }: BrowsingActivityProps) {
  const activities = useQuery(api.browsing.getBrowsingActivity, { deviceId, limit: 20 });
  const addDemoBrowsingData = useMutation(api.browsing.addDemoBrowsingData);
  const [isAddingDemo, setIsAddingDemo] = useState(false);

  const handleAddDemoData = async () => {
    setIsAddingDemo(true);
    try {
      await addDemoBrowsingData({ deviceId });
      toast.success("Demo browsing data added");
    } catch (error) {
      toast.error("Failed to add demo data");
    } finally {
      setIsAddingDemo(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getDomainColor = (domain: string) => {
    const colors = [
      "bg-blue-100 text-blue-800",
      "bg-green-100 text-green-800", 
      "bg-purple-100 text-purple-800",
      "bg-yellow-100 text-yellow-800",
      "bg-pink-100 text-pink-800",
      "bg-indigo-100 text-indigo-800",
    ];
    
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
      hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  if (activities === undefined) {
    return (
      <div className="flex justify-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-6">
        <div className="text-gray-400 mb-2">📊</div>
        <h4 className="font-medium text-gray-900 mb-2">No browsing activity</h4>
        <p className="text-sm text-gray-500 mb-4">
          No recent browsing activity found for this device.
        </p>
        <button
          onClick={handleAddDemoData}
          disabled={isAddingDemo}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 disabled:opacity-50"
        >
          {isAddingDemo ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              Adding Demo Data...
            </>
          ) : (
            "Add Demo Data"
          )}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">Recent Browsing Activity</h4>
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {activities.map((activity) => (
          <div key={activity._id} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDomainColor(activity.domain)}`}>
                  {activity.domain}
                </span>
                {activity.duration && (
                  <span className="text-xs text-gray-500">
                    {formatDuration(activity.duration)}
                  </span>
                )}
              </div>
              <div className="text-sm text-gray-900 truncate">
                {activity.title || activity.url}
              </div>
              <div className="text-xs text-gray-500">
                {formatTime(activity.timestamp)}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {activities.length >= 20 && (
        <div className="text-center">
          <span className="text-sm text-gray-500">Showing latest 20 activities</span>
        </div>
      )}
    </div>
  );
}
