import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";

export function SecurityDashboard() {
  const [isScanning, setIsScanning] = useState(false);
  const security = useQuery(api.security.getSecurityStatus);
  const scanSecurity = useAction(api.security.scanNetworkSecurity);

  const handleSecurityScan = async () => {
    setIsScanning(true);
    try {
      const result = await scanSecurity();
      toast.success(`Security scan complete! Found ${result.vulnerabilities} issues. Score: ${result.score}/100`);
    } catch (error) {
      toast.error("Security scan failed");
    } finally {
      setIsScanning(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400";
    if (score >= 60) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return "bg-green-50 dark:bg-green-900/20";
    if (score >= 60) return "bg-yellow-50 dark:bg-yellow-900/20";
    return "bg-red-50 dark:bg-red-900/20";
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "low":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="space-y-6">
      {/* Security Score */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={`${security ? getScoreBg(security.overallScore) : "bg-gray-50 dark:bg-gray-800"} rounded-lg p-6 border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Security Score
            </h3>
            <span className="text-2xl">🛡️</span>
          </div>
          {security ? (
            <div className="space-y-2">
              <div className={`text-3xl font-bold ${getScoreColor(security.overallScore)}`}>
                {security.overallScore}/100
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Last scanned: {new Date(security.lastScanned).toLocaleDateString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Protocol: {security.securityProtocol}
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-3xl font-bold text-gray-400">--</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                No security scan performed yet
              </div>
            </div>
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Security Scan
            </h3>
            <span className="text-2xl">🔍</span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Scan your network for security vulnerabilities and get recommendations.
          </p>
          <button
            onClick={handleSecurityScan}
            disabled={isScanning}
            className="w-full px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            {isScanning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Scanning...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Run Security Scan</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Vulnerabilities */}
      {security && security.vulnerabilities.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Security Issues ({security.vulnerabilities.length})
            </h3>
          </div>
          <div className="p-6 space-y-4">
            {security.vulnerabilities.map((vuln, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {vuln.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </h4>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(vuln.severity)}`}>
                    {vuln.severity.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  {vuln.description}
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                  <div className="flex items-start space-x-2">
                    <span className="text-blue-600 dark:text-blue-400 text-sm">💡</span>
                    <div>
                      <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                        Recommendation
                      </h5>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {vuln.recommendation}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Issues */}
      {security && security.vulnerabilities.length === 0 && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <span className="text-green-600 dark:text-green-400 text-2xl">✅</span>
            <div>
              <h3 className="font-medium text-green-800 dark:text-green-200 mb-1">
                No Security Issues Found
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                Your network security looks good! Regular scans are recommended to maintain security.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
