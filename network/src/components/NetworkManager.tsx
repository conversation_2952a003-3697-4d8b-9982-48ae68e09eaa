import { useState } from "react";
import { DeviceGrid } from "./DeviceGrid";
import { NetworkStats } from "./NetworkStats";
import { SecurityDashboard } from "./SecurityDashboard";
import { NetworkScanner } from "./NetworkScanner";
import { SettingsPanel } from "./SettingsPanel";

type Tab = "devices" | "security" | "settings";

export function NetworkManager() {
  const [activeTab, setActiveTab] = useState<Tab>("devices");

  const tabs = [
    { id: "devices" as const, label: "Devices", icon: "📱" },
    { id: "security" as const, label: "Security", icon: "🛡️" },
    { id: "settings" as const, label: "Settings", icon: "⚙️" },
  ];

  return (
    <div className="space-y-6">
      {/* Network Stats */}
      <NetworkStats />

      {/* Network Scanner */}
      <NetworkScanner />

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === "devices" && <DeviceGrid />}
          {activeTab === "security" && <SecurityDashboard />}
          {activeTab === "settings" && <SettingsPanel />}
        </div>
      </div>
    </div>
  );
}
