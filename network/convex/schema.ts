import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  devices: defineTable({
    userId: v.id("users"),
    macAddress: v.string(),
    originalName: v.string(),
    customName: v.optional(v.string()),
    deviceType: v.string(),
    manufacturer: v.optional(v.string()),
    isKidsDevice: v.boolean(),
    isOnline: v.boolean(),
    lastSeen: v.number(),
    ipAddress: v.optional(v.string()),
    signalStrength: v.optional(v.number()),
    connectionType: v.optional(v.string()),
    firstSeen: v.optional(v.number()),
    operatingSystem: v.optional(v.string()),
    openPorts: v.optional(v.array(v.number())),
    vulnerabilities: v.optional(v.array(v.object({
      cveId: v.string(),
      severity: v.string(),
      description: v.string(),
      solution: v.string(),
    }))),
    riskScore: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_mac", ["macAddress"])
    .index("by_user_online", ["userId", "isOnline"])
    .index("by_risk_score", ["riskScore"]),

  networkScans: defineTable({
    userId: v.id("users"),
    scanType: v.string(), // "discovery", "vulnerability", "port", "security"
    status: v.string(), // "running", "completed", "failed"
    startTime: v.number(),
    endTime: v.optional(v.number()),
    results: v.object({
      devicesFound: v.number(),
      vulnerabilitiesFound: v.number(),
      openPorts: v.number(),
      securityIssues: v.number(),
    }),
    details: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_user_status", ["userId", "status"]),

  vulnerabilities: defineTable({
    cveId: v.string(),
    severity: v.string(),
    score: v.number(),
    description: v.string(),
    solution: v.string(),
    publishedDate: v.number(),
    lastModified: v.number(),
    affectedProducts: v.array(v.string()),
    references: v.array(v.string()),
  })
    .index("by_cve", ["cveId"])
    .index("by_severity", ["severity"]),

  threatIntelligence: defineTable({
    type: v.string(), // "ip", "domain", "hash", "url"
    value: v.string(),
    source: v.string(), // "virustotal", "alienvault", "custom"
    threatType: v.string(),
    confidence: v.number(),
    lastChecked: v.number(),
    details: v.object({
      malicious: v.boolean(),
      reputation: v.number(),
      categories: v.array(v.string()),
    }),
  })
    .index("by_type_value", ["type", "value"])
    .index("by_source", ["source"]),

  networkTraffic: defineTable({
    userId: v.id("users"),
    deviceId: v.id("devices"),
    timestamp: v.number(),
    sourceIp: v.string(),
    destinationIp: v.string(),
    protocol: v.string(),
    port: v.number(),
    bytes: v.number(),
    packets: v.number(),
    suspicious: v.boolean(),
    blocked: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_device", ["deviceId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_suspicious", ["suspicious"]),

  adminUsers: defineTable({
    userId: v.id("users"),
    role: v.string(), // "super_admin", "admin", "analyst"
    permissions: v.array(v.string()),
    createdBy: v.id("users"),
    createdAt: v.number(),
    lastLogin: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_role", ["role"]),

  auditLogs: defineTable({
    userId: v.id("users"),
    action: v.string(),
    resource: v.string(),
    resourceId: v.optional(v.string()),
    details: v.object({
      ip: v.optional(v.string()),
      userAgent: v.optional(v.string()),
      changes: v.optional(v.object({})),
    }),
    timestamp: v.number(),
    severity: v.string(),
  })
    .index("by_user", ["userId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_action", ["action"]),

  systemMetrics: defineTable({
    timestamp: v.number(),
    metrics: v.object({
      totalUsers: v.number(),
      activeUsers: v.number(),
      totalDevices: v.number(),
      onlineDevices: v.number(),
      vulnerabilities: v.number(),
      threats: v.number(),
      networkTraffic: v.number(),
    }),
  })
    .index("by_timestamp", ["timestamp"]),

  internetControls: defineTable({
    deviceId: v.id("devices"),
    userId: v.id("users"),
    isBlocked: v.boolean(),
    blockedUntil: v.optional(v.number()),
    reason: v.optional(v.string()),
    blockedAt: v.optional(v.number()),
  })
    .index("by_device", ["deviceId"])
    .index("by_user", ["userId"]),

  browsingActivity: defineTable({
    deviceId: v.id("devices"),
    userId: v.id("users"),
    url: v.string(),
    domain: v.string(),
    title: v.optional(v.string()),
    timestamp: v.number(),
    duration: v.optional(v.number()),
    category: v.optional(v.string()),
    isBlocked: v.optional(v.boolean()),
    threatScore: v.optional(v.number()),
  })
    .index("by_device", ["deviceId"])
    .index("by_device_time", ["deviceId", "timestamp"])
    .index("by_user", ["userId"])
    .index("by_user_time", ["userId", "timestamp"]),

  networkSecurity: defineTable({
    userId: v.id("users"),
    routerIp: v.string(),
    securityProtocol: v.string(),
    vulnerabilities: v.array(v.object({
      type: v.string(),
      severity: v.string(),
      description: v.string(),
      recommendation: v.string(),
    })),
    lastScanned: v.number(),
    overallScore: v.number(),
  })
    .index("by_user", ["userId"]),

  notifications: defineTable({
    userId: v.id("users"),
    type: v.string(),
    title: v.string(),
    message: v.string(),
    deviceId: v.optional(v.id("devices")),
    isRead: v.boolean(),
    timestamp: v.number(),
    severity: v.string(),
    actionRequired: v.optional(v.boolean()),
  })
    .index("by_user", ["userId"])
    .index("by_user_unread", ["userId", "isRead"]),

  userSettings: defineTable({
    userId: v.id("users"),
    theme: v.string(),
    notifications: v.object({
      newDevices: v.boolean(),
      securityAlerts: v.boolean(),
      unusualActivity: v.boolean(),
      deviceBlocked: v.boolean(),
    }),
    autoScanInterval: v.number(),
    aiAnalysisEnabled: v.boolean(),
  })
    .index("by_user", ["userId"]),

  aiAnalysis: defineTable({
    userId: v.id("users"),
    deviceId: v.optional(v.id("devices")),
    analysisType: v.string(),
    findings: v.array(v.object({
      type: v.string(),
      confidence: v.number(),
      description: v.string(),
      recommendation: v.optional(v.string()),
    })),
    timestamp: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_device", ["deviceId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
