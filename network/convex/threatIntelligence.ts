import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const storeThreatData = mutation({
  args: {
    type: v.string(),
    value: v.string(),
    source: v.string(),
    data: v.object({}),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("threatIntelligence")
      .withIndex("by_type_value", (q) => q.eq("type", args.type).eq("value", args.value))
      .first();

    const threatData = {
      type: args.type,
      value: args.value,
      source: args.source,
      threatType: (args.data as any).threat_type || "unknown",
      confidence: (args.data as any).confidence || 0,
      lastChecked: Date.now(),
      details: {
        malicious: (args.data as any).positives > 0,
        reputation: (args.data as any).reputation || 0,
        categories: (args.data as any).categories || [],
      },
    };

    if (existing) {
      await ctx.db.patch(existing._id, threatData);
    } else {
      await ctx.db.insert("threatIntelligence", threatData);
    }
  },
});

export const getThreatIntelligence = query({
  args: {
    type: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    if (args.type) {
      const type = args.type;
      return await ctx.db
        .query("threatIntelligence")
        .withIndex("by_source", (q) => q.eq("source", type))
        .order("desc")
        .take(args.limit || 100);
    }

    return await ctx.db
      .query("threatIntelligence")
      .order("desc")
      .take(args.limit || 100);
  },
});
