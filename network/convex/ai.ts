"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";
import OpenAI from "openai";

const openai = new OpenAI({
  baseURL: process.env.CONVEX_OPENAI_BASE_URL,
  apiKey: process.env.CONVEX_OPENAI_API_KEY,
});

export const analyzeBehavior = action({
  args: {
    deviceId: v.id("devices"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get device info and browsing activity
    const device = await ctx.runQuery(api.devices.listDevices);
    const targetDevice = device.find((d: any) => d._id === args.deviceId);
    
    if (!targetDevice) {
      throw new Error("Device not found");
    }

    const activities = await ctx.runQuery(api.browsing.getBrowsingActivity, {
      deviceId: args.deviceId,
      limit: 100,
    });

    // Analyze browsing patterns
    const domains = activities.map((a: any) => a.domain);
    const domainCounts = domains.reduce((acc: Record<string, number>, domain: string) => {
      acc[domain] = (acc[domain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalTime = activities.reduce((sum: number, a: any) => sum + (a.duration || 0), 0);
    const avgSessionTime = totalTime / activities.length || 0;

    // Get AI analysis
    const prompt = `Analyze this device's browsing behavior:
Device: ${targetDevice.displayName} (${targetDevice.deviceType})
Is Kids Device: ${targetDevice.isKidsDevice}
Top domains: ${Object.entries(domainCounts).slice(0, 10).map(([d, c]) => `${d}: ${c} visits`).join(', ')}
Average session time: ${Math.round(avgSessionTime / 60)} minutes
Total activities: ${activities.length}

Provide insights about:
1. Usage patterns (normal/concerning)
2. Content appropriateness (if kids device)
3. Potential security risks
4. Recommendations

Respond in JSON format with findings array containing type, confidence (0-1), description, and recommendation.`;

    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
      });

      const content = response.choices[0].message.content;
      if (!content) {
        throw new Error("No AI response");
      }

      const analysis = JSON.parse(content);
      
      // Store analysis
      await ctx.runMutation(api.aiQueries.storeAnalysis, {
        userId,
        deviceId: args.deviceId,
        analysisType: "behavior",
        findings: analysis.findings,
      });

      // Create notifications for high-confidence concerning findings
      for (const finding of analysis.findings) {
        if (finding.confidence > 0.7 && finding.type.includes("concern")) {
          await ctx.runMutation(api.notifications.createNotification, {
            userId,
            type: "unusual_activity",
            title: "Unusual Activity Detected",
            message: finding.description,
            deviceId: args.deviceId,
            severity: "warning",
          });
        }
      }

      return analysis;
    } catch (error) {
      console.error("AI analysis failed:", error);
      // Return fallback analysis
      return {
        findings: [
          {
            type: "analysis_unavailable",
            confidence: 1,
            description: "AI analysis is temporarily unavailable",
            recommendation: "Manual review recommended",
          },
        ],
      };
    }
  },
});




