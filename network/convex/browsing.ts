import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getBrowsingActivity = query({
  args: {
    deviceId: v.id("devices"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    const activities = await ctx.db
      .query("browsingActivity")
      .withIndex("by_device_time", (q) => q.eq("deviceId", args.deviceId))
      .order("desc")
      .take(args.limit || 50);

    return activities;
  },
});

export const addBrowsingActivity = mutation({
  args: {
    deviceId: v.id("devices"),
    url: v.string(),
    domain: v.string(),
    title: v.optional(v.string()),
    duration: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    await ctx.db.insert("browsingActivity", {
      deviceId: args.deviceId,
      userId,
      url: args.url,
      domain: args.domain,
      title: args.title,
      timestamp: Date.now(),
      duration: args.duration,
    });
  },
});

// Add demo browsing data
export const addDemoBrowsingData = mutation({
  args: {
    deviceId: v.id("devices"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const demoSites = [
      { domain: "youtube.com", url: "https://youtube.com/watch?v=abc123", title: "Funny Cat Videos", duration: 1200 },
      { domain: "google.com", url: "https://google.com/search?q=homework+help", title: "Google Search", duration: 300 },
      { domain: "wikipedia.org", url: "https://en.wikipedia.org/wiki/Solar_System", title: "Solar System - Wikipedia", duration: 600 },
      { domain: "netflix.com", url: "https://netflix.com/watch/12345", title: "Stranger Things", duration: 2700 },
      { domain: "instagram.com", url: "https://instagram.com/explore", title: "Instagram", duration: 900 },
      { domain: "tiktok.com", url: "https://tiktok.com/@user123", title: "TikTok", duration: 1800 },
      { domain: "reddit.com", url: "https://reddit.com/r/funny", title: "r/funny - Reddit", duration: 450 },
    ];

    const now = Date.now();
    for (let i = 0; i < demoSites.length; i++) {
      const site = demoSites[i];
      await ctx.db.insert("browsingActivity", {
        deviceId: args.deviceId,
        userId,
        url: site.url,
        domain: site.domain,
        title: site.title,
        timestamp: now - (i * 30 * 60 * 1000), // 30 minutes apart
        duration: site.duration,
      });
    }
  },
});
