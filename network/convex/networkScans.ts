import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const createScan = mutation({
  args: {
    userId: v.id("users"),
    scanType: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("networkScans", {
      userId: args.userId,
      scanType: args.scanType,
      status: "running",
      startTime: Date.now(),
      results: {
        devicesFound: 0,
        vulnerabilitiesFound: 0,
        openPorts: 0,
        securityIssues: 0,
      },
    });
  },
});

export const updateScanResults = mutation({
  args: {
    scanId: v.id("networkScans"),
    results: v.object({
      devicesFound: v.number(),
      vulnerabilitiesFound: v.number(),
      openPorts: v.number(),
      securityIssues: v.number(),
      error: v.optional(v.string()),
    }),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.scanId, {
      status: args.status,
      endTime: Date.now(),
      results: args.results,
    });
  },
});

export const getRecentScans = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("networkScans")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(args.limit || 10);
  },
});
