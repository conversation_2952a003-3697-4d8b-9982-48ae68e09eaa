import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getSecurityStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const security = await ctx.db
      .query("networkSecurity")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return security;
  },
});

export const scanNetworkSecurity = action({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Simulate security scan
    const vulnerabilities = [
      {
        type: "weak_password",
        severity: "high" as const,
        description: "Router admin password is weak or default",
        recommendation: "Change to a strong, unique password with at least 12 characters",
      },
      {
        type: "outdated_firmware",
        severity: "medium" as const,
        description: "Router firmware is outdated",
        recommendation: "Update to the latest firmware version",
      },
      {
        type: "wps_enabled",
        severity: "medium" as const,
        description: "WPS (Wi-Fi Protected Setup) is enabled",
        recommendation: "Disable WPS to prevent brute force attacks",
      },
    ];

    const overallScore = Math.max(0, 100 - (vulnerabilities.length * 15));

    await ctx.runMutation(api.security.updateSecurityStatus, {
      userId,
      routerIp: "***********",
      securityProtocol: "WPA2",
      vulnerabilities,
      overallScore,
    });

    // Create security alert notification
    if (vulnerabilities.some(v => v.severity === "high")) {
      await ctx.runMutation(api.notifications.createNotification, {
        userId,
        type: "security_alert",
        title: "Security Issues Detected",
        message: `Found ${vulnerabilities.length} security issues that need attention`,
        severity: "error",
      });
    }

    return { vulnerabilities: vulnerabilities.length, score: overallScore };
  },
});

export const updateSecurityStatus = mutation({
  args: {
    userId: v.id("users"),
    routerIp: v.string(),
    securityProtocol: v.string(),
    vulnerabilities: v.array(v.object({
      type: v.string(),
      severity: v.string(),
      description: v.string(),
      recommendation: v.string(),
    })),
    overallScore: v.number(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("networkSecurity")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        routerIp: args.routerIp,
        securityProtocol: args.securityProtocol,
        vulnerabilities: args.vulnerabilities,
        lastScanned: Date.now(),
        overallScore: args.overallScore,
      });
    } else {
      await ctx.db.insert("networkSecurity", {
        userId: args.userId,
        routerIp: args.routerIp,
        securityProtocol: args.securityProtocol,
        vulnerabilities: args.vulnerabilities,
        lastScanned: Date.now(),
        overallScore: args.overallScore,
      });
    }
  },
});

import { api } from "./_generated/api";
