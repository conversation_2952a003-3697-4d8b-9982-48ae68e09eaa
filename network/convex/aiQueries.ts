import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getAnalysis = query({
  args: {
    deviceId: v.optional(v.id("devices")),
    analysisType: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    let analyses;

    if (args.deviceId) {
      analyses = await ctx.db
        .query("aiAnalysis")
        .withIndex("by_device", (q) => q.eq("deviceId", args.deviceId))
        .order("desc")
        .take(args.limit || 10);
    } else {
      analyses = await ctx.db
        .query("aiAnalysis")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .order("desc")
        .take(args.limit || 10);
    }

    return analyses.filter(a => !args.analysisType || a.analysisType === args.analysisType);
  },
});

export const storeAnalysis = mutation({
  args: {
    userId: v.id("users"),
    deviceId: v.optional(v.id("devices")),
    analysisType: v.string(),
    findings: v.array(v.object({
      type: v.string(),
      confidence: v.number(),
      description: v.string(),
      recommendation: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("aiAnalysis", {
      userId: args.userId,
      deviceId: args.deviceId,
      analysisType: args.analysisType,
      findings: args.findings,
      timestamp: Date.now(),
    });
  },
});
