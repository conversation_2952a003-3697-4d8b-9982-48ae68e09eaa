/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as adminDashboard from "../adminDashboard.js";
import type * as ai from "../ai.js";
import type * as aiQueries from "../aiQueries.js";
import type * as auth from "../auth.js";
import type * as browsing from "../browsing.js";
import type * as devices from "../devices.js";
import type * as http from "../http.js";
import type * as networkScans from "../networkScans.js";
import type * as notifications from "../notifications.js";
import type * as realNetworkScanner from "../realNetworkScanner.js";
import type * as router from "../router.js";
import type * as security from "../security.js";
import type * as settings from "../settings.js";
import type * as threatIntelligence from "../threatIntelligence.js";
import type * as vulnerabilities from "../vulnerabilities.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  adminDashboard: typeof adminDashboard;
  ai: typeof ai;
  aiQueries: typeof aiQueries;
  auth: typeof auth;
  browsing: typeof browsing;
  devices: typeof devices;
  http: typeof http;
  networkScans: typeof networkScans;
  notifications: typeof notifications;
  realNetworkScanner: typeof realNetworkScanner;
  router: typeof router;
  security: typeof security;
  settings: typeof settings;
  threatIntelligence: typeof threatIntelligence;
  vulnerabilities: typeof vulnerabilities;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
