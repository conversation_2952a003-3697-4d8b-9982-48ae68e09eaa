import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const storeCVE = mutation({
  args: {
    cveId: v.string(),
    severity: v.string(),
    score: v.number(),
    description: v.string(),
    publishedDate: v.number(),
    lastModified: v.number(),
    references: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("vulnerabilities")
      .withIndex("by_cve", (q) => q.eq("cveId", args.cveId))
      .first();

    const cveData = {
      cveId: args.cveId,
      severity: args.severity,
      score: args.score,
      description: args.description,
      solution: "Apply vendor security patches",
      publishedDate: args.publishedDate,
      lastModified: args.lastModified,
      affectedProducts: [],
      references: args.references,
    };

    if (existing) {
      await ctx.db.patch(existing._id, cveData);
    } else {
      await ctx.db.insert("vulnerabilities", cveData);
    }
  },
});

export const getCVEs = query({
  args: {
    severity: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    if (args.severity) {
      const severity = args.severity;
      return await ctx.db
        .query("vulnerabilities")
        .withIndex("by_severity", (q) => q.eq("severity", severity))
        .order("desc")
        .take(args.limit || 50);
    }

    return await ctx.db
      .query("vulnerabilities")
      .order("desc")
      .take(args.limit || 50);
  },
});
