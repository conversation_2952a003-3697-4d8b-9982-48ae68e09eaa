import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getSystemMetrics = query({
  args: {
    timeRange: v.optional(v.string()), // "1h", "24h", "7d", "30d"
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user is admin
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser) {
      throw new Error("Unauthorized - Admin access required");
    }

    const timeRange = args.timeRange || "24h";
    const now = Date.now();
    let startTime;

    switch (timeRange) {
      case "1h":
        startTime = now - (60 * 60 * 1000);
        break;
      case "24h":
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case "7d":
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = now - (24 * 60 * 60 * 1000);
    }

    // Get all users
    const allUsers = await ctx.db.query("users").collect();
    const totalUsers = allUsers.length;

    // Get active users (logged in within timeRange)
    const activeUsers = allUsers.filter(user => 
      user._creationTime && (now - user._creationTime) < (now - startTime)
    ).length;

    // Get all devices
    const allDevices = await ctx.db.query("devices").collect();
    const totalDevices = allDevices.length;
    const onlineDevices = allDevices.filter(d => d.isOnline).length;

    // Get vulnerabilities
    const vulnerabilities = await ctx.db.query("vulnerabilities").collect();
    const totalVulnerabilities = vulnerabilities.length;

    // Get threats
    const threats = await ctx.db.query("threatIntelligence").collect();
    const totalThreats = threats.filter(t => t.details.malicious).length;

    // Get network traffic
    const networkTraffic = await ctx.db
      .query("networkTraffic")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", startTime))
      .collect();
    const totalTraffic = networkTraffic.reduce((sum, t) => sum + t.bytes, 0);

    return {
      totalUsers,
      activeUsers,
      totalDevices,
      onlineDevices,
      vulnerabilities: totalVulnerabilities,
      threats: totalThreats,
      networkTraffic: totalTraffic,
      timeRange,
    };
  },
});

export const getAllUsers = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check admin permissions
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser || !adminUser.permissions.includes("view_users")) {
      throw new Error("Unauthorized");
    }

    const users = await ctx.db
      .query("users")
      .order("desc")
      .take(args.limit || 50);

    // Get device counts for each user
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const devices = await ctx.db
          .query("devices")
          .withIndex("by_user", (q) => q.eq("userId", user._id))
          .collect();

        const notifications = await ctx.db
          .query("notifications")
          .withIndex("by_user_unread", (q) => q.eq("userId", user._id).eq("isRead", false))
          .collect();

        return {
          ...user,
          deviceCount: devices.length,
          onlineDevices: devices.filter(d => d.isOnline).length,
          unreadNotifications: notifications.length,
        };
      })
    );

    return usersWithStats;
  },
});

export const getAuditLogs = query({
  args: {
    userId: v.optional(v.id("users")),
    action: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check admin permissions
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser || !adminUser.permissions.includes("view_audit_logs")) {
      throw new Error("Unauthorized");
    }

    if (args.userId) {
      const userId = args.userId;
      return await ctx.db
        .query("auditLogs")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .order("desc")
        .take(args.limit || 100);
    } else if (args.action) {
      const action = args.action;
      return await ctx.db
        .query("auditLogs")
        .withIndex("by_action", (q) => q.eq("action", action))
        .order("desc")
        .take(args.limit || 100);
    }

    return await ctx.db
      .query("auditLogs")
      .order("desc")
      .take(args.limit || 100);


  },
});

export const createAdminUser = mutation({
  args: {
    targetUserId: v.id("users"),
    role: v.string(),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if current user is super admin
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser || adminUser.role !== "super_admin") {
      throw new Error("Unauthorized - Super admin access required");
    }

    // Check if target user is already admin
    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", args.targetUserId))
      .first();

    if (existingAdmin) {
      throw new Error("User is already an admin");
    }

    await ctx.db.insert("adminUsers", {
      userId: args.targetUserId,
      role: args.role,
      permissions: args.permissions,
      createdBy: userId,
      createdAt: Date.now(),
    });

    // Log the action
    await ctx.db.insert("auditLogs", {
      userId,
      action: "create_admin_user",
      resource: "admin_users",
      resourceId: args.targetUserId,
      details: {
        changes: { role: args.role, permissions: args.permissions },
      },
      timestamp: Date.now(),
      severity: "high",
    });
  },
});

export const sendBulkNotification = mutation({
  args: {
    userIds: v.array(v.id("users")),
    title: v.string(),
    message: v.string(),
    severity: v.string(),
    type: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check admin permissions
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser || !adminUser.permissions.includes("send_notifications")) {
      throw new Error("Unauthorized");
    }

    // Send notification to each user
    for (const targetUserId of args.userIds) {
      await ctx.db.insert("notifications", {
        userId: targetUserId,
        type: args.type,
        title: args.title,
        message: args.message,
        isRead: false,
        timestamp: Date.now(),
        severity: args.severity,
        actionRequired: true,
      });
    }

    // Log the action
    await ctx.db.insert("auditLogs", {
      userId,
      action: "send_bulk_notification",
      resource: "notifications",
      details: {
        changes: { userCount: args.userIds.length, title: args.title },
      },
      timestamp: Date.now(),
      severity: "medium",
    });
  },
});
