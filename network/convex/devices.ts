import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api, internal } from "./_generated/api";

export const listDevices = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const devices = await ctx.db
      .query("devices")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    // Get internet control status for each device
    const devicesWithControls = await Promise.all(
      devices.map(async (device) => {
        const control = await ctx.db
          .query("internetControls")
          .withIndex("by_device", (q) => q.eq("deviceId", device._id))
          .first();

        const isCurrentlyBlocked = control?.isBlocked && 
          (!control.blockedUntil || control.blockedUntil > Date.now());

        return {
          ...device,
          displayName: device.customName || device.originalName,
          internetControl: control,
          isCurrentlyBlocked,
        };
      })
    );

    return devicesWithControls.sort((a, b) => {
      // Sort by online status first, then by last seen
      if (a.isOnline !== b.isOnline) {
        return a.isOnline ? -1 : 1;
      }
      return b.lastSeen - a.lastSeen;
    });
  },
});

export const addOrUpdateDevice = mutation({
  args: {
    macAddress: v.string(),
    originalName: v.string(),
    deviceType: v.string(),
    manufacturer: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    signalStrength: v.optional(v.number()),
    connectionType: v.optional(v.string()),
    operatingSystem: v.optional(v.string()),
    openPorts: v.optional(v.array(v.number())),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if device already exists
    const existingDevice = await ctx.db
      .query("devices")
      .withIndex("by_mac", (q) => q.eq("macAddress", args.macAddress))
      .first();

    if (existingDevice) {
      // Update existing device
      await ctx.db.patch(existingDevice._id, {
        isOnline: true,
        lastSeen: Date.now(),
        ipAddress: args.ipAddress,
        signalStrength: args.signalStrength,
        connectionType: args.connectionType,
        operatingSystem: args.operatingSystem,
        openPorts: args.openPorts,
      });
      return existingDevice._id;
    }

    // Create new device
    const deviceId = await ctx.db.insert("devices", {
      userId: args.userId,
      macAddress: args.macAddress,
      originalName: args.originalName,
      deviceType: args.deviceType,
      manufacturer: args.manufacturer,
      isKidsDevice: false,
      isOnline: true,
      lastSeen: Date.now(),
      firstSeen: Date.now(),
      ipAddress: args.ipAddress,
      signalStrength: args.signalStrength,
      connectionType: args.connectionType,
      operatingSystem: args.operatingSystem,
      openPorts: args.openPorts,
      riskScore: 0,
    });

    // Create default internet control
    await ctx.db.insert("internetControls", {
      deviceId,
      userId: args.userId,
      isBlocked: false,
    });

    // Create notification for new device
    await ctx.db.insert("notifications", {
      userId: args.userId,
      type: "new_device",
      title: "New Device Connected",
      message: `${args.originalName} has connected to your network`,
      deviceId,
      isRead: false,
      timestamp: Date.now(),
      severity: "info",
    });

    return deviceId;
  },
});

export const getDeviceStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const devices = await ctx.db
      .query("devices")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const controls = await ctx.db
      .query("internetControls")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const onlineDevices = devices.filter(d => d.isOnline);
    const kidsDevices = devices.filter(d => d.isKidsDevice);
    const blockedDevices = controls.filter(c => 
      c.isBlocked && (!c.blockedUntil || c.blockedUntil > Date.now())
    );

    return {
      total: devices.length,
      online: onlineDevices.length,
      offline: devices.length - onlineDevices.length,
      kids: kidsDevices.length,
      blocked: blockedDevices.length,
    };
  },
});

export const updateDeviceName = mutation({
  args: {
    deviceId: v.id("devices"),
    customName: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    await ctx.db.patch(args.deviceId, {
      customName: args.customName,
    });
  },
});

export const toggleKidsDevice = mutation({
  args: {
    deviceId: v.id("devices"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    await ctx.db.patch(args.deviceId, {
      isKidsDevice: !device.isKidsDevice,
    });
  },
});

export const toggleInternetAccess = mutation({
  args: {
    deviceId: v.id("devices"),
    duration: v.optional(v.number()), // in minutes
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    const control = await ctx.db
      .query("internetControls")
      .withIndex("by_device", (q) => q.eq("deviceId", args.deviceId))
      .first();

    if (!control) {
      throw new Error("Internet control not found");
    }

    const isCurrentlyBlocked = control.isBlocked && 
      (!control.blockedUntil || control.blockedUntil > Date.now());

    if (isCurrentlyBlocked) {
      // Unblock the device
      await ctx.db.patch(control._id, {
        isBlocked: false,
        blockedUntil: undefined,
        reason: undefined,
      });

      // Create notification
      await ctx.db.insert("notifications", {
        userId,
        type: "device_blocked",
        title: "Internet Access Restored",
        message: `${device.customName || device.originalName} can now access the internet`,
        deviceId: args.deviceId,
        isRead: false,
        timestamp: Date.now(),
        severity: "info",
      });
    } else {
      // Block the device
      const blockedUntil = args.duration 
        ? Date.now() + (args.duration * 60 * 1000)
        : undefined;

      await ctx.db.patch(control._id, {
        isBlocked: true,
        blockedUntil,
        reason: args.reason,
        blockedAt: Date.now(),
      });

      // Create notification
      await ctx.db.insert("notifications", {
        userId,
        type: "device_blocked",
        title: "Internet Access Blocked",
        message: `${device.customName || device.originalName} has been blocked from accessing the internet`,
        deviceId: args.deviceId,
        isRead: false,
        timestamp: Date.now(),
        severity: "warning",
      });
    }
  },
});

// Network scanning simulation
export const scanNetwork = action({
  args: {},
  handler: async (ctx): Promise<any> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Call the real network scanner
    return await ctx.runAction(api.realNetworkScanner.performNetworkScan, {
      scanType: "discovery",
      targetRange: "192.168.1.0/24",
    });
  },
});

export const updateDeviceStatus = mutation({
  args: {
    deviceId: v.id("devices"),
    isOnline: v.boolean(),
    signalStrength: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const device = await ctx.db.get(args.deviceId);
    if (!device || device.userId !== userId) {
      throw new Error("Device not found or unauthorized");
    }

    await ctx.db.patch(args.deviceId, {
      isOnline: args.isOnline,
      lastSeen: Date.now(),
      signalStrength: args.signalStrength,
    });
  },
});
