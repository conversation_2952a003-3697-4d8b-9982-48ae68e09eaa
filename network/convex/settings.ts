import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getUserSettings = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const settings = await ctx.db
      .query("userSettings")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    // Return default settings if none exist
    return settings || {
      theme: "system",
      notifications: {
        newDevices: true,
        securityAlerts: true,
        unusualActivity: true,
        deviceBlocked: true,
      },
      autoScanInterval: 5, // 5 minutes
      aiAnalysisEnabled: true,
    };
  },
});

export const updateUserSettings = mutation({
  args: {
    theme: v.optional(v.string()),
    notifications: v.optional(v.object({
      newDevices: v.boolean(),
      securityAlerts: v.boolean(),
      unusualActivity: v.boolean(),
      deviceBlocked: v.boolean(),
    })),
    autoScanInterval: v.optional(v.number()),
    aiAnalysisEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const existing = await ctx.db
      .query("userSettings")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    const updateData = Object.fromEntries(
      Object.entries(args).filter(([_, value]) => value !== undefined)
    );

    if (existing) {
      await ctx.db.patch(existing._id, updateData);
    } else {
      await ctx.db.insert("userSettings", {
        userId,
        theme: "system",
        notifications: {
          newDevices: true,
          securityAlerts: true,
          unusualActivity: true,
          deviceBlocked: true,
        },
        autoScanInterval: 5,
        aiAnalysisEnabled: true,
        ...updateData,
      });
    }
  },
});
